<?php
// Method 1: Direct WCFM trigger (works even if already sent)
function trigger_wcfm_store_email_direct($order_id) {
    if (!class_exists('WCFMmp_Email_Store_new_order')) {
        error_log("WCFM Store email class not found");
        return false;
    }
    
    $order = wc_get_order($order_id);
    if (!$order) {
        error_log("Order #{$order_id} not found");
        return false;
    }
    
    // Reset the email triggered flag to allow re-sending
    $order->delete_meta_data('_wcfmmp_order_email_triggered');
    $order->save();
    
    $store_email = new WCFMmp_Email_Store_new_order();
    $store_email->trigger($order_id);
    error_log("WCFM store email triggered directly for order #{$order_id}");
    return true;
}

// Method 2: Using WooCommerce mailer (alternative approach)
function trigger_store_email_via_mailer($order_id) {
    $order = wc_get_order($order_id);
    if (!$order) {
        error_log("Order #{$order_id} not found");
        return false;
    }
    
    // Reset email flags
    $order->delete_meta_data('_wcfmmp_order_email_triggered');
    $order->save();
    
    $mailer = WC()->mailer();
    $emails = $mailer->get_emails();
    
    if (isset($emails['WCFMmp_Email_Store_new_order'])) {
        $store_email = $emails['WCFMmp_Email_Store_new_order'];
        $store_email->trigger($order_id);
        error_log("Store email triggered via mailer for order #{$order_id}");
        return true;
    }
    
    error_log("Store email class not found in mailer");
    return false;
}

// Method 3: Force trigger using existing functions
function send_store_new_order_email_manually($order_id, $vendor_id = null) {
    if (!$order_id) {
        error_log("Order ID is required");
        return false;
    }

    $order = wc_get_order($order_id);
    if (!$order) {
        error_log("Order {$order_id} not found");
        return false;
    }

    // Reset email flags to allow re-sending
    $order->delete_meta_data('_wcfmmp_order_email_triggered');
    $order->save();

    // Get WooCommerce mailer
    $mailer = WC()->mailer();
    $emails = $mailer->get_emails();

    // Trigger store new order email manually
    if (isset($emails['store-new-order'])) {
        $store_email = $emails['store-new-order'];
        if ($store_email->is_enabled()) {
            error_log("Triggering store new order email manually");
            $store_email->trigger($order_id);
            return true;
        } else {
            error_log("Store new order email is disabled");
        }
    } else {
        error_log("Store new order email not found in mailer");

        // Try to create and trigger manually
        if (class_exists('WCFMmp_Email_Store_new_order')) {
            $store_email = new WCFMmp_Email_Store_new_order();
            if ($store_email) {
                error_log("Created store email instance manually");
                $store_email->trigger($order_id);
                return true;
            }
        }
    }
    return false;
}

// =============================================================================
// CALLING METHODS - Choose one of these methods to trigger the email
// =============================================================================

// OPTION 1: Simple one-time call (runs once when you visit admin)
add_action('admin_init', 'trigger_store_email_once');
function trigger_store_email_once() {
    static $called = false;
    if (!$called && current_user_can('manage_options')) {
        $order_id = 12345; // Replace with your actual order ID
        
        // Choose which method to use:
        $result = trigger_store_email_via_mailer($order_id);
        // OR: $result = trigger_wcfm_store_email_direct($order_id);
        // OR: $result = send_store_new_order_email_manually($order_id);
        
        if ($result) {
            error_log("SUCCESS: Store email triggered for order #{$order_id}");
        } else {
            error_log("FAILED: Could not trigger store email for order #{$order_id}");
        }
        $called = true;
    }
}

// OPTION 2: URL parameter trigger (visit: yoursite.com/wp-admin/?trigger_store_email=12345)
/*
add_action('admin_init', 'trigger_store_email_by_url');
function trigger_store_email_by_url() {
    if (isset($_GET['trigger_store_email']) && current_user_can('manage_options')) {
        $order_id = intval($_GET['trigger_store_email']);
        if ($order_id > 0) {
            $result = trigger_store_email_via_mailer($order_id);
            if ($result) {
                wp_die("✅ Store email triggered successfully for order #{$order_id}");
            } else {
                wp_die("❌ Failed to trigger store email for order #{$order_id}");
            }
        }
    }
}
*/

// OPTION 3: Trigger with safety check to avoid duplicates
/*
add_action('init', 'safe_trigger_store_email');
function safe_trigger_store_email() {
    if (is_admin() && current_user_can('manage_options')) {
        $order_id = 12345; // Replace with your order ID
        
        // Check if already triggered manually today
        $today_key = "manual_store_email_" . date('Y-m-d') . "_{$order_id}";
        $already_sent_today = get_option($today_key, false);
        
        if (!$already_sent_today) {
            $result = trigger_store_email_via_mailer($order_id);
            if ($result) {
                update_option($today_key, true);
                error_log("Store email triggered for order #{$order_id} on " . date('Y-m-d H:i:s'));
            }
        }
    }
}
*/

// OPTION 4: Multiple orders at once
/*
add_action('admin_init', 'trigger_multiple_store_emails');
function trigger_multiple_store_emails() {
    static $called = false;
    if (!$called && current_user_can('manage_options')) {
        $order_ids = [12345, 12346, 12347]; // Replace with your order IDs
        
        foreach ($order_ids as $order_id) {
            $result = trigger_store_email_via_mailer($order_id);
            if ($result) {
                error_log("SUCCESS: Store email triggered for order #{$order_id}");
            } else {
                error_log("FAILED: Store email for order #{$order_id}");
            }
        }
        $called = true;
    }
}
*/

// =============================================================================
// UTILITY FUNCTIONS
// =============================================================================

// Check email status for an order
function check_store_email_status($order_id) {
    $order = wc_get_order($order_id);
    if ($order) {
        $email_triggered = $order->get_meta('_wcfmmp_order_email_triggered');
        error_log("Order #{$order_id} email status: " . ($email_triggered ? 'Sent' : 'Not sent'));
        return $email_triggered;
    }
    return false;
}

// Display email status in admin (optional)
add_action('admin_notices', 'show_email_status_notice');
function show_email_status_notice() {
    if (current_user_can('manage_options') && isset($_GET['page'])) {
        $order_id = 12345; // Replace with your order ID
        $status = check_store_email_status($order_id);
        echo '<div class="notice notice-info"><p>Order #' . $order_id . ' store email status: ' . ($status ? '✅ Sent' : '❌ Not sent') . '</p></div>';
    }
}

// Reset email flag for testing
function reset_store_email_flag($order_id) {
    $order = wc_get_order($order_id);
    if ($order) {
        $order->delete_meta_data('_wcfmmp_order_email_triggered');
        $order->save();
        error_log("Reset email flag for order #{$order_id}");
        return true;
    }
    return false;
}
?>