<?php

namespace Moip\Tests\Resource;

use Moip\Tests\TestCase;

/**
 * Description of AccountTest.
 */
class AccountTest extends TestCase
{
    public function testShouldCreateAccount()
    {
        $this->mockHttpSession($this->body_moip_account_create);

        $account = $this->moip->accounts()
            ->setName('For tests')
            ->setLastName('Mine Customer Company')
            ->setEmail('<EMAIL>')
            ->setIdentityDocument('*********', 'SSP', '2017-10-25')
            ->setBirthDate('1990-01-01')
            ->setTaxDocument('***********')
            ->setType('MERCHANT')
            ->setPhone(11, *********, 55)
            ->addAlternativePhone(11, *********, 55)
            ->setTransparentAccount(false)
            ->setBusinessSegment(5)
            ->addAddress('Av. Brigadeiro Far<PERSON> Lima', 2927, 'Itaim', 'São Paulo', 'SP', '********', 'Apt. X', 'BRA')
            ->create();

        $this->assertNotEmpty($account->getId());
        $this->assertNotEmpty($account->getAccessToken());
        $this->assertNotEmpty($account->getchannelId());
        $this->assertNotEmpty($account->getCreatedAt());
        $this->assertEquals('*********', $account->getIdentityDocumentNumber());
        $this->assertEquals('SSP', $account->getIdentityDocumentIssuer());
        $this->assertEquals('2017-10-25', $account->getIdentityDocumentIssueDate());
        $this->assertEquals('RG', $account->getIdentityDocumentType());
        $this->assertEquals('https://desenvolvedor.moip.com.br/sandbox/AskForNewPassword.do?method=confirm&email=dev.moip%40labs.moip.com.br&code=8e3b306d59907f4a47508913956c96ba', $account->getPasswordLink());
        $this->assertEquals(5, $account->getBusinessSegmentId());
        $this->assertEquals('Antiguidades / Negociante de artes / Galerias', $account->getBusinessSegmentName());
        $this->assertEquals(5971, $account->getBusinessSegmentMcc());
    }

    public function testShouldCreateAccountWithCompany()
    {
        $this->mockHttpSession($this->body_moip_account_create);

        $account = $this->moip->accounts()
            ->setName('For tests')
            ->setLastName('Mine Customer Company')
            ->setEmail('<EMAIL>')
            ->setIdentityDocument('*********', 'SSP', '2017-10-25')
            ->setBirthDate('1990-01-01')
            ->setTaxDocument('***********')
            ->setType('MERCHANT')
            ->setPhone(11, *********, 55)
            ->addAlternativePhone(11, *********, 55)
            ->setTransparentAccount(false)
            ->addAddress('Av. Brigadeiro Faria Lima', 2927, 'Itaim', 'São Paulo', 'SP', '********', 'Apt. X', 'BRA')
            ->setCompanyName('Mine Customer Company', 'Company Business')
            ->setCompanyOpeningDate('2011-01-01')
            ->setCompanyPhone(11, *********, 55)
            ->setCompanyTaxDocument('**************')
            ->setCompanyAddress('R. Company', 321, 'Bairro Company', 'São Paulo', 'SP', '********', 'Ap. Y', 'BRA')
            ->setCompanyMainActivity('82.91-1/00', 'Test')
            ->create();

        $this->assertNotEmpty($account->getId());
        $this->assertNotEmpty($account->getAccessToken());
        $this->assertNotEmpty($account->getchannelId());
        $this->assertNotEmpty($account->getCreatedAt());
        $this->assertEquals('*********', $account->getIdentityDocumentNumber());
        $this->assertEquals('SSP', $account->getIdentityDocumentIssuer());
        $this->assertEquals('RG', $account->getIdentityDocumentType());
        $this->assertEquals('*********', $account->getAlternativePhones()[0]->number);
        $this->assertEquals('Company Business', $account->getCompany()->businessName);
    }

    public function testCheckExistingAccount()
    {
        $this->mockHttpSession('', 200);
        $this->assertTrue($this->moip->accounts()->checkExistence('123.456.798-91'));
    }

    public function testCheckNonExistingAccount()
    {
        $this->mockHttpSession('', 404);
        $this->assertFalse($this->moip->accounts()->checkExistence('412.309.725-10'));
    }

    public function testShouldGetAccount()
    {
        $this->mockHttpSession($this->body_moip_account_get);
        $account_id = 'MPA-7E9B1F907512';
        $account = $this->moip->accounts()->get($account_id);

        $this->assertNotEmpty($account->getId());
        $this->assertNotEmpty($account->getCreatedAt());
        $this->assertEquals(false, $account->getTransparentAccount());
        $this->assertEquals('<EMAIL>', $account->getLogin());
        $this->assertEquals(true, $account->getEmailConfirmed());
        $this->assertEquals('<EMAIL>', $account->getEmailAddress());
        $this->assertEquals('794.663.228-26', $account->getTaxDocumentNumber());
    }
}
