<?php
/**
 * Plugin Name: ACF Taxonomy CSV Import for WooCommerce
 * Plugin URI: https://yoursite.com
 * Description: Dynamically handles ACF (Advanced Custom Fields) taxonomy imports during WooCommerce CSV import, similar to how categories work. Automatically detects ACF taxonomies and provides mapping options.
 * Version: 1.0.0
 * Author: Your Name
 * License: GPL v2 or later
 * Text Domain: acf-taxonomy-import
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * WC requires at least: 5.0
 * WC tested up to: 8.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Simple debug logging
function acf_tax_debug_log($message) {
    if (get_option('acf_tax_enable_logging', 'yes') === 'yes') {
        error_log('ACF Taxonomy Import: ' . $message);
    }
}

// Check if WooCommerce is active
function acf_tax_is_woocommerce_active() {
    return class_exists('WooCommerce') || function_exists('WC');
}

if (!acf_tax_is_woocommerce_active()) {
    acf_tax_debug_log('WooCommerce not active - plugin not loading');
    return;
}

class ACF_Taxonomy_CSV_Import {
    
    /**
     * Constructor
     */
    public function __construct() {
        acf_tax_debug_log('Plugin constructor called');
        add_action('init', array($this, 'init'));
        add_action('admin_init', array($this, 'admin_init'));
    }
    
    /**
     * Initialize the plugin
     */
    public function init() {
        acf_tax_debug_log('Plugin init called');
        
        try {
            // Add import mapping options
            add_filter('woocommerce_csv_product_import_mapping_options', array($this, 'add_taxonomy_import_columns'));
            
            // Add automatic mapping support
            add_filter('woocommerce_csv_product_import_mapping_default_columns', array($this, 'add_taxonomy_mapping_screen'));
            
            // Parse taxonomy data from CSV
            add_filter('woocommerce_product_importer_parsed_data', array($this, 'parse_taxonomy_csv_data'), 10, 2);
            
            // Set taxonomy after product import
            add_filter('woocommerce_product_import_inserted_product_object', array($this, 'set_product_taxonomies'), 10, 2);
            
            // Add export support
            add_filter('woocommerce_product_export_column_names', array($this, 'add_taxonomy_export_columns'));
            add_filter('woocommerce_product_export_product_default_columns', array($this, 'add_taxonomy_export_columns'));
            
            acf_tax_debug_log('All hooks added successfully');
            
        } catch (Exception $e) {
            acf_tax_debug_log('Error in init(): ' . $e->getMessage());
        }
    }
    
    /**
     * Admin initialization
     */
    public function admin_init() {
        acf_tax_debug_log('Admin init called');
        
        // Add admin menu
        add_action('admin_menu', array($this, 'add_admin_menu'));
        
        // Register settings
        register_setting('acf_tax_settings', 'acf_tax_enable_logging');
        register_setting('acf_tax_settings', 'acf_tax_manual_taxonomies');
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        acf_tax_debug_log('Adding admin menu');
        
        $page = add_submenu_page(
            'woocommerce',
            __('ACF Taxonomy Import', 'acf-taxonomy-import'),
            __('ACF Taxonomy Import', 'acf-taxonomy-import'),
            'manage_woocommerce',
            'acf-taxonomy-import',
            array($this, 'admin_page')
        );
        
        if ($page) {
            acf_tax_debug_log('Admin menu added successfully');
        } else {
            acf_tax_debug_log('Failed to add admin menu');
        }
    }
    
    /**
     * Get manual ACF taxonomies
     */
    public function get_manual_acf_taxonomies() {
        $manual_taxonomies = get_option('acf_tax_manual_taxonomies', 'brands,pattern_look,texture,durability,quickship,scale,category_type,application,style,format,dimension,enviroment,acoustic,colors');
        
        if (empty($manual_taxonomies)) {
            return array();
        }
        
        $taxonomy_names = array_map('trim', explode(',', $manual_taxonomies));
        $taxonomies = array();
        
        foreach ($taxonomy_names as $taxonomy_name) {
            if (!empty($taxonomy_name) && taxonomy_exists($taxonomy_name)) {
                $taxonomy_object = get_taxonomy($taxonomy_name);
                if ($taxonomy_object && is_object($taxonomy_object)) {
                    $taxonomies[$taxonomy_name] = $taxonomy_object;
                }
            }
        }
        
        return $taxonomies;
    }
    
    /**
     * Add taxonomy columns to import mapping options
     */
    public function add_taxonomy_import_columns($columns) {
        $acf_taxonomies = $this->get_manual_acf_taxonomies();
        
        foreach ($acf_taxonomies as $taxonomy_name => $taxonomy) {
            $columns[$taxonomy_name] = $taxonomy->label . ' (ACF)';
        }
        
        return $columns;
    }
    
    /**
     * Add automatic mapping support for taxonomy columns
     */
    public function add_taxonomy_mapping_screen($columns) {
        $acf_taxonomies = $this->get_manual_acf_taxonomies();
        
        foreach ($acf_taxonomies as $taxonomy_name => $taxonomy) {
            // Try different variations of column names
            $possible_names = array(
                $taxonomy->label,
                ucfirst(str_replace('_', ' ', $taxonomy_name)),
                ucwords(str_replace('_', ' ', $taxonomy_name))
            );
            
            foreach ($possible_names as $name) {
                $columns[$name] = $taxonomy_name;
            }
        }
        
        return $columns;
    }
    
    /**
     * Parse taxonomy data from CSV
     */
    public function parse_taxonomy_csv_data($parsed_data, $importer) {
        $acf_taxonomies = $this->get_manual_acf_taxonomies();
        
        foreach ($acf_taxonomies as $taxonomy_name => $taxonomy) {
            if (!empty($parsed_data[$taxonomy_name])) {
                $separator = get_option('acf_tax_separator_' . $taxonomy_name, ',');
                $data = explode($separator, $parsed_data[$taxonomy_name]);
                
                // Clean up the data
                $data = array_map('trim', $data);
                $data = array_filter($data);
                
                unset($parsed_data[$taxonomy_name]);
                
                if (is_array($data)) {
                    $parsed_data[$taxonomy_name] = array();
                    
                    foreach ($data as $term_name) {
                        if (!empty($term_name)) {
                            $parsed_data[$taxonomy_name][] = $term_name;
                        }
                    }
                }
            }
        }
        
        return $parsed_data;
    }
    
    /**
     * Set product taxonomies after import
     */
    public function set_product_taxonomies($product, $data) {
        if (!is_a($product, 'WC_Product')) {
            return $product;
        }
        
        $acf_taxonomies = $this->get_manual_acf_taxonomies();
        
        foreach ($acf_taxonomies as $taxonomy_name => $taxonomy) {
            if (!empty($data[$taxonomy_name])) {
                $term_ids = array();
                
                foreach ($data[$taxonomy_name] as $term_name) {
                    $term_id = $this->get_or_create_term($term_name, $taxonomy_name);
                    if ($term_id) {
                        $term_ids[] = $term_id;
                    }
                }
                
                // Assign terms to product
                if (!empty($term_ids)) {
                    wp_set_object_terms($product->get_id(), $term_ids, $taxonomy_name, false);
                    acf_tax_debug_log("Assigned {$taxonomy_name} terms to product {$product->get_id()}: " . implode(', ', $data[$taxonomy_name]));
                }
            }
        }
        
        return $product;
    }
    
    /**
     * Get or create taxonomy term
     */
    private function get_or_create_term($term_name, $taxonomy_name) {
        // Check if term exists
        $term = get_term_by('name', $term_name, $taxonomy_name);
        
        if (!$term) {
            // Create the term if it doesn't exist
            $term_result = wp_insert_term($term_name, $taxonomy_name, array(
                'slug' => sanitize_title($term_name)
            ));
            
            if (!is_wp_error($term_result)) {
                acf_tax_debug_log("Created {$taxonomy_name} term: {$term_name}");
                return $term_result['term_id'];
            }
            return false;
        }
        
        return $term->term_id;
    }
    
    /**
     * Add taxonomy columns to export
     */
    public function add_taxonomy_export_columns($columns) {
        $acf_taxonomies = $this->get_manual_acf_taxonomies();
        
        foreach ($acf_taxonomies as $taxonomy_name => $taxonomy) {
            $columns[$taxonomy_name] = $taxonomy->label . ' (ACF)';
        }
        
        return $columns;
    }
    
    /**
     * Admin page content
     */
    public function admin_page() {
        acf_tax_debug_log('Admin page called');
        
        if (isset($_POST['submit'])) {
            // Handle form submission
            update_option('acf_tax_enable_logging', isset($_POST['acf_tax_enable_logging']) ? 'yes' : 'no');
            update_option('acf_tax_manual_taxonomies', sanitize_text_field($_POST['acf_tax_manual_taxonomies']));
            echo '<div class="notice notice-success"><p>' . __('Settings saved!', 'acf-taxonomy-import') . '</p></div>';
        }
        
        $manual_taxonomies = get_option('acf_tax_manual_taxonomies', 'brands,pattern_look,texture,durability,quickship,scale,category_type,application,style,format,dimension,enviroment,acoustic,colors');
        $enable_logging = get_option('acf_tax_enable_logging', 'yes');
        
        ?>
        <div class="wrap">
            <h1><?php _e('ACF Taxonomy Import Settings', 'acf-taxonomy-import'); ?></h1>
            <p><?php _e('Configure import/export settings for ACF (Advanced Custom Fields) taxonomies.', 'acf-taxonomy-import'); ?></p>
            
            <form method="post" action="">
                <?php wp_nonce_field('acf_tax_settings'); ?>
                
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('Enable Logging', 'acf-taxonomy-import'); ?></th>
                        <td>
                            <input type="checkbox" name="acf_tax_enable_logging" value="yes" <?php checked($enable_logging, 'yes'); ?> />
                            <p class="description"><?php _e('Enable logging for import/export operations', 'acf-taxonomy-import'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('ACF Taxonomies', 'acf-taxonomy-import'); ?></th>
                        <td>
                            <input type="text" name="acf_tax_manual_taxonomies" value="<?php echo esc_attr($manual_taxonomies); ?>" class="regular-text" />
                            <p class="description"><?php _e('Comma-separated list of ACF taxonomy names (e.g., brands,colors,texture)', 'acf-taxonomy-import'); ?></p>
                        </td>
                    </tr>
                </table>
                
                <h2><?php _e('Current ACF Taxonomies Status', 'acf-taxonomy-import'); ?></h2>
                <?php
                $taxonomies = $this->get_manual_acf_taxonomies();
                if (!empty($taxonomies)) {
                    echo '<ul>';
                    foreach ($taxonomies as $taxonomy_name => $taxonomy) {
                        echo '<li><strong>' . esc_html($taxonomy->label) . '</strong> (' . esc_html($taxonomy_name) . ') - ✅ Found</li>';
                    }
                    echo '</ul>';
                } else {
                    echo '<p>❌ No ACF taxonomies found. Please check your taxonomy names above.</p>';
                }
                ?>
                
                <?php submit_button(); ?>
            </form>
        </div>
        <?php
    }
    
    /**
     * Plugin activation
     */
    public static function activate() {
        acf_tax_debug_log('Plugin activation started');
        
        // Set default options
        update_option('acf_tax_enable_logging', 'yes');
        update_option('acf_tax_manual_taxonomies', 'brands,pattern_look,texture,durability,quickship,scale,category_type,application,style,format,dimension,enviroment,acoustic,colors');
        
        acf_tax_debug_log('Plugin activated successfully');
    }
    
    /**
     * Plugin deactivation
     */
    public static function deactivate() {
        acf_tax_debug_log('Plugin deactivated');
    }
}

// Activation/deactivation hooks
register_activation_hook(__FILE__, array('ACF_Taxonomy_CSV_Import', 'activate'));
register_deactivation_hook(__FILE__, array('ACF_Taxonomy_CSV_Import', 'deactivate'));

// Initialize the plugin
acf_tax_debug_log('Initializing ACF Taxonomy CSV Import plugin');
new ACF_Taxonomy_CSV_Import();
