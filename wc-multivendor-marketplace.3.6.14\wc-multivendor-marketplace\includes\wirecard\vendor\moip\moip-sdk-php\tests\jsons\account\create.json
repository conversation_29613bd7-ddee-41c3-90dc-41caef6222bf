{"id": "MPA-61B1FE0940A2", "login": "<EMAIL>", "accessToken": "dbb1cee101444f7a9f74c576854b9103_v2", "channelId": "APP-QGAGZRIX1CUF", "type": "MERCHANT", "transparentAccount": false, "email": {"address": "<EMAIL>", "confirmed": false}, "person": {"name": "For tests", "lastName": "Mine Customer Company", "birthDate": "1990-01-01", "taxDocument": {"type": "CPF", "number": "028.229.218-73"}, "address": {"street": "Av. <PERSON><PERSON>", "streetNumber": "2927", "district": "Itaim", "zipcode": "********", "zipCode": "********", "city": "São Paulo", "state": "SP", "country": "BRA", "complement": "Ap. X"}, "phone": {"countryCode": "55", "areaCode": "11", "number": "*********", "verified": false, "phoneType": "not_informed"}, "nationality": "BRA", "identityDocument": {"number": "*********", "issuer": "SSP", "issueDate": "2017-10-25", "type": "RG"}, "alternativePhones": [{"countryCode": "55", "areaCode": "11", "number": "*********", "verified": false, "phoneType": "not_informed"}], "birthPlace": "São Paulo", "parentsName": {"father": "Father of Mine Customer", "mother": "Mother of Mine Customer"}}, "company": {"name": "Mine Customer Company", "businessName": "Company Business", "taxDocument": {"type": "CNPJ", "number": "64.893.609/0001-10"}, "address": {"street": "R. Company", "streetNumber": "321", "district": "Bairro Company", "zipcode": "********", "zipCode": "********", "city": "São Paulo", "state": "SP", "country": "BRA", "complement": "Ap. Y"}, "phone": {"countryCode": "55", "areaCode": "11", "number": "*********", "verified": false, "phoneType": "not_informed"}, "openingDate": "2017-10-20"}, "businessSegment": {"id": 5, "name": "Antiguidades / Negociante de artes / Galerias", "mcc": 5971}, "site": "https://www.meusite.com", "createdAt": "2017-10-25T14:05:46.050Z", "_links": {"self": {"href": "https://sandbox.moip.com.br/moipaccounts/MPA-61B1FE0940A2", "title": null}, "setPassword": {"href": "https://desenvolvedor.moip.com.br/sandbox/AskForNewPassword.do?method=confirm&email=dev.moip%40labs.moip.com.br&code=8e3b306d59907f4a47508913956c96ba"}}}