{"info": {"_postman_id": "f8a4698f-41b2-4c31-9847-10dedeb7d1ab", "name": "Zagzoog Hisense Online", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "11074965"}, "item": [{"name": "saveorder", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\"orderid\":\"123\",\r\n\"firstname\":\"first\",\r\n\"lastname\":\"last\",\r\n\"mobile\":\"0521111111\",\r\n\"email\":\"<EMAIL>\",\r\n\"shippingcity\":\"Jeddah\",\r\n\"shippingaddress\":\"test test\",\r\n \"products\":[\r\n  {\r\n    \"productid\":\"ZRF93H\",\r\n    \"quantity\":\"1\"\r\n },\r\n  {\r\n    \"productid\":\"ZWM180N\",\r\n    \"quantity\":\"2\"\r\n }]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://zagzoog.com/hisenseapi/saveorder.php", "protocol": "https", "host": ["zagzoog", "com"], "path": ["<PERSON><PERSON><PERSON><PERSON>", "saveorder.php"]}}, "response": []}, {"name": "order update webhook", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\"orderid\":\"123\",\r\n\"status\":\"Pending/Shipment Ready/Shipped/Delivered\" }", "options": {"raw": {"language": "json"}}}, "url": {"raw": "Provide webhook url", "host": ["Provide webhook url"]}}, "response": []}]}