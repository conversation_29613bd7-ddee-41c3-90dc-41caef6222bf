{"id": "PAY-DB5TBW0E0Z24", "status": "IN_ANALYSIS", "delayCapture": false, "amount": {"total": 7300, "fees": 0, "refunds": 0, "liquid": 7300, "currency": "BRL"}, "installmentCount": 1, "statementDescriptor": "minhaLoja.com", "fundingInstrument": {"creditCard": {"id": "CRC-7D197TPTPYWQ", "brand": "VISA", "first6": "401200", "last4": "1112", "store": true, "holder": {"birthdate": "1988-12-30", "birthDate": "1988-12-30", "taxDocument": {"type": "CPF", "number": "22222222222"}, "fullname": "<PERSON>"}}, "method": "CREDIT_CARD"}, "fees": [{"type": "TRANSACTION", "amount": 0}], "escrows": [{"id": "ECW-MYB3UUWHHPM9", "status": "HOLD_PENDING", "description": "teste de descricao", "amount": 7300, "createdAt": "2017-07-05T10:19:19.156-03", "updatedAt": "2017-07-05T10:19:19.156-03", "_links": {"self": {"href": "https://sandbox.moip.com.br/v2/escrows/ECW-MYB3UUWHHPM9"}, "order": {"href": "https://sandbox.moip.com.br/v2/orders/ORD-QDDLVRVO8ZTK", "title": "ORD-QDDLVRVO8ZTK"}, "payment": {"href": "https://sandbox.moip.com.br/v2/payments/PAY-DB5TBW0E0Z24", "title": "PAY-DB5TBW0E0Z24"}}}], "events": [{"type": "PAYMENT.IN_ANALYSIS", "createdAt": "2017-07-05T10:19:19.299-03"}, {"type": "PAYMENT.CREATED", "createdAt": "2017-07-05T10:19:19.125-03"}], "receivers": [{"moipAccount": {"id": "MPA-7ED9D2D0BC81", "login": "<EMAIL>", "fullname": "<PERSON> Menezes <PERSON>"}, "type": "PRIMARY", "amount": {"total": 7300, "refunds": 0}}], "_links": {"self": {"href": "https://sandbox.moip.com.br/v2/payments/PAY-DB5TBW0E0Z24"}, "order": {"href": "https://sandbox.moip.com.br/v2/orders/ORD-QDDLVRVO8ZTK", "title": "ORD-QDDLVRVO8ZTK"}}, "createdAt": "2017-07-05T10:19:19.117-03", "updatedAt": "2017-07-05T10:19:19.299-03"}