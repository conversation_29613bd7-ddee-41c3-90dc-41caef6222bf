{"id": "REF-JR2WAKM894UJ", "status": "REQUESTED", "events": [{"type": "REFUND.REQUESTED", "createdAt": "2017-08-29T09:26:30.000-03"}], "amount": {"total": 10000, "gross": 10000, "fees": 0, "currency": "BRL"}, "type": "PARTIAL", "refundingInstrument": {"bankAccount": {"bankNumber": "001", "bankName": "BANCO DO BRASIL S.A.", "agencyNumber": "4444444", "agencyCheckNumber": "2", "accountNumber": "1234", "accountCheckNumber": "1", "type": "CHECKING", "holder": {"taxDocument": {"type": "CPF", "number": "***********"}, "fullname": "<PERSON>"}}, "method": "BANK_ACCOUNT"}, "createdAt": "2017-08-29T09:26:30.000-03", "_links": {"self": {"href": "https://sandbox.moip.com.br/v2/refunds/REF-JR2WAKM894UJ"}, "order": {"href": "https://sandbox.moip.com.br/v2/orders/ORD-XOZB2LRQ9BZ3", "title": "ORD-XOZB2LRQ9BZ3"}, "payment": {"href": "https://sandbox.moip.com.br/v2/payments/PAY-E4Q0N9TK0BFW", "title": "PAY-E4Q0N9TK0BFW"}}}