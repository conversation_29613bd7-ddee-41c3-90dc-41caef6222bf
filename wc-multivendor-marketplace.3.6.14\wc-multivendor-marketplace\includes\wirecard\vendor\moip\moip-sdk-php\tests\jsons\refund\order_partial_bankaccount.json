{"id": "REF-PW9CU6JMQ9TL", "status": "REQUESTED", "events": [{"type": "REFUND.REQUESTED", "createdAt": "2017-08-28T16:41:08.000-03"}], "amount": {"total": 10000, "gross": 10000, "fees": 0, "currency": "BRL"}, "type": "PARTIAL", "refundingInstrument": {"bankAccount": {"bankNumber": "001", "bankName": "BANCO DO BRASIL S.A.", "agencyNumber": "4444444", "agencyCheckNumber": "2", "accountNumber": "1234", "accountCheckNumber": "1", "type": "CHECKING", "holder": {"taxDocument": {"type": "CPF", "number": "***********"}, "fullname": "<PERSON>"}}, "method": "BANK_ACCOUNT"}, "createdAt": "2017-08-28T16:41:08.000-03", "_links": {"self": {"href": "https://sandbox.moip.com.br/v2/refunds/REF-PW9CU6JMQ9TL"}, "order": {"href": "https://sandbox.moip.com.br/v2/orders/ORD-PW650FBY3GLU", "title": "ORD-PW650FBY3GLU"}, "payment": {"href": "https://sandbox.moip.com.br/v2/payments/PAY-1RS1LQV2QH47", "title": "PAY-1RS1LQV2QH47"}}}