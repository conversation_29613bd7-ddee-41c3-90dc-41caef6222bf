{"id": "MPY-PFE3PI6WCOSJ", "status": "CANCELLED", "amount": {"total": 8000, "gross": 8000, "currency": "BRL"}, "installmentCount": 1, "payments": [{"id": "PAY-247DYOI8PS0J", "status": "CANCELLED", "delayCapture": true, "cancellationDetails": {"code": "7", "description": "Política do Moip", "cancelledBy": "MOIP"}, "amount": {"total": 4000, "gross": 4000, "fees": 289, "refunds": 0, "liquid": 3711, "currency": "BRL"}, "installmentCount": 1, "fundingInstrument": {"creditCard": {"id": "CRC-UARTCOXFHAQI", "brand": "VISA", "first6": "401200", "last4": "1112", "store": true, "holder": {"birthdate": "1988-12-30", "birthDate": "1988-12-30", "taxDocument": {"type": "CPF", "number": "22222222222"}, "fullname": "<PERSON>"}}, "method": "CREDIT_CARD"}, "fees": [{"type": "TRANSACTION", "amount": 289}], "escrows": [{"id": "ECW-1HE2AH5DLGUI", "status": "HOLD_PENDING", "description": "<PERSON>e", "amount": 4000, "createdAt": "2017-10-02T09:59:17.000-03", "updatedAt": "2017-10-02T09:59:17.000-03", "_links": {"self": {"href": "https://sandbox.moip.com.br/v2/escrows/ECW-1HE2AH5DLGUI"}, "order": {"href": "https://sandbox.moip.com.br/v2/orders/ORD-X0D0J868YSNU", "title": "ORD-X0D0J868YSNU"}, "payment": {"href": "https://sandbox.moip.com.br/v2/payments/PAY-247DYOI8PS0J", "title": "PAY-247DYOI8PS0J"}}}], "events": [{"type": "PAYMENT.CANCELLED", "createdAt": "2017-10-02T10:08:03.603-03"}, {"type": "PAYMENT.PRE_AUTHORIZED", "createdAt": "2017-10-02T09:59:18.000-03"}, {"type": "PAYMENT.CREATED", "createdAt": "2017-10-02T09:59:17.000-03"}, {"type": "PAYMENT.IN_ANALYSIS", "createdAt": "2017-10-02T09:59:17.000-03"}, {"type": "PAYMENT.WAITING", "createdAt": "2017-10-02T09:59:17.000-03"}], "receivers": [{"moipAccount": {"id": "MPA-E3C8493A06AE", "login": "<EMAIL>", "fullname": "<PERSON>"}, "type": "PRIMARY", "amount": {"total": 3500, "refunds": 0}}, {"moipAccount": {"id": "MPA-8D5DBB4EF8B8", "login": "<EMAIL>", "fullname": "Caio <PERSON>par"}, "type": "SECONDARY", "amount": {"total": 500, "fees": 0, "refunds": 0}, "feePayor": false}], "_links": {"self": {"href": "https://sandbox.moip.com.br/v2/payments/PAY-247DYOI8PS0J"}, "order": {"href": "https://sandbox.moip.com.br/v2/orders/ORD-X0D0J868YSNU", "title": "ORD-X0D0J868YSNU"}}, "createdAt": "2017-10-02T09:59:17.000-03", "updatedAt": "2017-10-02T10:08:03.602-03"}, {"id": "PAY-AQEO6Q1N1941", "status": "CANCELLED", "delayCapture": true, "cancellationDetails": {"code": "7", "description": "Política do Moip", "cancelledBy": "MOIP"}, "amount": {"total": 4000, "gross": 4000, "fees": 289, "refunds": 0, "liquid": 3711, "currency": "BRL"}, "installmentCount": 1, "fundingInstrument": {"creditCard": {"id": "CRC-UARTCOXFHAQI", "brand": "VISA", "first6": "401200", "last4": "1112", "store": true, "holder": {"birthdate": "1988-12-30", "birthDate": "1988-12-30", "taxDocument": {"type": "CPF", "number": "22222222222"}, "fullname": "<PERSON>"}}, "method": "CREDIT_CARD"}, "fees": [{"type": "TRANSACTION", "amount": 289}], "escrows": [{"id": "ECW-5RAZGXIV4RSI", "status": "HOLD_PENDING", "description": "<PERSON>e", "amount": 4000, "createdAt": "2017-10-02T09:59:17.000-03", "updatedAt": "2017-10-02T09:59:17.000-03", "_links": {"self": {"href": "https://sandbox.moip.com.br/v2/escrows/ECW-5RAZGXIV4RSI"}, "order": {"href": "https://sandbox.moip.com.br/v2/orders/ORD-7ZCLI7U7K17B", "title": "ORD-7ZCLI7U7K17B"}, "payment": {"href": "https://sandbox.moip.com.br/v2/payments/PAY-AQEO6Q1N1941", "title": "PAY-AQEO6Q1N1941"}}}], "events": [{"type": "PAYMENT.CANCELLED", "createdAt": "2017-10-02T10:08:03.604-03"}, {"type": "PAYMENT.PRE_AUTHORIZED", "createdAt": "2017-10-02T09:59:18.000-03"}, {"type": "PAYMENT.CREATED", "createdAt": "2017-10-02T09:59:17.000-03"}, {"type": "PAYMENT.IN_ANALYSIS", "createdAt": "2017-10-02T09:59:17.000-03"}, {"type": "PAYMENT.WAITING", "createdAt": "2017-10-02T09:59:17.000-03"}], "receivers": [{"moipAccount": {"id": "MPA-8D5DBB4EF8B8", "login": "<EMAIL>", "fullname": "Caio <PERSON>par"}, "type": "PRIMARY", "amount": {"total": 4000, "refunds": 0}}], "_links": {"self": {"href": "https://sandbox.moip.com.br/v2/payments/PAY-AQEO6Q1N1941"}, "order": {"href": "https://sandbox.moip.com.br/v2/orders/ORD-7ZCLI7U7K17B", "title": "ORD-7ZCLI7U7K17B"}}, "createdAt": "2017-10-02T09:59:17.000-03", "updatedAt": "2017-10-02T10:08:03.603-03"}], "_links": {"self": {"href": "https://sandbox.moip.com.br/v2/multipayments/MPY-PFE3PI6WCOSJ"}, "multiorder": {"href": "https://sandbox.moip.com.br/v2/multiorders/MOR-T6AK5UX6EBXX"}}}