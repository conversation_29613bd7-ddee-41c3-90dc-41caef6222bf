<?php
/**
 * Plugin Name: ACF Taxonomy CSV Import (Debug Version)
 * Description: Minimal debug version to identify issues
 * Version: 1.0.0-debug
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Simple debug logging
function dtci_debug_log($message) {
    error_log('DTCI Debug: ' . $message);
}

// Check if WooCommerce is active
if (!in_array('woocommerce/woocommerce.php', apply_filters('active_plugins', get_option('active_plugins')))) {
    dtci_debug_log('WooCommerce not active');
    return;
}

class Dynamic_Taxonomy_CSV_Import_Debug {
    
    public function __construct() {
        dtci_debug_log('Constructor called');
        add_action('init', array($this, 'init'));
    }
    
    public function init() {
        dtci_debug_log('Init called');
        
        try {
            // Only add the most basic functionality
            add_action('admin_menu', array($this, 'add_admin_menu'));
            dtci_debug_log('Admin menu hook added');
            
        } catch (Exception $e) {
            dtci_debug_log('Error in init: ' . $e->getMessage());
        }
    }
    
    public function add_admin_menu() {
        dtci_debug_log('Add admin menu called');
        
        try {
            add_submenu_page(
                'woocommerce',
                'ACF Taxonomy Debug',
                'ACF Debug',
                'manage_woocommerce',
                'acf-taxonomy-debug',
                array($this, 'admin_page')
            );
            dtci_debug_log('Admin menu added successfully');
            
        } catch (Exception $e) {
            dtci_debug_log('Error adding admin menu: ' . $e->getMessage());
        }
    }
    
    public function admin_page() {
        dtci_debug_log('Admin page called');
        
        echo '<div class="wrap">';
        echo '<h1>ACF Taxonomy Debug</h1>';
        
        // Test basic functionality
        echo '<h2>Debug Information</h2>';
        
        // Check if ACF is active
        echo '<p><strong>ACF Active:</strong> ' . (function_exists('acf_get_field_groups') ? 'Yes' : 'No') . '</p>';
        
        // Check WooCommerce
        echo '<p><strong>WooCommerce Active:</strong> ' . (class_exists('WooCommerce') ? 'Yes' : 'No') . '</p>';
        
        // Get product taxonomies
        try {
            $product_taxonomies = get_object_taxonomies('product', 'objects');
            echo '<p><strong>Product Taxonomies Found:</strong> ' . count($product_taxonomies) . '</p>';
            
            echo '<h3>All Product Taxonomies:</h3>';
            echo '<ul>';
            foreach ($product_taxonomies as $taxonomy) {
                if (is_object($taxonomy) && isset($taxonomy->name)) {
                    echo '<li>' . esc_html($taxonomy->name) . ' - ' . esc_html($taxonomy->label) . '</li>';
                }
            }
            echo '</ul>';
            
        } catch (Exception $e) {
            echo '<p><strong>Error getting taxonomies:</strong> ' . esc_html($e->getMessage()) . '</p>';
        }
        
        // Manual taxonomies test
        $manual_taxonomies = get_option('dtci_manual_acf_taxonomies', '');
        echo '<p><strong>Manual Taxonomies Setting:</strong> ' . esc_html($manual_taxonomies) . '</p>';
        
        if (!empty($manual_taxonomies)) {
            $taxonomy_names = array_map('trim', explode(',', $manual_taxonomies));
            echo '<h3>Manual Taxonomy Status:</h3>';
            echo '<ul>';
            foreach ($taxonomy_names as $taxonomy_name) {
                $exists = taxonomy_exists($taxonomy_name);
                echo '<li>' . esc_html($taxonomy_name) . ' - ' . ($exists ? 'Exists' : 'Not Found') . '</li>';
            }
            echo '</ul>';
        }
        
        echo '</div>';
    }
    
    public static function activate() {
        dtci_debug_log('Plugin activation started');
        
        try {
            // Set basic options
            update_option('dtci_enable_logging', 'yes');
            update_option('dtci_manual_acf_taxonomies', 'brands,pattern_look,texture,durability,quickship,scale,category_type,application,style,format,dimension,enviroment,acoustic,colors');
            
            dtci_debug_log('Plugin activated successfully');
            
        } catch (Exception $e) {
            dtci_debug_log('Activation error: ' . $e->getMessage());
        }
    }
    
    public static function deactivate() {
        dtci_debug_log('Plugin deactivated');
    }
}

// Activation/deactivation hooks
register_activation_hook(__FILE__, array('Dynamic_Taxonomy_CSV_Import_Debug', 'activate'));
register_deactivation_hook(__FILE__, array('Dynamic_Taxonomy_CSV_Import_Debug', 'deactivate'));

// Initialize
dtci_debug_log('Initializing debug plugin');
new Dynamic_Taxonomy_CSV_Import_Debug();
