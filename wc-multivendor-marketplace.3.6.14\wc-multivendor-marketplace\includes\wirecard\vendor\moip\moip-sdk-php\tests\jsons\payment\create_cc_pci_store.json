{"id": "PAY-L6J2NKS9OGYU", "status": "IN_ANALYSIS", "delayCapture": false, "amount": {"total": 102470, "fees": 5695, "refunds": 0, "liquid": 96775, "currency": "BRL"}, "installmentCount": 1, "fundingInstrument": {"creditCard": {"id": "CRC-2TJ13YB4Y1WU", "brand": "MASTERCARD", "first6": "555566", "last4": "8884", "store": false, "holder": {"birthdate": "1989-06-01", "birthDate": "1989-06-01", "taxDocument": {"type": "CPF", "number": "22222222222"}, "fullname": "<PERSON>"}}, "method": "CREDIT_CARD"}, "fees": [{"type": "TRANSACTION", "amount": 5695}], "events": [{"type": "PAYMENT.IN_ANALYSIS", "createdAt": "2016-02-19T18:18:54.535-02"}, {"type": "PAYMENT.CREATED", "createdAt": "2016-02-19T18:18:51.946-02"}], "_links": {"order": {"href": "https://sandbox.moip.com.br/v2/orders/ORD-8UDL4K9VRJTB", "title": "ORD-8UDL4K9VRJTB"}, "self": {"href": "https://sandbox.moip.com.br/v2/payments/PAY-L6J2NKS9OGYU"}}, "createdAt": "2016-02-19T18:18:51.944-02", "updatedAt": "2016-02-19T18:18:54.535-02"}