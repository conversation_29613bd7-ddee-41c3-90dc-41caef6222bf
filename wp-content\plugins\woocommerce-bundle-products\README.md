# WooCommerce Bundle Products Plugin

A custom WordPress plugin for creating and managing WooCommerce bundle products with full backend order creation support.

## Features

- **Bundle Product Type**: Create bundle products that contain multiple individual products
- **Flexible Pricing**: Choose between fixed pricing or dynamic pricing (sum of individual products)
- **Product Discounts**: Apply individual discounts to products within bundles
- **Bundle Discounts**: Apply overall discounts to the entire bundle
- **Optional Products**: Mark products as optional within bundles
- **Backend Order Creation**: Add bundle products when creating orders manually in WooCommerce admin
- **Frontend Display**: Beautiful display of bundle contents on product pages
- **Order Management**: Bundle contents are clearly displayed in order details

## Installation

1. Upload the plugin folder to `/wp-content/plugins/`
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Ensure WooCommerce is installed and activated

## Usage

### Creating Bundle Products

1. Go to **Products > Add New** in your WordPress admin
2. Set the **Product Type** to "Bundle Product"
3. Configure the bundle settings in the **Bundle Products** tab:
   - **Bundle Pricing**: Choose between Fixed Price or Dynamic Price
   - **Bundle Discount**: Set an overall discount percentage for the bundle
   - **Add Products**: Search and add products to your bundle
   - **Set Quantities**: Define how many of each product are included
   - **Individual Discounts**: Apply discounts to specific products in the bundle
   - **Optional Products**: Mark products as optional

### Bundle Pricing Types

#### Fixed Price
- Set a fixed price for the entire bundle
- Individual product prices are ignored
- Best for promotional bundles with special pricing

#### Dynamic Price
- Bundle price is calculated from individual product prices
- Applies individual product discounts and bundle discount
- Price updates automatically when product prices change

### Adding Bundles to Orders (Backend)

When creating or editing orders in WooCommerce admin:

1. Scroll to the **Add Bundle Products** section
2. Search for bundle products using the search field
3. Select a bundle to preview its contents and total price
4. Click **Add Bundle to Order** to add the bundle to the order
5. The bundle and all its contents will be added as line items

### Frontend Display

Bundle products automatically display their contents on the product page, showing:
- Individual products included in the bundle
- Quantities for each product
- Any discounts applied
- Total savings
- Optional products (if any)

## Database Structure

The plugin creates a custom table `wp_wc_bundle_products` to store bundle relationships:

- `bundle_id`: The ID of the bundle product
- `product_id`: The ID of the product included in the bundle
- `quantity`: How many of this product are included
- `discount_type`: Type of discount (currently supports percentage)
- `discount_value`: Discount percentage for this product
- `optional`: Whether this product is optional (1 or 0)
- `sort_order`: Display order of products in the bundle

## Hooks and Filters

The plugin provides several hooks for customization:

### Actions
- `wc_bundle_before_add_to_cart`: Fired before adding bundle to cart
- `wc_bundle_after_add_to_cart`: Fired after adding bundle to cart

### Filters
- `wc_bundle_product_price`: Filter the calculated price of bundle products
- `wc_bundle_display_contents`: Filter the display of bundle contents
- `wc_bundle_order_item_name`: Filter the order item name for bundles

## Styling

The plugin includes basic CSS for frontend display. You can override these styles in your theme:

```css
.wc-bundle-products {
    /* Main bundle container */
}

.bundle-item {
    /* Individual bundle item */
}

.bundle-item-price {
    /* Price display for bundle items */
}

.bundle-savings {
    /* Total savings display */
}
```

## Requirements

- WordPress 5.0 or higher
- WooCommerce 5.0 or higher
- PHP 7.4 or higher

## Compatibility

- Compatible with most WooCommerce themes
- Works with WooCommerce order management
- Supports WooCommerce REST API
- Compatible with most payment gateways

## Support

For support and customization requests, please contact the development team.

## Changelog

### Version 1.0.0
- Initial release
- Bundle product type creation
- Backend order creation support
- Frontend bundle display
- Dynamic and fixed pricing options
- Individual and bundle discounts
- Optional products support

## License

This plugin is proprietary software developed for Hisense KSA.

## Developer Notes

### File Structure
```
woocommerce-bundle-products/
├── woocommerce-bundle-products.php (Main plugin file)
├── assets/
│   └── admin.js (Admin JavaScript)
└── README.md (This file)
```

### Key Classes and Methods

#### WC_Bundle_Products (Main Class)
- `init()`: Initialize plugin hooks
- `create_bundle_table()`: Create database table
- `add_bundle_product_type()`: Add bundle to product types
- `save_bundle_product_data()`: Save bundle configuration
- `ajax_add_bundle_to_order()`: Handle adding bundles to orders
- `display_bundle_products()`: Frontend bundle display

### Database Queries
The plugin uses direct database queries for bundle relationships to ensure optimal performance when dealing with complex bundle structures.

### Security
- All AJAX requests are protected with nonces
- Input sanitization on all user inputs
- Capability checks for admin functions
