# /*! DO NOT EDIT THIS FILE. This file is a auto generated on 2023-04-05 */
# This file is distributed under the same license as the WCFM - WooCommerce Multivendor Marketplace plugin.
# 
msgid ""
msgstr ""
"Project-Id-Version: WCFM - WooCommerce Multivendor Marketplace 3.5.10\n"
"Report-Msgid-Bugs-To: "
"https://wordpress.org/support/plugin/wc-multivendor-marketplace\n"
"POT-Creation-Date: 2023-04-05 06:50:54+00:00\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"PO-Revision-Date: 2023-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: en\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Poedit-Country: United States\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: "
"__;_e;_x:1,2c;_ex:1,2c;_n:1,2;_nx:1,2,4c;_n_noop:1,2;_nx_noop:1,2,3c;esc_"
"attr__;esc_html__;esc_attr_e;esc_html_e;esc_attr_x:1,2c;esc_html_x:1,2c;\n"
"X-Poedit-Basepath: ../\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-Bookmarks: \n"
"X-Textdomain-Support: yes\n"
"X-Generator: grunt-wp-i18n 1.0.3\n"

#: controllers/media/wcfmmp-controller-media.php:146
#: controllers/product_multivendor/wcfmmp-controller-sell-items-catalog.php:282
msgid "View"
msgstr ""

#: controllers/media/wcfmmp-controller-media.php:149
#: controllers/reviews/wcfmmp-controller-product-reviews.php:141
#: controllers/reviews/wcfmmp-controller-reviews.php:132
#: core/class-wcfmmp-ajax.php:728
msgid "Delete"
msgstr ""

#: controllers/product_multivendor/wcfmmp-controller-sell-items-catalog.php:80
#: views/product_multivendor/wcfmmp-view-sell-items-catalog.php:77
msgid "Simple Product"
msgstr ""

#: controllers/product_multivendor/wcfmmp-controller-sell-items-catalog.php:80
#: views/product_multivendor/wcfmmp-view-sell-items-catalog.php:77
msgid "Variable Product"
msgstr ""

#: controllers/product_multivendor/wcfmmp-controller-sell-items-catalog.php:80
#: views/product_multivendor/wcfmmp-view-sell-items-catalog.php:77
msgid "Grouped Product"
msgstr ""

#: controllers/product_multivendor/wcfmmp-controller-sell-items-catalog.php:80
#: views/product_multivendor/wcfmmp-view-sell-items-catalog.php:77
msgid "External/Affiliate Product"
msgstr ""

#: controllers/product_multivendor/wcfmmp-controller-sell-items-catalog.php:235
#: views/store/wcfmmp-view-store-sidebar.php:34
msgid "Categories"
msgstr ""

#: controllers/product_multivendor/wcfmmp-controller-sell-items-catalog.php:283
msgid "Click here add to your store"
msgstr ""

#: controllers/product_multivendor/wcfmmp-controller-sell-items-catalog.php:283
#: core/class-wcfmmp-product-multivendor.php:154
#: core/class-wcfmmp-product-multivendor.php:317
msgid "Add to My Store"
msgstr ""

#: controllers/refund/wcfmmp-controller-refund-requests-actions.php:34
#: controllers/refund/wcfmmp-controller-refund-requests-actions.php:102
msgid "There has some error in submitted data."
msgstr ""

#: controllers/refund/wcfmmp-controller-refund-requests-actions.php:52
msgid "Refund processing failed, please check wcfm log."
msgstr ""

#: controllers/refund/wcfmmp-controller-refund-requests-actions.php:58
#: helpers/wcfmmp-core-functions.php:625 helpers/wcfmmp-core-functions.php:640
msgid "Refund requests successfully approved."
msgstr ""

#: controllers/refund/wcfmmp-controller-refund-requests-actions.php:60
msgid "No refunds selected for approve"
msgstr ""

#: controllers/refund/wcfmmp-controller-refund-requests-actions.php:119
msgid "Refund request(s) successfully rejected."
msgstr ""

#: controllers/refund/wcfmmp-controller-refund-requests-actions.php:121
msgid "No refund(s) selected for approve"
msgstr ""

#: controllers/refund/wcfmmp-controller-refund-requests-form.php:46
msgid "Captcha failed, please try again."
msgstr ""

#: controllers/refund/wcfmmp-controller-refund-requests-form.php:113
msgid "Refund request amount more than item value."
msgstr ""

#: controllers/refund/wcfmmp-controller-refund-requests-form.php:143
msgid "Refund request tax amount more than item actual tax value."
msgstr ""

#: controllers/refund/wcfmmp-controller-refund-requests-form.php:182
msgid "Refund <b>%s</b> has been processed for Order <b>%s</b> by <b>%s</b>"
msgstr ""

#: controllers/refund/wcfmmp-controller-refund-requests-form.php:199
msgid ""
"Refund <b>%s</b> has been processed for Order <b>%s</b> item <b>%s</b> by "
"<b>%s</b>"
msgstr ""

#: controllers/refund/wcfmmp-controller-refund-requests-form.php:239
msgid "Refund Request <b>%s</b> received for Order <b>%s</b>"
msgstr ""

#: controllers/refund/wcfmmp-controller-refund-requests-form.php:263
msgid "Refund Request <b>%s</b> received for Order <b>%s</b> item <b>%s</b>"
msgstr ""

#: controllers/refund/wcfmmp-controller-refund-requests-form.php:321
msgid "No item selected for refund request."
msgstr ""

#: controllers/refund/wcfmmp-controller-refund-requests-form.php:323
msgid "Refund requests successfully processed."
msgstr ""

#: controllers/refund/wcfmmp-controller-refund-requests.php:110
msgid "Refund Completed"
msgstr ""

#: controllers/refund/wcfmmp-controller-refund-requests.php:112
msgid "Refund Cancelled"
msgstr ""

#: controllers/refund/wcfmmp-controller-refund-requests.php:183
#: views/refund/wcfmmp-view-refund-requests-popup.php:82
msgid "Partial Refund"
msgstr ""

#: controllers/refund/wcfmmp-controller-refund-requests.php:185
#: views/refund/wcfmmp-view-refund-requests-popup.php:82
msgid "Full Refund"
msgstr ""

#: controllers/reviews/wcfmmp-controller-product-reviews.php:94
#: controllers/reviews/wcfmmp-controller-reviews.php:91
#: views/refund/wcfmmp-view-refund-requests.php:40
#: views/reviews/wcfmmp-view-product-reviews.php:21
#: views/reviews/wcfmmp-view-reviews.php:21
msgid "Approved"
msgstr ""

#: controllers/reviews/wcfmmp-controller-product-reviews.php:96
#: controllers/reviews/wcfmmp-controller-reviews.php:93
msgid "Waiting Approval"
msgstr ""

#: controllers/reviews/wcfmmp-controller-product-reviews.php:112
#: controllers/reviews/wcfmmp-controller-reviews.php:111
msgid "Rated %d out of 5"
msgstr ""

#: controllers/reviews/wcfmmp-controller-product-reviews.php:112
#: controllers/reviews/wcfmmp-controller-reviews.php:111
#: core/class-wcfmmp-reviews.php:596
msgid "out of 5"
msgstr ""

#: controllers/reviews/wcfmmp-controller-product-reviews.php:136
#: controllers/reviews/wcfmmp-controller-reviews.php:127
msgid "Unapprove"
msgstr ""

#: controllers/reviews/wcfmmp-controller-product-reviews.php:138
#: controllers/reviews/wcfmmp-controller-reviews.php:129
#: views/refund/wcfmmp-view-refund-requests.php:104
msgid "Approve"
msgstr ""

#: controllers/reviews/wcfmmp-controller-reviews-submit.php:135
msgid "%s has received a new Review from <b>%s</b>"
msgstr ""

#: controllers/reviews/wcfmmp-controller-reviews-submit.php:154
#: core/class-wcfmmp-reviews.php:777
msgid "You have received a new Review from <b>%s</b>"
msgstr ""

#: core/class-wcfmmp-admin.php:117
msgid ""
"<h2>\r\n"
"\t\t\t\t\t\t\t\t\t\t   WCFM Marketplace: Multi Vendor Plugin Conflict "
"Detected !!!\r\n"
"\t\t\t\t\t\t\t\t\t\t </h2>"
msgstr ""

#: core/class-wcfmmp-admin.php:120
msgid ""
"<p %s>WCFM - Marketplace is installed and active. But there is another "
"multi-vendor plugin found in your site. Now this is not possible to run a "
"site with more than one multi-vendor plugins at a time. %sDisable "
"<b><u>%s</u></b> to make your site stable and run smoothly.</p>"
msgstr ""

#: core/class-wcfmmp-admin.php:142
msgid ""
"<h2>\r\n"
"\t\t\t\t\t\t\t\t\t\t   WCFM Marketplace Inactive: WCFM Core Missing !!!\r\n"
"\t\t\t\t\t\t\t\t\t\t </h2>"
msgstr ""

#: core/class-wcfmmp-admin.php:145
msgid ""
"<p>WCFM Marketplace is inactive. WooCommerce Frontend Manager (WCFM Core) "
"must be active for the WCFM Marketplace to work. Please install & activate "
"WooCommerce Frontend Manager.</p>"
msgstr ""

#: core/class-wcfmmp-admin.php:150 core/class-wcfmmp-admin.php:176
msgid "GET IT NOW"
msgstr ""

#: core/class-wcfmmp-admin.php:168
msgid ""
"<h2>\r\n"
"\t\t\t\t\t\t\t\t\t\t   WCFM Marketplace: Vendor Registration Disable !!!\r\n"
"\t\t\t\t\t\t\t\t\t\t </h2>"
msgstr ""

#: core/class-wcfmmp-admin.php:171
msgid ""
"<p>WCFM - Membership is essential for WCFM Marketplace to register new "
"vendors. You may additionally setup vendor membership using this as well. "
"Recurring subscription also possible using PayPal and Stripe.</p>"
msgstr ""

#: core/class-wcfmmp-admin.php:188 core/class-wcfmmp-admin.php:189
msgid "Marketplace"
msgstr ""

#: core/class-wcfmmp-admin.php:196 core/class-wcfmmp-admin.php:334
#: core/class-wcfmmp-admin.php:370 core/class-wcfmmp-admin.php:383
#: core/class-wcfmmp-settings.php:72 core/class-wcfmmp-store-hours.php:68
#: core/class-wcfmmp-store-hours.php:73 core/class-wcfmmp-vendor.php:223
#: core/class-wcfmmp-vendor.php:230 core/class-wcfmmp-vendor.php:1823
#: helpers/class-wcfmmp-store-setup.php:57
#: helpers/wcfmmp-core-functions.php:896 views/media/wcfmmp-view-media.php:70
#: views/media/wcfmmp-view-media.php:83
#: views/product-geolocate/wcfmmp-view-product-lists-vendor-filter.php:67
#: views/product-geolocate/wcfmmp-view-product-lists-vendor-filter.php:83
#: views/product_multivendor/wcfmmp-view-more-offers-loop.php:74
#: views/product_multivendor/wcfmmp-view-sell-items-catalog.php:117
#: views/product_multivendor/wcfmmp-view-sell-items-catalog.php:131
#: views/refund/wcfmmp-view-refund-requests.php:64
#: views/refund/wcfmmp-view-refund-requests.php:77
#: views/reviews/wcfmmp-view-product-reviews.php:97
#: views/reviews/wcfmmp-view-product-reviews.php:109
#: views/reviews/wcfmmp-view-reviews.php:38
#: views/reviews/wcfmmp-view-reviews.php:95
#: views/reviews/wcfmmp-view-reviews.php:107
msgid "Store"
msgstr ""

#: core/class-wcfmmp-admin.php:339 core/class-wcfmmp-product.php:161
#: core/class-wcfmmp-vendor.php:1759 core/class-wcfmmp-vendor.php:1766
#: core/class-wcfmmp-vendor.php:1790 core/class-wcfmmp-vendor.php:1797
msgid "Commission"
msgstr ""

#: core/class-wcfmmp-admin.php:370
msgid "Choose"
msgstr ""

#: core/class-wcfmmp-admin.php:401 core/class-wcfmmp-admin.php:533
#: core/class-wcfmmp-admin.php:618 core/class-wcfmmp-admin.php:691
#: core/class-wcfmmp-frontend.php:589 core/class-wcfmmp-product.php:133
#: core/class-wcfmmp-product.php:198 core/class-wcfmmp-vendor.php:955
#: core/class-wcfmmp-vendor.php:973 core/class-wcfmmp-vendor.php:998
msgid "By Global Rule"
msgstr ""

#: core/class-wcfmmp-admin.php:427 core/class-wcfmmp-admin.php:633
#: core/class-wcfmmp-admin.php:710 core/class-wcfmmp-frontend.php:625
#: core/class-wcfmmp-product.php:166 core/class-wcfmmp-settings.php:507
#: core/class-wcfmmp-vendor.php:1022
msgid "Commission For"
msgstr ""

#: core/class-wcfmmp-admin.php:429 core/class-wcfmmp-admin.php:635
#: core/class-wcfmmp-admin.php:710 core/class-wcfmmp-frontend.php:625
#: core/class-wcfmmp-notification-manager.php:82
#: core/class-wcfmmp-product.php:166 core/class-wcfmmp-settings.php:507
#: core/class-wcfmmp-settings.php:1055 core/class-wcfmmp-settings.php:1520
#: core/class-wcfmmp-settings.php:1571 core/class-wcfmmp-settings.php:1634
#: core/class-wcfmmp-settings.php:1777
#: includes/payment-gateways/class-wcfmmp-gateway-wirecard.php:147
msgid "Vendor"
msgstr ""

#: core/class-wcfmmp-admin.php:429 core/class-wcfmmp-admin.php:635
#: core/class-wcfmmp-admin.php:710 core/class-wcfmmp-frontend.php:625
#: core/class-wcfmmp-product.php:166 core/class-wcfmmp-settings.php:507
#: includes/payment-gateways/class-wcfmmp-gateway-wirecard.php:146
msgid "Admin"
msgstr ""

#: core/class-wcfmmp-admin.php:431 core/class-wcfmmp-admin.php:637
#: core/class-wcfmmp-admin.php:710 core/class-wcfmmp-frontend.php:625
#: core/class-wcfmmp-product.php:166 core/class-wcfmmp-vendor.php:1022
msgid "Always applicable as per global rule."
msgstr ""

#: core/class-wcfmmp-admin.php:434 core/class-wcfmmp-admin.php:549
#: core/class-wcfmmp-admin.php:640 core/class-wcfmmp-admin.php:714
#: core/class-wcfmmp-frontend.php:626 core/class-wcfmmp-product.php:167
#: core/class-wcfmmp-product.php:205 core/class-wcfmmp-settings.php:508
#: core/class-wcfmmp-vendor.php:1023
msgid "Commission Mode"
msgstr ""

#: core/class-wcfmmp-admin.php:438 core/class-wcfmmp-admin.php:553
#: core/class-wcfmmp-admin.php:644 core/class-wcfmmp-admin.php:714
#: core/class-wcfmmp-product.php:167 core/class-wcfmmp-product.php:205
msgid ""
"Keep this as Global to apply commission rule as per vendor or marketplace "
"commission setup."
msgstr ""

#: core/class-wcfmmp-admin.php:441 core/class-wcfmmp-admin.php:563
#: core/class-wcfmmp-admin.php:647 core/class-wcfmmp-admin.php:718
#: core/class-wcfmmp-frontend.php:627 core/class-wcfmmp-frontend.php:633
#: core/class-wcfmmp-frontend.php:640 core/class-wcfmmp-frontend.php:647
#: core/class-wcfmmp-product.php:168 core/class-wcfmmp-product.php:206
#: core/class-wcfmmp-settings.php:509 core/class-wcfmmp-settings.php:515
#: core/class-wcfmmp-settings.php:522 core/class-wcfmmp-settings.php:529
#: core/class-wcfmmp-vendor.php:1024 core/class-wcfmmp-vendor.php:1030
#: core/class-wcfmmp-vendor.php:1037 core/class-wcfmmp-vendor.php:1044
msgid "Commission Percent(%)"
msgstr ""

#: core/class-wcfmmp-admin.php:447 core/class-wcfmmp-admin.php:577
#: core/class-wcfmmp-admin.php:653 core/class-wcfmmp-admin.php:722
#: core/class-wcfmmp-frontend.php:628 core/class-wcfmmp-frontend.php:634
#: core/class-wcfmmp-frontend.php:641 core/class-wcfmmp-frontend.php:648
#: core/class-wcfmmp-product.php:169 core/class-wcfmmp-product.php:207
#: core/class-wcfmmp-settings.php:510 core/class-wcfmmp-settings.php:516
#: core/class-wcfmmp-settings.php:523 core/class-wcfmmp-settings.php:530
#: core/class-wcfmmp-vendor.php:1025 core/class-wcfmmp-vendor.php:1031
#: core/class-wcfmmp-vendor.php:1038 core/class-wcfmmp-vendor.php:1045
msgid "Commission Fixed"
msgstr ""

#: core/class-wcfmmp-admin.php:453 core/class-wcfmmp-admin.php:659
#: core/class-wcfmmp-admin.php:725 core/class-wcfmmp-frontend.php:658
#: core/class-wcfmmp-product.php:173 core/class-wcfmmp-settings.php:540
#: core/class-wcfmmp-vendor.php:1054
msgid "Commission Tax Settings"
msgstr ""

#: core/class-wcfmmp-admin.php:455 core/class-wcfmmp-admin.php:661
#: core/class-wcfmmp-admin.php:728 core/class-wcfmmp-frontend.php:659
#: core/class-wcfmmp-product.php:174 core/class-wcfmmp-settings.php:545
#: core/class-wcfmmp-settings.php:1073 core/class-wcfmmp-settings.php:1086
#: core/class-wcfmmp-settings.php:1201 core/class-wcfmmp-settings.php:1324
#: core/class-wcfmmp-settings.php:1672 core/class-wcfmmp-settings.php:1936
#: core/class-wcfmmp-settings.php:1950 core/class-wcfmmp-settings.php:1965
#: core/class-wcfmmp-vendor.php:1055
msgid "Enable"
msgstr ""

#: core/class-wcfmmp-admin.php:461 core/class-wcfmmp-admin.php:667
#: core/class-wcfmmp-admin.php:732 core/class-wcfmmp-frontend.php:660
#: core/class-wcfmmp-product.php:175 core/class-wcfmmp-settings.php:546
#: core/class-wcfmmp-vendor.php:1057
msgid "Tax Label"
msgstr ""

#: core/class-wcfmmp-admin.php:463 core/class-wcfmmp-admin.php:669
#: core/class-wcfmmp-admin.php:732 core/class-wcfmmp-frontend.php:660
#: core/class-wcfmmp-product.php:175 core/class-wcfmmp-settings.php:546
#: core/class-wcfmmp-vendor.php:1057 views/emails/plain/store-new-order.php:101
#: views/emails/plain/store-new-order.php:102
#: views/emails/store-new-order.php:109 views/emails/store-new-order.php:110
#: views/refund/wcfmmp-view-refund-requests-popup.php:98
msgid "Tax"
msgstr ""

#: core/class-wcfmmp-admin.php:467 core/class-wcfmmp-admin.php:673
#: core/class-wcfmmp-admin.php:736 core/class-wcfmmp-frontend.php:661
#: core/class-wcfmmp-product.php:176 core/class-wcfmmp-settings.php:547
#: core/class-wcfmmp-vendor.php:1058
msgid "Tax Percent (%)"
msgstr ""

#: core/class-wcfmmp-admin.php:631 core/class-wcfmmp-admin.php:707
#: core/class-wcfmmp-settings.php:498
msgid "Commission Settings"
msgstr ""

#: core/class-wcfmmp-ajax.php:57 core/class-wcfmmp-ajax.php:59
msgid "No Order ID found."
msgstr ""

#: core/class-wcfmmp-ajax.php:65 core/class-wcfmmp-ajax.php:67
msgid "This status not allowed, please go through Refund Request."
msgstr ""

#: core/class-wcfmmp-ajax.php:73 core/class-wcfmmp-ajax.php:75
msgid "This status not allowed, please go through Shipment Tracking."
msgstr ""

#: core/class-wcfmmp-ajax.php:118
msgid "Order item <b>%s</b> status updated to <b>%s</b> by <b>%s</b>"
msgstr ""

#: core/class-wcfmmp-ajax.php:127
msgid "<b>%s</b> order item <b>%s</b> status updated to <b>%s</b> by <b>%s</b>"
msgstr ""

#: core/class-wcfmmp-ajax.php:157
msgid "Order status updated to <b>%s</b> by <b>%s</b>"
msgstr ""

#: core/class-wcfmmp-ajax.php:166
msgid "<b>%s</b> order status updated to <b>%s</b> by <b>%s</b>"
msgstr ""

#: core/class-wcfmmp-ajax.php:217 core/class-wcfmmp-commission.php:919
msgid "<b>%s</b> order status updated to <b>%s</b>"
msgstr ""

#: core/class-wcfmmp-ajax.php:246 core/class-wcfmmp-ajax.php:249
msgid "Order status updated."
msgstr ""

#: core/class-wcfmmp-ajax.php:258 core/class-wcfmmp-ajax.php:328
#: core/class-wcfmmp-ajax.php:422 core/class-wcfmmp-ajax.php:791
#: core/class-wcfmmp-ajax.php:826 core/class-wcfmmp-ajax.php:860
#: core/class-wcfmmp-ajax.php:895 core/class-wcfmmp-ajax.php:924
#: core/class-wcfmmp-ajax.php:965 core/class-wcfmmp-ajax.php:1012
#: core/class-wcfmmp-ledger.php:167 core/class-wcfmmp-media.php:167
#: core/class-wcfmmp-media.php:197 core/class-wcfmmp-media.php:229
#: core/class-wcfmmp-product-multivendor.php:220
#: core/class-wcfmmp-product-multivendor.php:360
#: core/class-wcfmmp-product-multivendor.php:407
#: core/class-wcfmmp-product-multivendor.php:536
#: core/class-wcfmmp-refund.php:214 core/class-wcfmmp-refund.php:270
#: core/class-wcfmmp-reviews.php:232 core/class-wcfmmp-reviews.php:297
#: core/class-wcfmmp-reviews.php:424 core/class-wcfmmp-reviews.php:458
msgid "Invalid nonce! Refresh your page and try again."
msgstr ""

#: core/class-wcfmmp-ajax.php:427 core/class-wcfmmp-ajax.php:796
#: core/class-wcfmmp-ajax.php:831 core/class-wcfmmp-ajax.php:865
#: core/class-wcfmmp-ajax.php:900 core/class-wcfmmp-ajax.php:970
#: core/class-wcfmmp-ajax.php:1017 core/class-wcfmmp-ledger.php:179
#: core/class-wcfmmp-media.php:179 core/class-wcfmmp-media.php:202
#: core/class-wcfmmp-media.php:234
#: core/class-wcfmmp-product-multivendor.php:233
#: core/class-wcfmmp-product-multivendor.php:365
#: core/class-wcfmmp-product-multivendor.php:412
#: core/class-wcfmmp-refund.php:227 core/class-wcfmmp-refund.php:242
#: core/class-wcfmmp-refund.php:252 core/class-wcfmmp-reviews.php:244
#: core/class-wcfmmp-reviews.php:259 core/class-wcfmmp-reviews.php:274
#: core/class-wcfmmp-reviews.php:302 core/class-wcfmmp-reviews.php:429
#: core/class-wcfmmp-reviews.php:463
msgid "You don&#8217;t have permission to do this."
msgstr ""

#: core/class-wcfmmp-ajax.php:543
msgid "Back to Zone List"
msgstr ""

#: core/class-wcfmmp-ajax.php:549 core/class-wcfmmp-ajax.php:552
#: views/shipping/wcfmmp-view-shipping-settings.php:178
msgid "Zone Name"
msgstr ""

#: core/class-wcfmmp-ajax.php:561 core/class-wcfmmp-ajax.php:565
msgid "Zone Location"
msgstr ""

#: core/class-wcfmmp-ajax.php:598
msgid "Limit Zone Location"
msgstr ""

#: core/class-wcfmmp-ajax.php:613
msgid "Select Specific Countries"
msgstr ""

#: core/class-wcfmmp-ajax.php:629
msgid "Select Specific States"
msgstr ""

#: core/class-wcfmmp-ajax.php:646
msgid "Select Specific City"
msgstr ""

#: core/class-wcfmmp-ajax.php:663
msgid "Set your postcode"
msgstr ""

#: core/class-wcfmmp-ajax.php:668
msgid ""
"Postcodes containing wildcards (e.g. CB23*) or fully numeric ranges (e.g. "
"90210...99000) are also supported."
msgstr ""

#: core/class-wcfmmp-ajax.php:681
#: views/shipping/wcfmmp-view-shipping-settings.php:180
msgid "Shipping Method"
msgstr ""

#: core/class-wcfmmp-ajax.php:685
msgid "Add your shipping method for appropiate zone"
msgstr ""

#: core/class-wcfmmp-ajax.php:693
#: includes/shipping-gateways/class-wcfmmp-shipping-by-country.php:78
#: includes/shipping-gateways/class-wcfmmp-shipping-by-distance.php:190
#: includes/shipping-gateways/class-wcfmmp-shipping-by-weight.php:194
#: views/shipping/wcfmmp-view-edit-method-popup.php:44
#: views/shipping/wcfmmp-view-edit-method-popup.php:98
#: views/shipping/wcfmmp-view-edit-method-popup.php:175
msgid "Method Title"
msgstr ""

#: core/class-wcfmmp-ajax.php:694 core/class-wcfmmp-vendor.php:1745
#: core/class-wcfmmp-vendor.php:1776
#: includes/sms-gateways/class-wcfmmp-twilio-sms-notification.php:310
#: views/ledger/wcfmmp-view-ledger.php:107
#: views/ledger/wcfmmp-view-ledger.php:117
#: views/reviews/wcfmmp-view-product-reviews.php:92
#: views/reviews/wcfmmp-view-product-reviews.php:104
#: views/reviews/wcfmmp-view-reviews-manage.php:167
#: views/reviews/wcfmmp-view-reviews.php:90
#: views/reviews/wcfmmp-view-reviews.php:102
msgid "Status"
msgstr ""

#: core/class-wcfmmp-ajax.php:695
#: includes/payment-gateways/class-wcfmmp-gateway-wirecard.php:137
#: views/shipping/wcfmmp-view-edit-method-popup.php:78
#: views/shipping/wcfmmp-view-edit-method-popup.php:155
#: views/shipping/wcfmmp-view-edit-method-popup.php:235
msgid "Description"
msgstr ""

#: core/class-wcfmmp-ajax.php:703
msgid "No shipping method found"
msgstr ""

#: core/class-wcfmmp-ajax.php:723
#: views/shipping/wcfmmp-view-shipping-settings.php:195
msgid "Edit"
msgstr ""

#: core/class-wcfmmp-ajax.php:763
#: views/shipping/wcfmmp-view-add-method-popup.php:37
msgid "Add Shipping Method"
msgstr ""

#: core/class-wcfmmp-ajax.php:812
msgid "Shipping method added successfully"
msgstr ""

#: core/class-wcfmmp-ajax.php:846
msgid "Shipping method enabled successfully"
msgstr ""

#: core/class-wcfmmp-ajax.php:846
msgid "Shipping method disabled successfully"
msgstr ""

#: core/class-wcfmmp-ajax.php:880
msgid "Shipping method deleted"
msgstr ""

#: core/class-wcfmmp-ajax.php:907
msgid "Shipping title must be required"
msgstr ""

#: core/class-wcfmmp-ajax.php:914
msgid "Shipping method updated"
msgstr ""

#: core/class-wcfmmp-ajax.php:981
msgid "Your Store: <b>%s</b> has been set off-line."
msgstr ""

#: core/class-wcfmmp-ajax.php:1000
msgid "Vendor Store Off-line."
msgstr ""

#: core/class-wcfmmp-ajax.php:1028
msgid "Your Store: <b>%s</b> has been set on-line."
msgstr ""

#: core/class-wcfmmp-ajax.php:1047
msgid "Vendor Store On-line."
msgstr ""

#: core/class-wcfmmp-commission.php:535
msgid "Split Pay for Order #"
msgstr ""

#: core/class-wcfmmp-commission.php:536
msgid "Payment for Order #"
msgstr ""

#: core/class-wcfmmp-frontend.php:314 core/class-wcfmmp-frontend.php:399
msgid "Delivery Location"
msgstr ""

#: core/class-wcfmmp-frontend.php:411 helpers/wcfmmp-core-functions.php:793
#: helpers/wcfmmp-core-functions.php:819
msgid "Location"
msgstr ""

#: core/class-wcfmmp-frontend.php:498
msgid "Next"
msgstr ""

#: core/class-wcfmmp-frontend.php:510
msgid "Previous"
msgstr ""

#: core/class-wcfmmp-frontend.php:532 core/class-wcfmmp-frontend.php:534
msgid "Become a Vendor"
msgstr ""

#: core/class-wcfmmp-frontend.php:548
msgid "Store Manager"
msgstr ""

#: core/class-wcfmmp-frontend.php:629 core/class-wcfmmp-settings.php:511
#: core/class-wcfmmp-vendor.php:1026
msgid "Commission By Sales Rule(s)"
msgstr ""

#: core/class-wcfmmp-frontend.php:629 core/class-wcfmmp-settings.php:511
#: core/class-wcfmmp-vendor.php:1026
msgid ""
"Commission rules depending upon vendors total sales. e.g 50&#37; commission "
"when sales < %s1000, 75&#37; commission when sales > %s1000 but < %s2000 "
"and so on. You may define any number of such rules. Please be sure, do not "
"set conflicting rules."
msgstr ""

#: core/class-wcfmmp-frontend.php:630 core/class-wcfmmp-settings.php:512
#: core/class-wcfmmp-vendor.php:1027
msgid "Sales"
msgstr ""

#: core/class-wcfmmp-frontend.php:631 core/class-wcfmmp-frontend.php:638
#: core/class-wcfmmp-frontend.php:645 core/class-wcfmmp-settings.php:513
#: core/class-wcfmmp-settings.php:520 core/class-wcfmmp-settings.php:527
#: core/class-wcfmmp-vendor.php:1028 core/class-wcfmmp-vendor.php:1035
#: core/class-wcfmmp-vendor.php:1042
msgid "Rule"
msgstr ""

#: core/class-wcfmmp-frontend.php:631 core/class-wcfmmp-frontend.php:638
#: core/class-wcfmmp-frontend.php:645 core/class-wcfmmp-settings.php:513
#: core/class-wcfmmp-settings.php:520 core/class-wcfmmp-settings.php:527
#: core/class-wcfmmp-vendor.php:1028 core/class-wcfmmp-vendor.php:1035
#: core/class-wcfmmp-vendor.php:1042
msgid "Up to"
msgstr ""

#: core/class-wcfmmp-frontend.php:631 core/class-wcfmmp-frontend.php:638
#: core/class-wcfmmp-frontend.php:645 core/class-wcfmmp-settings.php:513
#: core/class-wcfmmp-settings.php:520 core/class-wcfmmp-settings.php:527
#: core/class-wcfmmp-vendor.php:1028 core/class-wcfmmp-vendor.php:1035
#: core/class-wcfmmp-vendor.php:1042
msgid "More than"
msgstr ""

#: core/class-wcfmmp-frontend.php:632 core/class-wcfmmp-frontend.php:639
#: core/class-wcfmmp-frontend.php:646 core/class-wcfmmp-settings.php:514
#: core/class-wcfmmp-settings.php:521 core/class-wcfmmp-settings.php:528
#: core/class-wcfmmp-vendor.php:1029 core/class-wcfmmp-vendor.php:1036
#: core/class-wcfmmp-vendor.php:1043
msgid "Commission Type"
msgstr ""

#: core/class-wcfmmp-frontend.php:632 core/class-wcfmmp-frontend.php:639
#: core/class-wcfmmp-frontend.php:646 core/class-wcfmmp-settings.php:514
#: core/class-wcfmmp-settings.php:521 core/class-wcfmmp-settings.php:528
#: core/class-wcfmmp-settings.php:833 core/class-wcfmmp-settings.php:857
#: core/class-wcfmmp-vendor.php:1029 core/class-wcfmmp-vendor.php:1036
#: core/class-wcfmmp-vendor.php:1043 core/class-wcfmmp-vendor.php:1084
#: core/class-wcfmmp-vendor.php:1124 helpers/wcfmmp-core-functions.php:413
msgid "Percent"
msgstr ""

#: core/class-wcfmmp-frontend.php:632 core/class-wcfmmp-frontend.php:639
#: core/class-wcfmmp-frontend.php:646 core/class-wcfmmp-settings.php:514
#: core/class-wcfmmp-settings.php:521 core/class-wcfmmp-settings.php:528
#: core/class-wcfmmp-settings.php:833 core/class-wcfmmp-settings.php:857
#: core/class-wcfmmp-vendor.php:1029 core/class-wcfmmp-vendor.php:1036
#: core/class-wcfmmp-vendor.php:1043 core/class-wcfmmp-vendor.php:1084
#: core/class-wcfmmp-vendor.php:1124 helpers/wcfmmp-core-functions.php:414
msgid "Fixed"
msgstr ""

#: core/class-wcfmmp-frontend.php:632 core/class-wcfmmp-frontend.php:639
#: core/class-wcfmmp-frontend.php:646 core/class-wcfmmp-settings.php:514
#: core/class-wcfmmp-settings.php:521 core/class-wcfmmp-settings.php:528
#: core/class-wcfmmp-settings.php:833 core/class-wcfmmp-settings.php:857
#: core/class-wcfmmp-vendor.php:1029 core/class-wcfmmp-vendor.php:1036
#: core/class-wcfmmp-vendor.php:1043 core/class-wcfmmp-vendor.php:1084
#: core/class-wcfmmp-vendor.php:1124 helpers/wcfmmp-core-functions.php:415
msgid "Percent + Fixed"
msgstr ""

#: core/class-wcfmmp-frontend.php:636 core/class-wcfmmp-settings.php:518
#: core/class-wcfmmp-vendor.php:1033
msgid "Commission By Product Price"
msgstr ""

#: core/class-wcfmmp-frontend.php:636 core/class-wcfmmp-settings.php:518
#: core/class-wcfmmp-vendor.php:1033
msgid ""
"Commission rules depending upon product price. e.g 80&#37; commission when "
"product cost < %s1000, %s100 fixed commission when product cost > %s1000 "
"and so on. You may define any number of such rules. Please be sure, do not "
"set conflicting rules."
msgstr ""

#: core/class-wcfmmp-frontend.php:637 core/class-wcfmmp-settings.php:519
#: core/class-wcfmmp-vendor.php:1034
msgid "Product Cost"
msgstr ""

#: core/class-wcfmmp-frontend.php:643 core/class-wcfmmp-settings.php:525
#: core/class-wcfmmp-vendor.php:1040
msgid "Commission By Purchase Quantity"
msgstr ""

#: core/class-wcfmmp-frontend.php:643 core/class-wcfmmp-settings.php:525
#: core/class-wcfmmp-vendor.php:1040
msgid ""
"Commission rules depending upon purchased product quantity. e.g 80&#37; "
"commission when purchase quantity 2, 80&#37; commission when purchase "
"quantity > 2 and so on. You may define any number of such rules. Please be "
"sure, do not set conflicting rules."
msgstr ""

#: core/class-wcfmmp-frontend.php:644 core/class-wcfmmp-settings.php:526
#: core/class-wcfmmp-vendor.php:1041
msgid "Purchase Quantity"
msgstr ""

#: core/class-wcfmmp-frontend.php:650 core/class-wcfmmp-settings.php:532
#: core/class-wcfmmp-vendor.php:1047
msgid "Shipping cost goes to vendor?"
msgstr ""

#: core/class-wcfmmp-frontend.php:651 core/class-wcfmmp-settings.php:533
#: core/class-wcfmmp-vendor.php:1048
msgid "Tax goes to vendor?"
msgstr ""

#: core/class-wcfmmp-frontend.php:652 core/class-wcfmmp-settings.php:534
#: core/class-wcfmmp-vendor.php:1049
msgid "Commission after consider Vendor Coupon?"
msgstr ""

#: core/class-wcfmmp-frontend.php:652 core/class-wcfmmp-settings.php:534
#: core/class-wcfmmp-vendor.php:1049
msgid "Generate vendor commission after deduct Vendor Coupon discounts."
msgstr ""

#: core/class-wcfmmp-frontend.php:653 core/class-wcfmmp-settings.php:535
#: core/class-wcfmmp-vendor.php:1050
msgid "Commission after consider Admin Coupon?"
msgstr ""

#: core/class-wcfmmp-frontend.php:653 core/class-wcfmmp-settings.php:535
#: core/class-wcfmmp-vendor.php:1050
msgid "Generate vendor commission after deduct Admin Coupon discounts."
msgstr ""

#: core/class-wcfmmp-frontend.php:934
#: includes/store-widgets/class-wcfmmp-widget-store-lists-category-filter.php:64
#: views/store-lists/wcfmmp-view-store-lists-search-form.php:109
msgid "Choose Category"
msgstr ""

#: core/class-wcfmmp-frontend.php:934
msgid "Choose Location"
msgstr ""

#: core/class-wcfmmp-frontend.php:934
#: includes/store-widgets/class-wcfmmp-widget-store-lists-location-filter.php:94
#: includes/store-widgets/class-wcfmmp-widget-store-lists-location-filter.php:100
msgid "Choose State"
msgstr ""

#: core/class-wcfmmp-frontend.php:935 core/class-wcfmmp-frontend.php:947
#: core/class-wcfmmp-frontend.php:968 core/class-wcfmmp-library.php:103
#: helpers/class-wcfmmp-store-setup.php:199
#: includes/store-widgets/class-wcfmmp-widget-store-lists-radius-filter.php:65
#: views/product-geolocate/wcfmmp-view-product-lists-search-form.php:39
#: views/store-lists/wcfmmp-view-store-lists-search-form.php:254
msgid "Insert your address .."
msgstr ""

#: core/class-wcfmmp-ledger.php:76 core/class-wcfmmp-ledger.php:107
#: views/ledger/wcfmmp-view-ledger.php:34
#: views/ledger/wcfmmp-view-ledger.php:41
msgid "Ledger Book"
msgstr ""

#: core/class-wcfmmp-ledger.php:196 core/class-wcfmmp-vendor.php:1747
#: core/class-wcfmmp-vendor.php:1778 views/emails/plain/store-new-order.php:74
#: views/emails/plain/store-new-order.php:76
#: views/emails/store-new-order.php:82 views/emails/store-new-order.php:84
#: views/ledger/wcfmmp-view-ledger.php:52
#: views/reviews/wcfmmp-view-reviews-manage.php:77
msgid "Order"
msgstr ""

#: core/class-wcfmmp-ledger.php:197 views/ledger/wcfmmp-view-ledger.php:53
msgid "Withdrawal"
msgstr ""

#: core/class-wcfmmp-ledger.php:198 views/emails/plain/store-new-order.php:424
#: views/emails/store-new-order.php:432 views/ledger/wcfmmp-view-ledger.php:47
#: views/ledger/wcfmmp-view-ledger.php:57
msgid "Refunded"
msgstr ""

#: core/class-wcfmmp-ledger.php:199 views/ledger/wcfmmp-view-ledger.php:58
msgid "Partial Refunded"
msgstr ""

#: core/class-wcfmmp-ledger.php:200 views/ledger/wcfmmp-view-ledger.php:59
msgid "Charges"
msgstr ""

#: core/class-wcfmmp-ledger.php:214
msgid "Earning for %s order #%s."
msgstr ""

#: core/class-wcfmmp-ledger.php:226
msgid "Withdrawal Charges."
msgstr ""

#: core/class-wcfmmp-ledger.php:232
msgid "Auto withdrawal by paymode for order #%s."
msgstr ""

#: core/class-wcfmmp-ledger.php:234
msgid "Withdrawal by Stripe Split Pay for order #%s."
msgstr ""

#: core/class-wcfmmp-ledger.php:236
msgid "Withdrawal by request for order(s) %s."
msgstr ""

#: core/class-wcfmmp-ledger.php:248
msgid "Reverse Withdrawal for order #%s."
msgstr ""

#: core/class-wcfmmp-ledger.php:265
msgid "Request by Vendor for order #%s."
msgstr ""

#: core/class-wcfmmp-ledger.php:267
msgid "Request by Admin for order #%s."
msgstr ""

#: core/class-wcfmmp-ledger.php:269
msgid "Request by Customer for order #%s."
msgstr ""

#: core/class-wcfmmp-media.php:70 core/class-wcfmmp-media.php:101
#: core/class-wcfmmp.php:350 views/media/wcfmmp-view-media.php:67
#: views/media/wcfmmp-view-media.php:80
msgid "Media"
msgstr ""

#: core/class-wcfmmp-non-ajax.php:31
msgid "View WCFM Marketplace settings"
msgstr ""

#: core/class-wcfmmp-non-ajax.php:31
msgid "Settings"
msgstr ""

#: core/class-wcfmmp-non-ajax.php:48
msgid "View WCFM documentation"
msgstr ""

#: core/class-wcfmmp-non-ajax.php:48
msgid "Documentation"
msgstr ""

#: core/class-wcfmmp-non-ajax.php:50
msgid "Visit premium customer support"
msgstr ""

#: core/class-wcfmmp-non-ajax.php:50 helpers/wcfmmp-core-functions.php:557
msgid "Support"
msgstr ""

#: core/class-wcfmmp-non-ajax.php:52
msgid "Any WC help feel free to contact us"
msgstr ""

#: core/class-wcfmmp-non-ajax.php:52
msgid "Customization Help"
msgstr ""

#: core/class-wcfmmp-notification-manager.php:63
#: core/class-wcfmmp-notification-manager.php:67
msgid "Notification Manager"
msgstr ""

#: core/class-wcfmmp-notification-manager.php:72
msgid "Notification Sound"
msgstr ""

#: core/class-wcfmmp-notification-manager.php:81
msgid "Admin Notification"
msgstr ""

#: core/class-wcfmmp-notification-manager.php:82
msgid "Notification"
msgstr ""

#: core/class-wcfmmp-notification-manager.php:85
msgid "Notification Type"
msgstr ""

#: core/class-wcfmmp-notification-manager.php:86
#: core/class-wcfmmp-notification-manager.php:93
#: core/class-wcfmmp-store.php:619 core/class-wcfmmp-vendor.php:1668
#: helpers/class-wcfmmp-store-setup.php:793
msgid "Email"
msgstr ""

#: core/class-wcfmmp-notification-manager.php:87
#: core/class-wcfmmp-notification-manager.php:94
#: includes/sms-gateways/class-wcfmmp-twilio-sms-notification.php:309
msgid "Message"
msgstr ""

#: core/class-wcfmmp-notification-manager.php:89
#: core/class-wcfmmp-notification-manager.php:96
msgid "SMS"
msgstr ""

#: core/class-wcfmmp-product-multivendor.php:108
msgid "Sell Items Catalog"
msgstr ""

#: core/class-wcfmmp-product-multivendor.php:256
msgid "Title edit disabeled, it has other sellers!"
msgstr ""

#: core/class-wcfmmp-product-multivendor.php:375
#: core/class-wcfmmp-product-multivendor.php:417
msgid "No product to duplicate has been supplied!"
msgstr ""

#: core/class-wcfmmp-product-multivendor.php:384
#: core/class-wcfmmp-product-multivendor.php:463
#. translators: %s: product id
msgid "Product creation failed, could not find original product: %s"
msgstr ""

#: core/class-wcfmmp-product.php:204
msgid "Commission Rule"
msgstr ""

#: core/class-wcfmmp-product.php:505
#: views/shipping/wcfmmp-view-shipping-settings.php:54
msgid "Processing Time"
msgstr ""

#: core/class-wcfmmp-product.php:505
#: views/shipping/wcfmmp-view-shipping-settings.php:54
msgid "The time required before sending the product for delivery"
msgstr ""

#: core/class-wcfmmp-product.php:520
msgid "Override Shipping"
msgstr ""

#: core/class-wcfmmp-product.php:520
msgid "Override your store's default shipping cost for this product"
msgstr ""

#: core/class-wcfmmp-product.php:521
msgid "Additional Price"
msgstr ""

#: core/class-wcfmmp-product.php:521
msgid "First product of this type will be charged with this price"
msgstr ""

#: core/class-wcfmmp-product.php:522 core/class-wcfmmp-settings.php:1102
#: views/shipping/wcfmmp-view-shipping-settings.php:82
msgid "Per Qty Additional Price"
msgstr ""

#: core/class-wcfmmp-product.php:522 core/class-wcfmmp-settings.php:1102
#: views/shipping/wcfmmp-view-shipping-settings.php:82
msgid "Every second product of same type will be charged with this price"
msgstr ""

#: core/class-wcfmmp-refund.php:83
#: views/refund/wcfmmp-view-refund-requests.php:23
#: views/refund/wcfmmp-view-refund-requests.php:30
msgid "Refund Requests"
msgstr ""

#: core/class-wcfmmp-refund.php:130 core/class-wcfmmp-refund.php:370
msgid "Refund"
msgstr ""

#: core/class-wcfmmp-refund.php:308
#: views/refund/wcfmmp-view-refund-requests-popup.php:75
msgid "Refund Request"
msgstr ""

#: core/class-wcfmmp-refund.php:729
msgid "Your Refund Request approved for Order <b>%s</b>."
msgstr ""

#: core/class-wcfmmp-refund.php:745 core/class-wcfmmp-refund.php:779
#: core/class-wcfmmp-refund.php:832 core/class-wcfmmp-refund.php:849
#: core/class-wcfmmp-withdraw.php:534 core/class-wcfmmp-withdraw.php:595
#: core/class-wcfmmp-withdraw.php:654 core/class-wcfmmp-withdraw.php:715
#: views/refund/wcfmmp-view-refund-requests.php:96
msgid "Note"
msgstr ""

#: core/class-wcfmmp-refund.php:773
msgid "Partial Refund Request approved for Order <b>%s</b>."
msgstr ""

#: core/class-wcfmmp-refund.php:775
msgid "Refund Request approved for Order <b>%s</b>."
msgstr ""

#: core/class-wcfmmp-refund.php:816 core/class-wcfmmp-refund.php:894
msgid "Your Refund Request cancelled for Order <b>%s</b>."
msgstr ""

#: core/class-wcfmmp-refund.php:847
msgid "Refund Request cancelled for Order <b>%s</b>."
msgstr ""

#: core/class-wcfmmp-reviews.php:90
#: views/reviews/wcfmmp-view-product-reviews.php:64
msgid "Store Reviews"
msgstr ""

#: core/class-wcfmmp-reviews.php:94
#: views/reviews/wcfmmp-view-product-reviews.php:38
#: views/reviews/wcfmmp-view-reviews.php:65
msgid "Product Reviews"
msgstr ""

#: core/class-wcfmmp-reviews.php:125 core/class-wcfmmp-store.php:136
#: core/class-wcfmmp-store.php:183 core/class-wcfmmp.php:348
#: views/reviews/wcfmmp-view-reviews.php:38
msgid "Reviews"
msgstr ""

#: core/class-wcfmmp-reviews.php:583
msgid "Rated %s out of 5"
msgstr ""

#: core/class-wcfmmp-reviews.php:586
msgid "Rated %s out of 5 based on %s review(s)"
msgstr ""

#: core/class-wcfmmp-reviews.php:594
msgid "No reviews yet!"
msgstr ""

#: core/class-wcfmmp-reviews.php:800
msgid "Store Review"
msgstr ""

#: core/class-wcfmmp-settings.php:100 core/class-wcfmmp-settings.php:104
msgid "Marketplace Settings"
msgstr ""

#: core/class-wcfmmp-settings.php:109
msgid "Product Approval"
msgstr ""

#: core/class-wcfmmp-settings.php:109
msgid "Manager vendor capabilities from %sCapability Setting%s"
msgstr ""

#: core/class-wcfmmp-settings.php:111
msgid "Store URL Base"
msgstr ""

#: core/class-wcfmmp-settings.php:111
msgid "Define the seller store URL  (%s/[this-text]/[seller-name])"
msgstr ""

#: core/class-wcfmmp-settings.php:112
msgid "Visible Sold By"
msgstr ""

#: core/class-wcfmmp-settings.php:112
msgid "Uncheck this to disable Sold By display for products."
msgstr ""

#: core/class-wcfmmp-settings.php:113
msgid "Sold By Label"
msgstr ""

#: core/class-wcfmmp-settings.php:113
msgid "Sold By label along with store name under product archive pages."
msgstr ""

#: core/class-wcfmmp-settings.php:115
msgid "Sold By Template"
msgstr ""

#: core/class-wcfmmp-settings.php:115
msgid "Simple"
msgstr ""

#: core/class-wcfmmp-settings.php:115
msgid "Advanced"
msgstr ""

#: core/class-wcfmmp-settings.php:115
msgid "As Tab"
msgstr ""

#: core/class-wcfmmp-settings.php:115
msgid "Single product page Sold By template."
msgstr ""

#: core/class-wcfmmp-settings.php:120
msgid "Sold By Position"
msgstr ""

#: core/class-wcfmmp-settings.php:120
msgid "Below Title"
msgstr ""

#: core/class-wcfmmp-settings.php:120
msgid "Below Price"
msgstr ""

#: core/class-wcfmmp-settings.php:120
msgid "Below Short Description"
msgstr ""

#: core/class-wcfmmp-settings.php:120
msgid "Below Add to Cart"
msgstr ""

#: core/class-wcfmmp-settings.php:120
msgid "Sold by display position at Single Product Page."
msgstr ""

#: core/class-wcfmmp-settings.php:121 core/class-wcfmmp-vendor.php:839
msgid "Store Name Position"
msgstr ""

#: core/class-wcfmmp-settings.php:121 core/class-wcfmmp-vendor.php:839
msgid "On Banner"
msgstr ""

#: core/class-wcfmmp-settings.php:121 core/class-wcfmmp-vendor.php:839
msgid "At Header"
msgstr ""

#: core/class-wcfmmp-settings.php:121
msgid "Store name position at Vendor Store Page."
msgstr ""

#: core/class-wcfmmp-settings.php:123 core/class-wcfmmp-sidebar-widgets.php:58
msgid "Store List Sidebar"
msgstr ""

#: core/class-wcfmmp-settings.php:123
msgid "Uncheck this to disable store list sidebar."
msgstr ""

#: core/class-wcfmmp-settings.php:124
msgid "Store Sidebar"
msgstr ""

#: core/class-wcfmmp-settings.php:124
msgid "Uncheck this to disable vendor store sidebar."
msgstr ""

#: core/class-wcfmmp-settings.php:125
msgid "Store Sidebar Position"
msgstr ""

#: core/class-wcfmmp-settings.php:125
msgid "At Left"
msgstr ""

#: core/class-wcfmmp-settings.php:125
msgid "At Right"
msgstr ""

#: core/class-wcfmmp-settings.php:127 core/class-wcfmmp-vendor.php:840
msgid "Products per page"
msgstr ""

#: core/class-wcfmmp-settings.php:127
msgid "No of products at Store per Page."
msgstr ""

#: core/class-wcfmmp-settings.php:128
msgid "Store Related Products"
msgstr ""

#: core/class-wcfmmp-settings.php:128
msgid "As per WC Default Rule"
msgstr ""

#: core/class-wcfmmp-settings.php:128
msgid "Only same Store Products"
msgstr ""

#: core/class-wcfmmp-settings.php:128
msgid "Single product page related products rule."
msgstr ""

#: core/class-wcfmmp-settings.php:132
msgid "Store Default Logo"
msgstr ""

#: core/class-wcfmmp-settings.php:133
msgid "Store Default Banner"
msgstr ""

#: core/class-wcfmmp-settings.php:134
msgid "Store List Default Banner"
msgstr ""

#: core/class-wcfmmp-settings.php:135
msgid "Banner Dimension(s)"
msgstr ""

#: core/class-wcfmmp-settings.php:136
msgid "Width"
msgstr ""

#: core/class-wcfmmp-settings.php:136
msgid "Store banner preferred width in pixels."
msgstr ""

#: core/class-wcfmmp-settings.php:137
msgid "Height"
msgstr ""

#: core/class-wcfmmp-settings.php:137
msgid "Store banner preferred height in pixels."
msgstr ""

#: core/class-wcfmmp-settings.php:138
msgid "Width (Mob)"
msgstr ""

#: core/class-wcfmmp-settings.php:138
msgid "Store banner preferred width for mobile in pixels."
msgstr ""

#: core/class-wcfmmp-settings.php:139
msgid "Height (Mob)"
msgstr ""

#: core/class-wcfmmp-settings.php:139
msgid "Store banner preferred heightfor mobile in pixels."
msgstr ""

#: core/class-wcfmmp-settings.php:142
msgid "Disable Store Setup Widget"
msgstr ""

#: core/class-wcfmmp-settings.php:144
msgid "On Uninstall"
msgstr ""

#: core/class-wcfmmp-settings.php:144
msgid ""
"Delete all marketplace data on uninstall. Be careful, there is no way to "
"retrieve those data if once deleted!"
msgstr ""

#: core/class-wcfmmp-settings.php:170 core/class-wcfmmp-settings.php:174
msgid "Order Settings"
msgstr ""

#: core/class-wcfmmp-settings.php:178
msgid "Disable Multivendor Checkout"
msgstr ""

#: core/class-wcfmmp-settings.php:178
msgid "Enable this restrict multiple vendors product checkout at same order."
msgstr ""

#: core/class-wcfmmp-settings.php:179
msgid "Order Sync"
msgstr ""

#: core/class-wcfmmp-settings.php:179
msgid ""
"Enable this to sync WC main order status when vendors update their order "
"status."
msgstr ""

#: core/class-wcfmmp-settings.php:227
msgid "GEO Location"
msgstr ""

#: core/class-wcfmmp-settings.php:231
msgid "GEO Location Settings"
msgstr ""

#: core/class-wcfmmp-settings.php:237
msgid "Map Library"
msgstr ""

#: core/class-wcfmmp-settings.php:239
msgid "Google Map API Key"
msgstr ""

#: core/class-wcfmmp-settings.php:239
msgid "%sAPI Key%s is needed to display map on store page"
msgstr ""

#: core/class-wcfmmp-settings.php:241
msgid "Store List Radius Search"
msgstr ""

#: core/class-wcfmmp-settings.php:241
msgid "Check this to enable store list radius filter by user location."
msgstr ""

#: core/class-wcfmmp-settings.php:243
msgid "Product List Radius Search"
msgstr ""

#: core/class-wcfmmp-settings.php:243
msgid "Check this to enable product list radius filter by user location."
msgstr ""

#: core/class-wcfmmp-settings.php:245
msgid "Maximum Radius to Search"
msgstr ""

#: core/class-wcfmmp-settings.php:245
msgid "Set maximum radius allow to search."
msgstr ""

#: core/class-wcfmmp-settings.php:247
msgid "Radius Unit"
msgstr ""

#: core/class-wcfmmp-settings.php:249
msgid "Enable Auto Filter"
msgstr ""

#: core/class-wcfmmp-settings.php:249
msgid "Check this to enable auto-filter by user's location."
msgstr ""

#: core/class-wcfmmp-settings.php:251
msgid "Show Product Location"
msgstr ""

#: core/class-wcfmmp-settings.php:251
msgid "Check this to show product's location at single product page."
msgstr ""

#: core/class-wcfmmp-settings.php:253
msgid "Checkout User Location"
msgstr ""

#: core/class-wcfmmp-settings.php:253
msgid "Check this to enable user's location input using map at checkout."
msgstr ""

#: core/class-wcfmmp-settings.php:259
msgid "Map Default Location"
msgstr ""

#: core/class-wcfmmp-settings.php:259 core/class-wcfmmp-vendor.php:806
#: helpers/class-wcfmmp-store-setup.php:460
msgid "Type an address to find"
msgstr ""

#: core/class-wcfmmp-settings.php:272
msgid "Store List Page"
msgstr ""

#: core/class-wcfmmp-settings.php:278
msgid ""
"You just have to create a page using short code – %swcfm_stores%s\r\n"
"\t\t\t\t\t\t\tYou may specify “per_row” attribute to specify number of "
"store in one row, by default it’s “2”.%s\r\n"
"\t\t\t\t\t\t\tAlso specify “per_page” attribute to set how many stores you "
"want to show in a page. Default value is 10.%s\r\n"
"\t\t\t\t\t\t\tYou may also specify “excludes” attribute (comma separated "
"store ids) to excludes some store from list."
msgstr ""

#: core/class-wcfmmp-settings.php:502
msgid "Marketplace Commission Settings"
msgstr ""

#: core/class-wcfmmp-settings.php:677
msgid "Withdrawal Settings"
msgstr ""

#: core/class-wcfmmp-settings.php:681
msgid "Marketplace Withdrawal Settings"
msgstr ""

#: core/class-wcfmmp-settings.php:687 core/class-wcfmmp-vendor.php:1120
msgid "Request auto-approve?"
msgstr ""

#: core/class-wcfmmp-settings.php:687 core/class-wcfmmp-vendor.php:1120
msgid ""
"Check this to automatically disburse payments to vendors on request, no "
"admin approval required. Auto disbursement only works for auto-payment "
"gateways, e.g. PayPal, Stripe etc. Bank Transfer or other non-autopay mode "
"always requires approval, as these are manual transactions."
msgstr ""

#: core/class-wcfmmp-settings.php:689 core/class-wcfmmp-vendor.php:1119
msgid "Withdrawal Mode"
msgstr ""

#: core/class-wcfmmp-settings.php:689
msgid "Manual Withdrawal"
msgstr ""

#: core/class-wcfmmp-settings.php:689
msgid "Periodic Withdrawal"
msgstr ""

#: core/class-wcfmmp-settings.php:689
msgid "By Order Status"
msgstr ""

#: core/class-wcfmmp-settings.php:692
msgid "Order Status"
msgstr ""

#: core/class-wcfmmp-settings.php:692
msgid "Order status for generate withdrawal request automatically."
msgstr ""

#: core/class-wcfmmp-settings.php:694
msgid "Schedule Interval"
msgstr ""

#: core/class-wcfmmp-settings.php:694
msgid "Every Day"
msgstr ""

#: core/class-wcfmmp-settings.php:694
msgid "Every 7 Days (Every Week - Monday)"
msgstr ""

#: core/class-wcfmmp-settings.php:694
msgid "Every 15 Days (Every 2 Weeks - Monday)"
msgstr ""

#: core/class-wcfmmp-settings.php:694
msgid "Every 30 Days (Every Month - 1st)"
msgstr ""

#: core/class-wcfmmp-settings.php:694
msgid "Every 60 Days (Every 2 Months - 1st)"
msgstr ""

#: core/class-wcfmmp-settings.php:694
msgid "Every 90 Days (Every 3 Months - 1st)"
msgstr ""

#: core/class-wcfmmp-settings.php:696
msgid "Allowed Order Status for Withdrawal"
msgstr ""

#: core/class-wcfmmp-settings.php:696
msgid "Allowed order statuses for which vendor may request for withdrawal."
msgstr ""

#: core/class-wcfmmp-settings.php:700 core/class-wcfmmp-vendor.php:1122
msgid "Minimum Withdraw Limit"
msgstr ""

#: core/class-wcfmmp-settings.php:700 core/class-wcfmmp-vendor.php:1122
msgid ""
"Minimum balance required to make a withdraw request. Leave blank to set no "
"minimum limits."
msgstr ""

#: core/class-wcfmmp-settings.php:701 core/class-wcfmmp-vendor.php:1123
msgid "Withdraw Threshold"
msgstr ""

#: core/class-wcfmmp-settings.php:701 core/class-wcfmmp-vendor.php:1123
msgid ""
"Withdraw Threshold Days, (Make order matured to make a withdraw request). "
"Leave empty to inactive this option."
msgstr ""

#: core/class-wcfmmp-settings.php:707
msgid "Marketplace Reverse Withdrawal Settings"
msgstr ""

#: core/class-wcfmmp-settings.php:713 views/ledger/wcfmmp-view-ledger.php:55
msgid "Reverse Withdrawal"
msgstr ""

#: core/class-wcfmmp-settings.php:713
msgid ""
"Enable this to keep track reverse withdrawals. In case vendor receive full "
"payment (e.g. COD) from customer then they have to reverse-pay admin "
"commission. This is only applicable for reverse-withdrawal payment methods."
msgstr ""

#: core/class-wcfmmp-settings.php:714
msgid "Reverse or No Withdrawal Payment Methods"
msgstr ""

#: core/class-wcfmmp-settings.php:714
msgid ""
"Order Payment Methods which are not applicable for vendor withdrawal "
"request. e.g Order payment method COD and vendor receiving that amount "
"directly from customers. So, no more require withdrawal request. You may "
"also enable Reverse Withdrawal to track reverse pending payments for such "
"payment options."
msgstr ""

#: core/class-wcfmmp-settings.php:717
msgid "Reverse Withdraw Limit"
msgstr ""

#: core/class-wcfmmp-settings.php:717
msgid ""
"Set reverse withdrawal threshold limit, if reverse-pay balance reach this "
"limit then vendor will not allow to withdrawal anymore. Leave empty to "
"inactive this option."
msgstr ""

#: core/class-wcfmmp-settings.php:778
msgid "Payment Settings"
msgstr ""

#: core/class-wcfmmp-settings.php:782
msgid "Marketplace Payment Settings"
msgstr ""

#: core/class-wcfmmp-settings.php:788
msgid "Withdraw Payment Methods"
msgstr ""

#: core/class-wcfmmp-settings.php:791
msgid "Stripe 3D Secure and SCA?"
msgstr ""

#: core/class-wcfmmp-settings.php:791
msgid ""
"3D Secure and SCA ready transaction is only supported when both your "
"platform and the connected account (Vendor) are in the same region: both in "
"Europe or both in the U.S."
msgstr ""

#: core/class-wcfmmp-settings.php:792
msgid "Stripe Split Pay Mode"
msgstr ""

#: core/class-wcfmmp-settings.php:792
msgid "Direct Charges"
msgstr ""

#: core/class-wcfmmp-settings.php:792
msgid "Destination Charges"
msgstr ""

#: core/class-wcfmmp-settings.php:792
msgid "Transfer Charges"
msgstr ""

#: core/class-wcfmmp-settings.php:792
msgid "Set your preferred Stripe Split pay mode."
msgstr ""

#: core/class-wcfmmp-settings.php:794
msgid "Enable Test Mode"
msgstr ""

#: core/class-wcfmmp-settings.php:798 core/class-wcfmmp-settings.php:811
msgid "PayPal Client ID"
msgstr ""

#: core/class-wcfmmp-settings.php:799 core/class-wcfmmp-settings.php:812
msgid "PayPal Secret Key"
msgstr ""

#: core/class-wcfmmp-settings.php:801 core/class-wcfmmp-settings.php:814
msgid "Stripe Client ID"
msgstr ""

#: core/class-wcfmmp-settings.php:801 core/class-wcfmmp-settings.php:814
msgid "Set redirect URL(s) to your Stripe account - <br/> %s <br />%s"
msgstr ""

#: core/class-wcfmmp-settings.php:802 core/class-wcfmmp-settings.php:815
msgid "Stripe Publish Key"
msgstr ""

#: core/class-wcfmmp-settings.php:803 core/class-wcfmmp-settings.php:816
msgid "Stripe Secret Key"
msgstr ""

#: core/class-wcfmmp-settings.php:805 core/class-wcfmmp-settings.php:818
msgid "Wirecard Token"
msgstr ""

#: core/class-wcfmmp-settings.php:806 core/class-wcfmmp-settings.php:819
msgid "Wirecard Key"
msgstr ""

#: core/class-wcfmmp-settings.php:807 core/class-wcfmmp-settings.php:820
msgid "Wirecard Publick Key"
msgstr ""

#: core/class-wcfmmp-settings.php:827 core/class-wcfmmp-vendor.php:1077
msgid "Transaction Charges"
msgstr ""

#: core/class-wcfmmp-settings.php:828 core/class-wcfmmp-vendor.php:1078
msgid ""
"These charges will be deducted from vendor's total order commission "
"depending upon %sOrder Payment Method%s."
msgstr ""

#: core/class-wcfmmp-settings.php:833 core/class-wcfmmp-settings.php:857
#: core/class-wcfmmp-vendor.php:1084
msgid "Charge Type"
msgstr ""

#: core/class-wcfmmp-settings.php:833 core/class-wcfmmp-settings.php:857
#: core/class-wcfmmp-vendor.php:1084 core/class-wcfmmp-vendor.php:1124
msgid "No Charge"
msgstr ""

#: core/class-wcfmmp-settings.php:837 core/class-wcfmmp-vendor.php:1088
msgid "Charge"
msgstr ""

#: core/class-wcfmmp-settings.php:838 core/class-wcfmmp-settings.php:861
#: core/class-wcfmmp-settings.php:866 core/class-wcfmmp-settings.php:871
#: core/class-wcfmmp-settings.php:876 core/class-wcfmmp-vendor.php:1089
#: core/class-wcfmmp-vendor.php:1129 core/class-wcfmmp-vendor.php:1134
#: core/class-wcfmmp-vendor.php:1139 core/class-wcfmmp-vendor.php:1144
msgid "Percent Charge(%)"
msgstr ""

#: core/class-wcfmmp-settings.php:839 core/class-wcfmmp-settings.php:862
#: core/class-wcfmmp-settings.php:867 core/class-wcfmmp-settings.php:872
#: core/class-wcfmmp-settings.php:877 core/class-wcfmmp-vendor.php:1090
#: core/class-wcfmmp-vendor.php:1130 core/class-wcfmmp-vendor.php:1135
#: core/class-wcfmmp-vendor.php:1140 core/class-wcfmmp-vendor.php:1145
msgid "Fixed Charge"
msgstr ""

#: core/class-wcfmmp-settings.php:851 core/class-wcfmmp-vendor.php:1124
msgid "Withdrawal Charges"
msgstr ""

#: core/class-wcfmmp-settings.php:852
msgid ""
"These charges will be deducted from vendor's withdrawal amount depending "
"upon %sWithdrawal Payment Method%s."
msgstr ""

#: core/class-wcfmmp-settings.php:857 core/class-wcfmmp-vendor.php:1124
msgid "Charges applicable for each withdarwal."
msgstr ""

#: core/class-wcfmmp-settings.php:860 core/class-wcfmmp-vendor.php:1128
msgid "PayPal Charge"
msgstr ""

#: core/class-wcfmmp-settings.php:863 core/class-wcfmmp-settings.php:868
#: core/class-wcfmmp-settings.php:873 core/class-wcfmmp-settings.php:878
#: core/class-wcfmmp-vendor.php:1131 core/class-wcfmmp-vendor.php:1136
#: core/class-wcfmmp-vendor.php:1141 core/class-wcfmmp-vendor.php:1146
msgid "Charge Tax"
msgstr ""

#: core/class-wcfmmp-settings.php:863 core/class-wcfmmp-settings.php:868
#: core/class-wcfmmp-settings.php:873 core/class-wcfmmp-settings.php:878
#: core/class-wcfmmp-vendor.php:1131 core/class-wcfmmp-vendor.php:1136
#: core/class-wcfmmp-vendor.php:1141 core/class-wcfmmp-vendor.php:1146
msgid "Tax for withdrawal charge, calculate in percent."
msgstr ""

#: core/class-wcfmmp-settings.php:865 core/class-wcfmmp-vendor.php:1133
msgid "Stripe Charge"
msgstr ""

#: core/class-wcfmmp-settings.php:870 core/class-wcfmmp-vendor.php:1138
msgid "Skrill Charge"
msgstr ""

#: core/class-wcfmmp-settings.php:875 core/class-wcfmmp-vendor.php:1143
msgid "Bank Transfer Charge"
msgstr ""

#: core/class-wcfmmp-settings.php:1001
#: includes/payment-gateways/class-wcfmmp-gateway-wirecard.php:198
msgid "Wirecard APP: Access Token Generate - Something went wrong Wirecard!"
msgstr ""

#: core/class-wcfmmp-settings.php:1051 core/class-wcfmmp-settings.php:1055
msgid "Shipping Settings"
msgstr ""

#: core/class-wcfmmp-settings.php:1061 core/class-wcfmmp-vendor.php:889
#: includes/shipping-gateways/class-wcfmmp-shipping-by-zone.php:52
#: includes/shipping-gateways/class-wcfmmp-shipping-by-zone.php:194
msgid "Store Shipping"
msgstr ""

#: core/class-wcfmmp-settings.php:1061
msgid "Uncheck this to disable vendor wise store shipping options."
msgstr ""

#: core/class-wcfmmp-settings.php:1067
msgid "Shipping By Zone"
msgstr ""

#: core/class-wcfmmp-settings.php:1073
msgid "Uncheck this to disable zone wise shipping options."
msgstr ""

#: core/class-wcfmmp-settings.php:1080
#: views/shipping/wcfmmp-view-shipping-settings.php:71
msgid "Shipping By Country"
msgstr ""

#: core/class-wcfmmp-settings.php:1086
msgid "Uncheck this to disable country wise shipping options."
msgstr ""

#: core/class-wcfmmp-settings.php:1100
#: views/shipping/wcfmmp-view-shipping-settings.php:80
msgid "Default Shipping Price"
msgstr ""

#: core/class-wcfmmp-settings.php:1100
#: views/shipping/wcfmmp-view-shipping-settings.php:80
msgid ""
"This is the base price and will be the starting shipping price for each "
"product"
msgstr ""

#: core/class-wcfmmp-settings.php:1101
#: views/shipping/wcfmmp-view-shipping-settings.php:81
msgid "Per Product Additional Price"
msgstr ""

#: core/class-wcfmmp-settings.php:1101
#: views/shipping/wcfmmp-view-shipping-settings.php:81
msgid ""
"If a customer buys more than one type product from your store, first "
"product of the every second type will be charged with this price"
msgstr ""

#: core/class-wcfmmp-settings.php:1103 core/class-wcfmmp-settings.php:1203
#: core/class-wcfmmp-settings.php:1328
#: views/shipping/wcfmmp-view-shipping-settings.php:83
#: views/shipping/wcfmmp-view-shipping-settings.php:291
#: views/shipping/wcfmmp-view-shipping-settings.php:400
msgid "Free Shipping Minimum Order Amount"
msgstr ""

#: core/class-wcfmmp-settings.php:1103 core/class-wcfmmp-settings.php:1203
#: core/class-wcfmmp-settings.php:1328
#: views/shipping/wcfmmp-view-shipping-settings.php:83
#: views/shipping/wcfmmp-view-shipping-settings.php:291
#: views/shipping/wcfmmp-view-shipping-settings.php:400
msgid "NO Free Shipping"
msgstr ""

#: core/class-wcfmmp-settings.php:1103 core/class-wcfmmp-settings.php:1203
#: core/class-wcfmmp-settings.php:1328
#: views/shipping/wcfmmp-view-shipping-settings.php:83
#: views/shipping/wcfmmp-view-shipping-settings.php:291
#: views/shipping/wcfmmp-view-shipping-settings.php:400
msgid ""
"Free shipping will be available if order amount more than this. Leave empty "
"to disable Free Shipping."
msgstr ""

#: core/class-wcfmmp-settings.php:1104 core/class-wcfmmp-settings.php:1204
#: core/class-wcfmmp-settings.php:1329
#: views/shipping/wcfmmp-view-shipping-settings.php:84
#: views/shipping/wcfmmp-view-shipping-settings.php:292
#: views/shipping/wcfmmp-view-shipping-settings.php:401
msgid "Enable Local Pickup"
msgstr ""

#: core/class-wcfmmp-settings.php:1105 core/class-wcfmmp-settings.php:1205
#: core/class-wcfmmp-settings.php:1330
#: views/shipping/wcfmmp-view-shipping-settings.php:85
#: views/shipping/wcfmmp-view-shipping-settings.php:293
#: views/shipping/wcfmmp-view-shipping-settings.php:402
msgid "Local Pickup Cost"
msgstr ""

#: core/class-wcfmmp-settings.php:1106
#: views/shipping/wcfmmp-view-shipping-settings.php:86
msgid "Ships from:"
msgstr ""

#: core/class-wcfmmp-settings.php:1106
#: views/shipping/wcfmmp-view-shipping-settings.php:86
msgid ""
"Location from where the products are shipped for delivery. Usually it is "
"same as the store."
msgstr ""

#: core/class-wcfmmp-settings.php:1137
#: views/shipping/wcfmmp-view-shipping-settings.php:117
msgid "Shipping Rates by Country"
msgstr ""

#: core/class-wcfmmp-settings.php:1142
#: views/shipping/wcfmmp-view-shipping-settings.php:121
msgid ""
"Add the countries you deliver your products to. You can specify states as "
"well. If the shipping price is same except some countries, there is an "
"option Everywhere Else, you can use that."
msgstr ""

#: core/class-wcfmmp-settings.php:1146 core/class-wcfmmp-settings.php:1241
#: core/class-wcfmmp-vendor.php:783 core/class-wcfmmp-vendor.php:1671
#: helpers/class-wcfmmp-store-setup.php:796
#: views/shipping/wcfmmp-view-shipping-settings.php:124
#: views/shipping/wcfmmp-view-shipping-settings.php:302
msgid "Country"
msgstr ""

#: core/class-wcfmmp-settings.php:1153 core/class-wcfmmp-settings.php:1172
#: core/class-wcfmmp-settings.php:1296 core/class-wcfmmp-settings.php:1364
#: views/refund/wcfmmp-view-refund-requests-popup.php:92
#: views/shipping/wcfmmp-view-edit-method-popup.php:115
#: views/shipping/wcfmmp-view-edit-method-popup.php:192
#: views/shipping/wcfmmp-view-shipping-settings.php:131
#: views/shipping/wcfmmp-view-shipping-settings.php:151
#: views/shipping/wcfmmp-view-shipping-settings.php:358
#: views/shipping/wcfmmp-view-shipping-settings.php:431
msgid "Cost"
msgstr ""

#: core/class-wcfmmp-settings.php:1161
#: views/shipping/wcfmmp-view-shipping-settings.php:140
msgid "State Shipping Rates"
msgstr ""

#: core/class-wcfmmp-settings.php:1166
#: views/shipping/wcfmmp-view-shipping-settings.php:145
msgid "State"
msgstr ""

#: core/class-wcfmmp-settings.php:1175 core/class-wcfmmp-settings.php:1291
#: core/class-wcfmmp-settings.php:1298 core/class-wcfmmp-settings.php:1366
#: helpers/wcfmmp-core-functions.php:944
#: includes/shipping-gateways/class-wcfmmp-shipping-by-country.php:161
#: includes/shipping-gateways/class-wcfmmp-shipping-by-country.php:198
#: includes/shipping-gateways/class-wcfmmp-shipping-by-distance.php:117
#: includes/shipping-gateways/class-wcfmmp-shipping-by-distance.php:153
#: includes/shipping-gateways/class-wcfmmp-shipping-by-weight.php:118
#: includes/shipping-gateways/class-wcfmmp-shipping-by-weight.php:155
#: views/shipping/wcfmmp-view-shipping-settings.php:154
#: views/shipping/wcfmmp-view-shipping-settings.php:352
#: views/shipping/wcfmmp-view-shipping-settings.php:360
#: views/shipping/wcfmmp-view-shipping-settings.php:433
msgid "Free Shipping"
msgstr ""

#: core/class-wcfmmp-settings.php:1193
#: views/shipping/wcfmmp-view-shipping-settings.php:242
msgid "Shipping By Weight"
msgstr ""

#: core/class-wcfmmp-settings.php:1201
msgid "Uncheck this to disable weight based shipping options."
msgstr ""

#: core/class-wcfmmp-settings.php:1232
#: views/shipping/wcfmmp-view-shipping-settings.php:295
msgid "Country and Weight wise Shipping Rate Calculation"
msgstr ""

#: core/class-wcfmmp-settings.php:1237
#: views/shipping/wcfmmp-view-shipping-settings.php:299
msgid ""
"Add the countries you deliver your products to and specify rates for weight "
"range. If the shipping price is same except some countries/states, there is "
"an option Everywhere Else, you can use that."
msgstr ""

#: core/class-wcfmmp-settings.php:1248
#: views/shipping/wcfmmp-view-shipping-settings.php:309
msgid "Calculate cost"
msgstr ""

#: core/class-wcfmmp-settings.php:1252
#: views/shipping/wcfmmp-view-shipping-settings.php:313
msgid "Based on rules"
msgstr ""

#: core/class-wcfmmp-settings.php:1252 core/class-wcfmmp-settings.php:1255
#: views/shipping/wcfmmp-view-shipping-settings.php:313
#: views/shipping/wcfmmp-view-shipping-settings.php:316
msgid "Per unit cost"
msgstr ""

#: core/class-wcfmmp-settings.php:1261
#: views/shipping/wcfmmp-view-shipping-settings.php:322
msgid "Shipping cost will be calculated by <b>Per unit cost x Product weight</b>"
msgstr ""

#: core/class-wcfmmp-settings.php:1265
msgid "Default cost if no matching rule"
msgstr ""

#: core/class-wcfmmp-settings.php:1273
#: views/shipping/wcfmmp-view-shipping-settings.php:334
msgid "Weight-Cost Rules"
msgstr ""

#: core/class-wcfmmp-settings.php:1279
#: views/shipping/wcfmmp-view-shipping-settings.php:340
msgid "Weight Rule"
msgstr ""

#: core/class-wcfmmp-settings.php:1284
#: views/shipping/wcfmmp-view-shipping-settings.php:345
msgid "Weight up to"
msgstr ""

#: core/class-wcfmmp-settings.php:1285
#: views/shipping/wcfmmp-view-shipping-settings.php:346
msgid "Weight more than"
msgstr ""

#: core/class-wcfmmp-settings.php:1289
#: views/shipping/wcfmmp-view-shipping-settings.php:350
msgid "Weight"
msgstr ""

#: core/class-wcfmmp-settings.php:1315
#: views/shipping/wcfmmp-view-shipping-settings.php:379
msgid "Shipping By Distance"
msgstr ""

#: core/class-wcfmmp-settings.php:1324
msgid "Uncheck this to disable distance based shipping options."
msgstr ""

#: core/class-wcfmmp-settings.php:1326
#: views/shipping/wcfmmp-view-shipping-settings.php:398
msgid "Default Cost"
msgstr ""

#: core/class-wcfmmp-settings.php:1326
#: views/shipping/wcfmmp-view-shipping-settings.php:398
msgid ""
"Default shipping cost, will be added with distance rule cost. Leave empty "
"to consider default cost as `0`."
msgstr ""

#: core/class-wcfmmp-settings.php:1327
#: views/shipping/wcfmmp-view-shipping-settings.php:399
msgid "Max Distance"
msgstr ""

#: core/class-wcfmmp-settings.php:1327
#: views/shipping/wcfmmp-view-shipping-settings.php:399
msgid "No Limit"
msgstr ""

#: core/class-wcfmmp-settings.php:1327
#: views/shipping/wcfmmp-view-shipping-settings.php:399
msgid "Upto maximum distance shipping supported. Leave empty to consider no limit."
msgstr ""

#: core/class-wcfmmp-settings.php:1341
#: views/shipping/wcfmmp-view-shipping-settings.php:408
msgid "Distance-Cost Rules"
msgstr ""

#: core/class-wcfmmp-settings.php:1348
#: views/shipping/wcfmmp-view-shipping-settings.php:415
msgid "Distance Rule"
msgstr ""

#: core/class-wcfmmp-settings.php:1353
#: views/shipping/wcfmmp-view-shipping-settings.php:420
msgid "Distance up to"
msgstr ""

#: core/class-wcfmmp-settings.php:1354
#: views/shipping/wcfmmp-view-shipping-settings.php:421
msgid "Distance more than"
msgstr ""

#: core/class-wcfmmp-settings.php:1358 core/class-wcfmmp-shipping.php:511
#: views/shipping/wcfmmp-view-shipping-settings.php:425
msgid "Distance"
msgstr ""

#: core/class-wcfmmp-settings.php:1516 core/class-wcfmmp-settings.php:1520
msgid "Refund Settings"
msgstr ""

#: core/class-wcfmmp-settings.php:1525
msgid "Refund auto-approve?"
msgstr ""

#: core/class-wcfmmp-settings.php:1526
msgid "Refund by Customer?"
msgstr ""

#: core/class-wcfmmp-settings.php:1526
msgid ""
"Enable this to allow customers make refund requests. Customers refund "
"requests never auto-approve, admin always has to manually approve this."
msgstr ""

#: core/class-wcfmmp-settings.php:1527
msgid "Refund Threshold"
msgstr ""

#: core/class-wcfmmp-settings.php:1527
msgid ""
"Refund Threshold Days, (Allow an order available to make a refund request). "
"Leave empty to inactive this option."
msgstr ""

#: core/class-wcfmmp-settings.php:1567 core/class-wcfmmp-settings.php:1571
msgid "Review Settings"
msgstr ""

#: core/class-wcfmmp-settings.php:1576
msgid "Review auto-approve?"
msgstr ""

#: core/class-wcfmmp-settings.php:1577
msgid "Review only store users?"
msgstr ""

#: core/class-wcfmmp-settings.php:1577
msgid ""
"Enable this to allow only users to review the store who already purchased "
"something from this store."
msgstr ""

#: core/class-wcfmmp-settings.php:1578
msgid "Product review sync?"
msgstr ""

#: core/class-wcfmmp-settings.php:1578
msgid "Enable this to allow vendor's products review consider as store review."
msgstr ""

#: core/class-wcfmmp-settings.php:1579
msgid "Review Categories"
msgstr ""

#: core/class-wcfmmp-settings.php:1580
#: views/reviews/wcfmmp-view-reviews-manage.php:86
msgid "Category"
msgstr ""

#: core/class-wcfmmp-settings.php:1630 helpers/class-wcfmmp-install.php:103
msgid "Vendor Registration"
msgstr ""

#: core/class-wcfmmp-settings.php:1634
msgid "Registration Settings"
msgstr ""

#: core/class-wcfmmp-settings.php:1639
msgid "Hide \"Become a Vendor\"?"
msgstr ""

#: core/class-wcfmmp-settings.php:1639
msgid "Enable this to hide `Become a Vendor` link from `My Account` page."
msgstr ""

#: core/class-wcfmmp-settings.php:1640
msgid "Required Approval"
msgstr ""

#: core/class-wcfmmp-settings.php:1640
msgid "Whether user required Admin Approval to become vendor or not!"
msgstr ""

#: core/class-wcfmmp-settings.php:1641
msgid "Email Verification"
msgstr ""

#: core/class-wcfmmp-settings.php:1646
msgid "SMS (via OTP) Verification"
msgstr ""

#: core/class-wcfmmp-settings.php:1651
msgid "Registration Form Fields"
msgstr ""

#: core/class-wcfmmp-settings.php:1660
msgid "First Name"
msgstr ""

#: core/class-wcfmmp-settings.php:1661
msgid "Last Name"
msgstr ""

#: core/class-wcfmmp-settings.php:1662
msgid "User Name"
msgstr ""

#: core/class-wcfmmp-settings.php:1663 core/class-wcfmmp-vendor.php:773
msgid "Store Address"
msgstr ""

#: core/class-wcfmmp-settings.php:1664 core/class-wcfmmp-vendor.php:723
#: helpers/class-wcfmmp-store-setup.php:420
msgid "Store Phone"
msgstr ""

#: core/class-wcfmmp-settings.php:1665
msgid "Terms & Conditions"
msgstr ""

#: core/class-wcfmmp-settings.php:1666
msgid "Terms Page"
msgstr ""

#: core/class-wcfmmp-settings.php:1671
msgid "Registration Form Custom Fields"
msgstr ""

#: core/class-wcfmmp-settings.php:1673
msgid "Field Type"
msgstr ""

#: core/class-wcfmmp-settings.php:1674
msgid "Label"
msgstr ""

#: core/class-wcfmmp-settings.php:1675
msgid "Options"
msgstr ""

#: core/class-wcfmmp-settings.php:1675
msgid "Insert option values | separated"
msgstr ""

#: core/class-wcfmmp-settings.php:1676
msgid "Content"
msgstr ""

#: core/class-wcfmmp-settings.php:1677
msgid "Help Content"
msgstr ""

#: core/class-wcfmmp-settings.php:1678
msgid "Required?"
msgstr ""

#: core/class-wcfmmp-settings.php:1684
msgid "Registration Advanced Setting"
msgstr ""

#: core/class-wcfmmp-settings.php:1743 core/class-wcfmmp-vendor.php:720
msgid "Store Name"
msgstr ""

#: core/class-wcfmmp-settings.php:1744
msgid "Header Background"
msgstr ""

#: core/class-wcfmmp-settings.php:1745
msgid "Header Social Background"
msgstr ""

#: core/class-wcfmmp-settings.php:1746
msgid "Header Text"
msgstr ""

#: core/class-wcfmmp-settings.php:1747
msgid "Header Icon"
msgstr ""

#: core/class-wcfmmp-settings.php:1748
msgid "Sidebar Background"
msgstr ""

#: core/class-wcfmmp-settings.php:1749
msgid "Sidebar Heading"
msgstr ""

#: core/class-wcfmmp-settings.php:1750
msgid "Sidebar Text"
msgstr ""

#: core/class-wcfmmp-settings.php:1751
msgid "Tabs Text"
msgstr ""

#: core/class-wcfmmp-settings.php:1752
msgid "Tabs Active Text"
msgstr ""

#: core/class-wcfmmp-settings.php:1753
msgid "Store Card Highlight Color"
msgstr ""

#: core/class-wcfmmp-settings.php:1754
msgid "Store Card Title Color"
msgstr ""

#: core/class-wcfmmp-settings.php:1755
msgid "Store Card Text Color"
msgstr ""

#: core/class-wcfmmp-settings.php:1756
msgid "Button Background"
msgstr ""

#: core/class-wcfmmp-settings.php:1757
msgid "Button Text"
msgstr ""

#: core/class-wcfmmp-settings.php:1758
msgid "Button Hover Background"
msgstr ""

#: core/class-wcfmmp-settings.php:1759
msgid "Button Hover Text"
msgstr ""

#: core/class-wcfmmp-settings.php:1760
msgid "Star Rating"
msgstr ""

#: core/class-wcfmmp-settings.php:1773
msgid "Store Style"
msgstr ""

#: core/class-wcfmmp-settings.php:1777
msgid "Page Display Setting"
msgstr ""

#: core/class-wcfmmp-settings.php:1789
msgid "Reset to Default"
msgstr ""

#: core/class-wcfmmp-settings.php:1915
msgid "Data Cleanup"
msgstr ""

#: core/class-wcfmmp-settings.php:1919
msgid "Data Cleanup Settings"
msgstr ""

#: core/class-wcfmmp-settings.php:1924
msgid "Enable Data Cleanup"
msgstr ""

#: core/class-wcfmmp-settings.php:1924
msgid ""
"Check this to enable scheduled cleanup old data. It will not possible to "
"restore this data anymore."
msgstr ""

#: core/class-wcfmmp-settings.php:1930
msgid "Notification Message Cleanup"
msgstr ""

#: core/class-wcfmmp-settings.php:1936 core/class-wcfmmp-settings.php:1965
msgid "Check this to enable scheduled cleanup old notification messages."
msgstr ""

#: core/class-wcfmmp-settings.php:1937 core/class-wcfmmp-settings.php:1951
#: core/class-wcfmmp-settings.php:1966
msgid "Cleanup data more than"
msgstr ""

#: core/class-wcfmmp-settings.php:1937 core/class-wcfmmp-settings.php:1951
#: core/class-wcfmmp-settings.php:1966
msgid "30 days old"
msgstr ""

#: core/class-wcfmmp-settings.php:1937 core/class-wcfmmp-settings.php:1951
#: core/class-wcfmmp-settings.php:1966
msgid "60 days old"
msgstr ""

#: core/class-wcfmmp-settings.php:1937 core/class-wcfmmp-settings.php:1951
#: core/class-wcfmmp-settings.php:1966
msgid "90 days old"
msgstr ""

#: core/class-wcfmmp-settings.php:1937 core/class-wcfmmp-settings.php:1951
#: core/class-wcfmmp-settings.php:1966
msgid "180 days old"
msgstr ""

#: core/class-wcfmmp-settings.php:1937 core/class-wcfmmp-settings.php:1951
#: core/class-wcfmmp-settings.php:1966
msgid "365 days old"
msgstr ""

#: core/class-wcfmmp-settings.php:1944
msgid "Inquiry Messages Cleanup"
msgstr ""

#: core/class-wcfmmp-settings.php:1950
msgid "Check this to enable scheduled cleanup old inquiry messages."
msgstr ""

#: core/class-wcfmmp-settings.php:1959
msgid "Analytics Data Cleanup"
msgstr ""

#: core/class-wcfmmp-shipping-zone.php:95
msgid "No shipping method found for adding"
msgstr ""

#: core/class-wcfmmp-shipping-zone.php:120
msgid "Shipping method not added successfully"
msgstr ""

#: core/class-wcfmmp-shipping-zone.php:141
msgid "Shipping method not deleted"
msgstr ""

#: core/class-wcfmmp-shipping-zone.php:165
msgid "Lets you charge a rate for shipping"
msgstr ""

#: core/class-wcfmmp-shipping-zone.php:234
msgid "Shipping method not updated"
msgstr ""

#: core/class-wcfmmp-shipping-zone.php:252
msgid "Method enable or disable not working"
msgstr ""

#: core/class-wcfmmp-shipping.php:424
#: views/emails/plain/store-new-order.php:240
#: views/emails/plain/store-new-order.php:378
#: views/emails/store-new-order.php:248 views/emails/store-new-order.php:386
msgid "Shipping"
msgstr ""

#: core/class-wcfmmp-shipping.php:517
msgid "Deliver upto"
msgstr ""

#: core/class-wcfmmp-shipping.php:525
msgid "Shop for %s%s more to get free shipping"
msgstr ""

#: core/class-wcfmmp-shortcode.php:949 core/class-wcfmmp-store-hours.php:78
#: core/class-wcfmmp-store-hours.php:217 core/class-wcfmmp-vendor.php:1294
#: includes/store-widgets/class-wcfmmp-widget-store-hours.php:85
msgid "Monday"
msgstr ""

#: core/class-wcfmmp-shortcode.php:949 core/class-wcfmmp-store-hours.php:78
#: core/class-wcfmmp-store-hours.php:217 core/class-wcfmmp-vendor.php:1294
#: includes/store-widgets/class-wcfmmp-widget-store-hours.php:85
msgid "Tuesday"
msgstr ""

#: core/class-wcfmmp-shortcode.php:949 core/class-wcfmmp-store-hours.php:78
#: core/class-wcfmmp-store-hours.php:217 core/class-wcfmmp-vendor.php:1294
#: includes/store-widgets/class-wcfmmp-widget-store-hours.php:85
msgid "Wednesday"
msgstr ""

#: core/class-wcfmmp-shortcode.php:949 core/class-wcfmmp-store-hours.php:78
#: core/class-wcfmmp-store-hours.php:217 core/class-wcfmmp-vendor.php:1294
#: includes/store-widgets/class-wcfmmp-widget-store-hours.php:85
msgid "Thursday"
msgstr ""

#: core/class-wcfmmp-shortcode.php:949 core/class-wcfmmp-store-hours.php:78
#: core/class-wcfmmp-store-hours.php:217 core/class-wcfmmp-vendor.php:1294
#: includes/store-widgets/class-wcfmmp-widget-store-hours.php:85
msgid "Friday"
msgstr ""

#: core/class-wcfmmp-shortcode.php:949 core/class-wcfmmp-store-hours.php:78
#: core/class-wcfmmp-store-hours.php:217 core/class-wcfmmp-vendor.php:1294
#: includes/store-widgets/class-wcfmmp-widget-store-hours.php:85
msgid "Saturday"
msgstr ""

#: core/class-wcfmmp-shortcode.php:949 core/class-wcfmmp-store-hours.php:78
#: core/class-wcfmmp-store-hours.php:217 core/class-wcfmmp-vendor.php:1294
#: includes/store-widgets/class-wcfmmp-widget-store-hours.php:85
msgid "Sunday"
msgstr ""

#: core/class-wcfmmp-shortcode.php:952 core/class-wcfmmp-store-hours.php:204
#: core/class-wcfmmp.php:349 helpers/wcfmmp-core-functions.php:570
#: includes/store-widgets/class-wcfmmp-widget-store-hours.php:17
#: includes/store-widgets/class-wcfmmp-widget-store-hours.php:136
msgid "Store Hours"
msgstr ""

#: core/class-wcfmmp-sidebar-widgets.php:41
msgid "Vendor Store Sidebar"
msgstr ""

#: core/class-wcfmmp-store-hours.php:68
msgid "Hours"
msgstr ""

#: core/class-wcfmmp-store-hours.php:73
msgid "Default Store Hours Setting"
msgstr ""

#: core/class-wcfmmp-store-hours.php:78 core/class-wcfmmp-vendor.php:1294
msgid "Set Day OFF"
msgstr ""

#: core/class-wcfmmp-store-hours.php:84 core/class-wcfmmp-store-hours.php:223
#: core/class-wcfmmp-vendor.php:1299
msgid "Daily Basis Opening & Closing Hours"
msgstr ""

#: core/class-wcfmmp-store-hours.php:89 core/class-wcfmmp-store-hours.php:228
#: core/class-wcfmmp-vendor.php:1304
msgid "Monday Time Slots"
msgstr ""

#: core/class-wcfmmp-store-hours.php:90 core/class-wcfmmp-store-hours.php:95
#: core/class-wcfmmp-store-hours.php:100 core/class-wcfmmp-store-hours.php:105
#: core/class-wcfmmp-store-hours.php:110 core/class-wcfmmp-store-hours.php:115
#: core/class-wcfmmp-store-hours.php:120 core/class-wcfmmp-store-hours.php:229
#: core/class-wcfmmp-store-hours.php:234 core/class-wcfmmp-store-hours.php:239
#: core/class-wcfmmp-store-hours.php:244 core/class-wcfmmp-store-hours.php:249
#: core/class-wcfmmp-store-hours.php:254 core/class-wcfmmp-store-hours.php:259
#: core/class-wcfmmp-vendor.php:1305 core/class-wcfmmp-vendor.php:1310
#: core/class-wcfmmp-vendor.php:1315 core/class-wcfmmp-vendor.php:1320
#: core/class-wcfmmp-vendor.php:1325 core/class-wcfmmp-vendor.php:1330
#: core/class-wcfmmp-vendor.php:1335
msgid "Opening"
msgstr ""

#: core/class-wcfmmp-store-hours.php:91 core/class-wcfmmp-store-hours.php:96
#: core/class-wcfmmp-store-hours.php:101 core/class-wcfmmp-store-hours.php:106
#: core/class-wcfmmp-store-hours.php:111 core/class-wcfmmp-store-hours.php:116
#: core/class-wcfmmp-store-hours.php:121 core/class-wcfmmp-store-hours.php:230
#: core/class-wcfmmp-store-hours.php:235 core/class-wcfmmp-store-hours.php:240
#: core/class-wcfmmp-store-hours.php:245 core/class-wcfmmp-store-hours.php:250
#: core/class-wcfmmp-store-hours.php:255 core/class-wcfmmp-store-hours.php:260
#: core/class-wcfmmp-vendor.php:1306 core/class-wcfmmp-vendor.php:1311
#: core/class-wcfmmp-vendor.php:1316 core/class-wcfmmp-vendor.php:1321
#: core/class-wcfmmp-vendor.php:1326 core/class-wcfmmp-vendor.php:1331
#: core/class-wcfmmp-vendor.php:1336
msgid "Closing"
msgstr ""

#: core/class-wcfmmp-store-hours.php:94 core/class-wcfmmp-store-hours.php:233
#: core/class-wcfmmp-vendor.php:1309
msgid "Tuesday Time Slots"
msgstr ""

#: core/class-wcfmmp-store-hours.php:99 core/class-wcfmmp-store-hours.php:238
#: core/class-wcfmmp-vendor.php:1314
msgid "Wednesday Time Slots"
msgstr ""

#: core/class-wcfmmp-store-hours.php:104 core/class-wcfmmp-store-hours.php:243
#: core/class-wcfmmp-vendor.php:1319
msgid "Thursday Time Slots"
msgstr ""

#: core/class-wcfmmp-store-hours.php:109 core/class-wcfmmp-store-hours.php:248
#: core/class-wcfmmp-vendor.php:1324
msgid "Friday Time Slots"
msgstr ""

#: core/class-wcfmmp-store-hours.php:114 core/class-wcfmmp-store-hours.php:253
#: core/class-wcfmmp-vendor.php:1329
msgid "Saturday Time Slots"
msgstr ""

#: core/class-wcfmmp-store-hours.php:119 core/class-wcfmmp-store-hours.php:258
#: core/class-wcfmmp-vendor.php:1334
msgid "Sunday Time Slots"
msgstr ""

#: core/class-wcfmmp-store-hours.php:209 core/class-wcfmmp-vendor.php:1286
msgid "Store Hours Setting"
msgstr ""

#: core/class-wcfmmp-store-hours.php:215 core/class-wcfmmp-vendor.php:1292
msgid "Enable Store Hours"
msgstr ""

#: core/class-wcfmmp-store-hours.php:216 core/class-wcfmmp-vendor.php:1293
msgid "Disable Purchase During OFF Time"
msgstr ""

#: core/class-wcfmmp-store-hours.php:217
msgid "Set Week OFF"
msgstr ""

#: core/class-wcfmmp-store-hours.php:320 core/class-wcfmmp-store-hours.php:372
msgid "This store is now closed!"
msgstr ""

#: core/class-wcfmmp-store.php:132
msgid "Products"
msgstr ""

#: core/class-wcfmmp-store.php:133
msgid "Articles"
msgstr ""

#: core/class-wcfmmp-store.php:134
msgid "About"
msgstr ""

#: core/class-wcfmmp-store.php:135 core/class-wcfmmp-vendor.php:2231
#: helpers/class-wcfmmp-store-setup.php:67
msgid "Policies"
msgstr ""

#: core/class-wcfmmp-store.php:137 core/class-wcfmmp-store.php:149
msgid "Followers"
msgstr ""

#: core/class-wcfmmp-store.php:138 core/class-wcfmmp-store.php:155
msgid "Followings"
msgstr ""

#: core/class-wcfmmp-store.php:620 core/class-wcfmmp-vendor.php:1667
#: helpers/class-wcfmmp-store-setup.php:792
msgid "Phone"
msgstr ""

#: core/class-wcfmmp-vendor.php:425 core/class-wcfmmp-vendor.php:537
#: helpers/wcfmmp-core-functions.php:499 helpers/wcfmmp-core-functions.php:650
msgid "Shipped"
msgstr ""

#: core/class-wcfmmp-vendor.php:536 helpers/wcfmmp-core-functions.php:649
#: views/ledger/wcfmmp-view-ledger.php:46
#: views/reviews/wcfmmp-view-product-reviews.php:22
#: views/reviews/wcfmmp-view-reviews.php:22
msgid "Pending"
msgstr ""

#: core/class-wcfmmp-vendor.php:701 helpers/class-wcfmmp-store-setup.php:403
msgid "Upload a banner for your store. Banner size is (%sx%s) pixels."
msgstr ""

#: core/class-wcfmmp-vendor.php:708
msgid "Store Settings"
msgstr ""

#: core/class-wcfmmp-vendor.php:714
msgid "General Setting"
msgstr ""

#: core/class-wcfmmp-vendor.php:721
msgid "Store Slug"
msgstr ""

#: core/class-wcfmmp-vendor.php:722 helpers/class-wcfmmp-store-setup.php:419
msgid "Store Email"
msgstr ""

#: core/class-wcfmmp-vendor.php:730
msgid "Store Brand Setup"
msgstr ""

#: core/class-wcfmmp-vendor.php:735 core/class-wcfmmp-vendor.php:736
#: core/class-wcfmmp-vendor.php:739
msgid "Static Image"
msgstr ""

#: core/class-wcfmmp-vendor.php:735 core/class-wcfmmp-vendor.php:739
#: core/class-wcfmmp-vendor.php:742
msgid "Slider"
msgstr ""

#: core/class-wcfmmp-vendor.php:735 core/class-wcfmmp-vendor.php:736
#: core/class-wcfmmp-vendor.php:739
msgid "Video"
msgstr ""

#: core/class-wcfmmp-vendor.php:738 helpers/class-wcfmmp-store-setup.php:416
msgid "Store Logo"
msgstr ""

#: core/class-wcfmmp-vendor.php:738
msgid "Preferred  size is (125x125) pixels."
msgstr ""

#: core/class-wcfmmp-vendor.php:739
msgid "Store Banner Type"
msgstr ""

#: core/class-wcfmmp-vendor.php:740 helpers/class-wcfmmp-store-setup.php:417
msgid "Banner"
msgstr ""

#: core/class-wcfmmp-vendor.php:741
msgid "Video Banner"
msgstr ""

#: core/class-wcfmmp-vendor.php:741 core/class-wcfmmp-vendor.php:753
msgid "Insert YouTube video URL."
msgstr ""

#: core/class-wcfmmp-vendor.php:744
msgid "Slider Hyperlink"
msgstr ""

#: core/class-wcfmmp-vendor.php:748
msgid "Mobile Banner"
msgstr ""

#: core/class-wcfmmp-vendor.php:748
msgid "This Banner will be visible when someone browse store from Mobile."
msgstr ""

#: core/class-wcfmmp-vendor.php:750
msgid "Store List Banner Type"
msgstr ""

#: core/class-wcfmmp-vendor.php:751
msgid "Store List Banner"
msgstr ""

#: core/class-wcfmmp-vendor.php:751
msgid "This Banner will be visible at Store List Page."
msgstr ""

#: core/class-wcfmmp-vendor.php:753
msgid "Store List Video Banner"
msgstr ""

#: core/class-wcfmmp-vendor.php:755 helpers/class-wcfmmp-store-setup.php:473
msgid "Shop Description"
msgstr ""

#: core/class-wcfmmp-vendor.php:755 helpers/class-wcfmmp-store-setup.php:473
msgid "This is displayed on your shop page."
msgstr ""

#: core/class-wcfmmp-vendor.php:766 core/class-wcfmmp-vendor.php:826
#: core/class-wcfmmp-vendor.php:858 core/class-wcfmmp-vendor.php:904
#: core/class-wcfmmp-vendor.php:1070 core/class-wcfmmp-vendor.php:1106
#: core/class-wcfmmp-vendor.php:1159 core/class-wcfmmp-vendor.php:1210
#: core/class-wcfmmp-vendor.php:1348 core/class-wcfmmp-vendor.php:1379
#: core/class-wcfmmp-vendor.php:1504 core/class-wcfmmp-vendor.php:1537
#: core/class-wcfmmp-vendor.php:1652 core/class-wcfmmp-vendor.php:1687
msgid "Update"
msgstr ""

#: core/class-wcfmmp-vendor.php:779
msgid "Street"
msgstr ""

#: core/class-wcfmmp-vendor.php:779 helpers/class-wcfmmp-store-setup.php:447
msgid "Street address"
msgstr ""

#: core/class-wcfmmp-vendor.php:780
msgid "Street 2"
msgstr ""

#: core/class-wcfmmp-vendor.php:780 helpers/class-wcfmmp-store-setup.php:448
msgid "Apartment, suite, unit etc. (optional)"
msgstr ""

#: core/class-wcfmmp-vendor.php:781 core/class-wcfmmp-vendor.php:1672
#: helpers/class-wcfmmp-store-setup.php:797
msgid "City/Town"
msgstr ""

#: core/class-wcfmmp-vendor.php:781 helpers/class-wcfmmp-store-setup.php:449
msgid "Town / City"
msgstr ""

#: core/class-wcfmmp-vendor.php:782 core/class-wcfmmp-vendor.php:1674
#: helpers/class-wcfmmp-store-setup.php:799
msgid "Postcode/Zip"
msgstr ""

#: core/class-wcfmmp-vendor.php:782 helpers/class-wcfmmp-store-setup.php:450
msgid "Postcode / Zip"
msgstr ""

#: core/class-wcfmmp-vendor.php:784 core/class-wcfmmp-vendor.php:1673
#: helpers/class-wcfmmp-store-setup.php:798
msgid "State/County"
msgstr ""

#: core/class-wcfmmp-vendor.php:801 helpers/class-wcfmmp-store-setup.php:461
#: helpers/wcfmmp-core-functions.php:566
#: includes/store-widgets/class-wcfmmp-widget-store-location.php:17
#: includes/store-widgets/class-wcfmmp-widget-store-location.php:117
#: views/store/wcfmmp-view-store-sidebar.php:36
msgid "Store Location"
msgstr ""

#: core/class-wcfmmp-vendor.php:806 helpers/class-wcfmmp-store-setup.php:460
msgid "Find Location"
msgstr ""

#: core/class-wcfmmp-vendor.php:833
msgid "Visibility Setup"
msgstr ""

#: core/class-wcfmmp-vendor.php:839
msgid "Store name position at you Store Page."
msgstr ""

#: core/class-wcfmmp-vendor.php:840
msgid "No of products at you Store Page."
msgstr ""

#: core/class-wcfmmp-vendor.php:841
msgid "Hide Email from Store"
msgstr ""

#: core/class-wcfmmp-vendor.php:842
msgid "Hide Phone from Store"
msgstr ""

#: core/class-wcfmmp-vendor.php:843
msgid "Hide Address from Store"
msgstr ""

#: core/class-wcfmmp-vendor.php:844
msgid "Hide Map from Store"
msgstr ""

#: core/class-wcfmmp-vendor.php:845
msgid "Hide About from Store"
msgstr ""

#: core/class-wcfmmp-vendor.php:846
msgid "Hide Policy from Store"
msgstr ""

#: core/class-wcfmmp-vendor.php:973 core/class-wcfmmp-vendor.php:998
msgid "Vendor Specific Rule"
msgstr ""

#: core/class-wcfmmp-vendor.php:1008
msgid "Commission & Withdrawal"
msgstr ""

#: core/class-wcfmmp-vendor.php:1016
msgid "Commission Setup"
msgstr ""

#: core/class-wcfmmp-vendor.php:1055
msgid "Enable this to deduct tax from vendor's commission."
msgstr ""

#: core/class-wcfmmp-vendor.php:1083
msgid "Transactional Charge Mode"
msgstr ""

#: core/class-wcfmmp-vendor.php:1113
msgid "Withdrawal Setup"
msgstr ""

#: core/class-wcfmmp-vendor.php:1165
msgid "Payment Setup"
msgstr ""

#: core/class-wcfmmp-vendor.php:1171
msgid "Choose Withdrawal Payment Method"
msgstr ""

#: core/class-wcfmmp-vendor.php:1173
msgid "Prefered Payment Method"
msgstr ""

#: core/class-wcfmmp-vendor.php:1174 helpers/class-wcfmmp-store-setup.php:528
msgid "PayPal Email"
msgstr ""

#: core/class-wcfmmp-vendor.php:1175 helpers/class-wcfmmp-store-setup.php:529
msgid "Skrill Email"
msgstr ""

#: core/class-wcfmmp-vendor.php:1184
msgid "Bank Details"
msgstr ""

#: core/class-wcfmmp-vendor.php:1189 helpers/class-wcfmmp-store-setup.php:536
msgid "Account Name"
msgstr ""

#: core/class-wcfmmp-vendor.php:1189 helpers/class-wcfmmp-store-setup.php:536
msgid "Your bank account name"
msgstr ""

#: core/class-wcfmmp-vendor.php:1190 helpers/class-wcfmmp-store-setup.php:537
msgid "Account Number"
msgstr ""

#: core/class-wcfmmp-vendor.php:1190 helpers/class-wcfmmp-store-setup.php:537
msgid "Your bank account number"
msgstr ""

#: core/class-wcfmmp-vendor.php:1191 helpers/class-wcfmmp-store-setup.php:538
msgid "Bank Name"
msgstr ""

#: core/class-wcfmmp-vendor.php:1191 helpers/class-wcfmmp-store-setup.php:538
msgid "Name of bank"
msgstr ""

#: core/class-wcfmmp-vendor.php:1192 helpers/class-wcfmmp-store-setup.php:539
msgid "Bank Address"
msgstr ""

#: core/class-wcfmmp-vendor.php:1192 helpers/class-wcfmmp-store-setup.php:539
msgid "Address of your bank"
msgstr ""

#: core/class-wcfmmp-vendor.php:1193 helpers/class-wcfmmp-store-setup.php:540
msgid "Routing Number"
msgstr ""

#: core/class-wcfmmp-vendor.php:1193 helpers/class-wcfmmp-store-setup.php:540
msgid "Routing number"
msgstr ""

#: core/class-wcfmmp-vendor.php:1194 helpers/class-wcfmmp-store-setup.php:541
msgid "IBAN"
msgstr ""

#: core/class-wcfmmp-vendor.php:1195 helpers/class-wcfmmp-store-setup.php:542
msgid "Swift Code"
msgstr ""

#: core/class-wcfmmp-vendor.php:1195 helpers/class-wcfmmp-store-setup.php:542
msgid "Swift code"
msgstr ""

#: core/class-wcfmmp-vendor.php:1196 helpers/class-wcfmmp-store-setup.php:543
msgid "IFSC Code"
msgstr ""

#: core/class-wcfmmp-vendor.php:1196 helpers/class-wcfmmp-store-setup.php:543
msgid "IFSC code"
msgstr ""

#: core/class-wcfmmp-vendor.php:1279
msgid "Store Hours & Vacation"
msgstr ""

#: core/class-wcfmmp-vendor.php:1357
msgid "Vacation Mode"
msgstr ""

#: core/class-wcfmmp-vendor.php:1363
msgid "Enable Vacation Mode"
msgstr ""

#: core/class-wcfmmp-vendor.php:1364
msgid "Disable Purchase During Vacation"
msgstr ""

#: core/class-wcfmmp-vendor.php:1365
msgid "Vacation Type"
msgstr ""

#: core/class-wcfmmp-vendor.php:1365
msgid "Instantly Close"
msgstr ""

#: core/class-wcfmmp-vendor.php:1365
msgid "Date wise close"
msgstr ""

#: core/class-wcfmmp-vendor.php:1366
msgid "From"
msgstr ""

#: core/class-wcfmmp-vendor.php:1367
msgid "Upto"
msgstr ""

#: core/class-wcfmmp-vendor.php:1367
#: includes/sms-gateways/class-wcfmmp-twilio-sms-notification.php:307
msgid "To"
msgstr ""

#: core/class-wcfmmp-vendor.php:1368
msgid "Vacation Message"
msgstr ""

#: core/class-wcfmmp-vendor.php:1446
msgid "Store SEO & Social"
msgstr ""

#: core/class-wcfmmp-vendor.php:1453
msgid "General Setup"
msgstr ""

#: core/class-wcfmmp-vendor.php:1458 helpers/class-wcfmmp-store-setup.php:852
msgid "SEO Title"
msgstr ""

#: core/class-wcfmmp-vendor.php:1458 helpers/class-wcfmmp-store-setup.php:852
msgid "SEO Title is shown as the title of your store page"
msgstr ""

#: core/class-wcfmmp-vendor.php:1459 helpers/class-wcfmmp-store-setup.php:853
msgid "Meta Description"
msgstr ""

#: core/class-wcfmmp-vendor.php:1459 helpers/class-wcfmmp-store-setup.php:853
msgid ""
"The meta description is often shown as the black text under the title in a "
"search result. For this to work it has to contain the keyword that was "
"searched for and should be less than 156 chars."
msgstr ""

#: core/class-wcfmmp-vendor.php:1460 helpers/class-wcfmmp-store-setup.php:854
msgid "Meta Keywords"
msgstr ""

#: core/class-wcfmmp-vendor.php:1460 helpers/class-wcfmmp-store-setup.php:854
msgid "Insert some comma separated keywords for better ranking of your store page."
msgstr ""

#: core/class-wcfmmp-vendor.php:1468 helpers/class-wcfmmp-store-setup.php:862
msgid "Facebook Setup"
msgstr ""

#: core/class-wcfmmp-vendor.php:1473 helpers/class-wcfmmp-store-setup.php:867
msgid "Facebook Title"
msgstr ""

#: core/class-wcfmmp-vendor.php:1474 helpers/class-wcfmmp-store-setup.php:868
msgid "Facebook Description"
msgstr ""

#: core/class-wcfmmp-vendor.php:1475 helpers/class-wcfmmp-store-setup.php:869
msgid "Facebook Image"
msgstr ""

#: core/class-wcfmmp-vendor.php:1484 helpers/class-wcfmmp-store-setup.php:878
msgid "Twitter Setup"
msgstr ""

#: core/class-wcfmmp-vendor.php:1489 helpers/class-wcfmmp-store-setup.php:883
msgid "Twitter Title"
msgstr ""

#: core/class-wcfmmp-vendor.php:1490 helpers/class-wcfmmp-store-setup.php:884
msgid "Twitter Description"
msgstr ""

#: core/class-wcfmmp-vendor.php:1491 helpers/class-wcfmmp-store-setup.php:885
msgid "Twitter Image"
msgstr ""

#: core/class-wcfmmp-vendor.php:1513
msgid "Social Profile"
msgstr ""

#: core/class-wcfmmp-vendor.php:1519 helpers/class-wcfmmp-store-setup.php:930
msgid "Twitter"
msgstr ""

#: core/class-wcfmmp-vendor.php:1520 helpers/class-wcfmmp-store-setup.php:931
msgid "Facebook"
msgstr ""

#: core/class-wcfmmp-vendor.php:1521 helpers/class-wcfmmp-store-setup.php:932
msgid "Instagram"
msgstr ""

#: core/class-wcfmmp-vendor.php:1522 helpers/class-wcfmmp-store-setup.php:933
msgid "Youtube"
msgstr ""

#: core/class-wcfmmp-vendor.php:1523 helpers/class-wcfmmp-store-setup.php:934
msgid "Linkedin"
msgstr ""

#: core/class-wcfmmp-vendor.php:1524 helpers/class-wcfmmp-store-setup.php:935
msgid "Google Plus"
msgstr ""

#: core/class-wcfmmp-vendor.php:1525 helpers/class-wcfmmp-store-setup.php:936
msgid "Snapchat"
msgstr ""

#: core/class-wcfmmp-vendor.php:1526 helpers/class-wcfmmp-store-setup.php:937
msgid "Pinterest"
msgstr ""

#: core/class-wcfmmp-vendor.php:1614
msgid "Store Policies & Customer Support"
msgstr ""

#: core/class-wcfmmp-vendor.php:1616
msgid "Policies & Support"
msgstr ""

#: core/class-wcfmmp-vendor.php:1626
msgid "Policies Setting"
msgstr ""

#: core/class-wcfmmp-vendor.php:1638 helpers/class-wcfmmp-store-setup.php:726
msgid "Policy Tab Label"
msgstr ""

#: core/class-wcfmmp-vendor.php:1639 core/class-wcfmmp-vendor.php:2241
#: helpers/class-wcfmmp-store-setup.php:740
#: views/store/wcfmmp-view-store-policies.php:39
msgid "Shipping Policy"
msgstr ""

#: core/class-wcfmmp-vendor.php:1640 core/class-wcfmmp-vendor.php:2247
#: helpers/class-wcfmmp-store-setup.php:741
#: views/store/wcfmmp-view-store-policies.php:44
msgid "Refund Policy"
msgstr ""

#: core/class-wcfmmp-vendor.php:1641 helpers/class-wcfmmp-store-setup.php:742
msgid "Cancellation/Return/Exchange Policy"
msgstr ""

#: core/class-wcfmmp-vendor.php:1661 core/class-wcfmmp-vendor.php:2264
#: helpers/class-wcfmmp-store-setup.php:72
msgid "Customer Support"
msgstr ""

#: core/class-wcfmmp-vendor.php:1669 helpers/class-wcfmmp-store-setup.php:794
msgid "Address 1"
msgstr ""

#: core/class-wcfmmp-vendor.php:1670 helpers/class-wcfmmp-store-setup.php:795
msgid "Address 2"
msgstr ""

#: core/class-wcfmmp-vendor.php:1722
msgid "Store Orders"
msgstr ""

#: core/class-wcfmmp-vendor.php:1733
msgid "Show all"
msgstr ""

#: core/class-wcfmmp-vendor.php:1734
msgid "Unpaid"
msgstr ""

#: core/class-wcfmmp-vendor.php:1735 helpers/wcfmmp-core-functions.php:653
#: views/refund/wcfmmp-view-refund-requests.php:41
msgid "Requested"
msgstr ""

#: core/class-wcfmmp-vendor.php:1736
msgid "Paid"
msgstr ""

#: core/class-wcfmmp-vendor.php:1737 helpers/wcfmmp-core-functions.php:652
#: views/ledger/wcfmmp-view-ledger.php:48
#: views/refund/wcfmmp-view-refund-requests.php:42
msgid "Cancelled"
msgstr ""

#: core/class-wcfmmp-vendor.php:1748 core/class-wcfmmp-vendor.php:1779
msgid "Purchased"
msgstr ""

#: core/class-wcfmmp-vendor.php:1749 core/class-wcfmmp-vendor.php:1780
msgid "Quantity"
msgstr ""

#: core/class-wcfmmp-vendor.php:1750 core/class-wcfmmp-vendor.php:1781
msgid "Billing Address"
msgstr ""

#: core/class-wcfmmp-vendor.php:1751 core/class-wcfmmp-vendor.php:1782
msgid "Shipping Address"
msgstr ""

#: core/class-wcfmmp-vendor.php:1752 core/class-wcfmmp-vendor.php:1783
msgid "Gross Sales"
msgstr ""

#: core/class-wcfmmp-vendor.php:1753 core/class-wcfmmp-vendor.php:1784
msgid "Gross Sales Amount"
msgstr ""

#: core/class-wcfmmp-vendor.php:1755 core/class-wcfmmp-vendor.php:1762
#: core/class-wcfmmp-vendor.php:1786 core/class-wcfmmp-vendor.php:1793
msgid "Fees"
msgstr ""

#: core/class-wcfmmp-vendor.php:1757 core/class-wcfmmp-vendor.php:1764
#: core/class-wcfmmp-vendor.php:1788 core/class-wcfmmp-vendor.php:1795
msgid "Earnings"
msgstr ""

#: core/class-wcfmmp-vendor.php:1762 core/class-wcfmmp-vendor.php:1764
#: core/class-wcfmmp-vendor.php:1766 core/class-wcfmmp-vendor.php:1793
#: core/class-wcfmmp-vendor.php:1795 core/class-wcfmmp-vendor.php:1797
msgid " Amount"
msgstr ""

#: core/class-wcfmmp-vendor.php:1768 core/class-wcfmmp-vendor.php:1799
#: core/class-wcfmmp-vendor.php:1909 core/class-wcfmmp-vendor.php:1915
msgid "Additional Info"
msgstr ""

#: core/class-wcfmmp-vendor.php:1770 core/class-wcfmmp-vendor.php:1801
#: views/refund/wcfmmp-view-refund-requests.php:68
#: views/refund/wcfmmp-view-refund-requests.php:81
msgid "Date"
msgstr ""

#: core/class-wcfmmp-vendor.php:1771 core/class-wcfmmp-vendor.php:1802
#: views/media/wcfmmp-view-media.php:72 views/media/wcfmmp-view-media.php:85
#: views/product_multivendor/wcfmmp-view-sell-items-catalog.php:119
#: views/product_multivendor/wcfmmp-view-sell-items-catalog.php:133
#: views/reviews/wcfmmp-view-product-reviews.php:99
#: views/reviews/wcfmmp-view-product-reviews.php:111
#: views/reviews/wcfmmp-view-reviews.php:97
#: views/reviews/wcfmmp-view-reviews.php:109
msgid "Actions"
msgstr ""

#: core/class-wcfmmp-vendor.php:1829
msgid "— No change —"
msgstr ""

#: core/class-wcfmmp-vendor.php:1987
#: includes/store-widgets/class-wcfmmp-widget-store-lists-meta-filter.php:98
msgid "--Choose Option--"
msgstr ""

#: core/class-wcfmmp-vendor.php:2253
#: views/store/wcfmmp-view-store-policies.php:49
msgid "Cancellation / Return / Exchange Policy"
msgstr ""

#: core/class-wcfmmp-vendor.php:2324
msgid "Off-line Vendor Store"
msgstr ""

#: core/class-wcfmmp-vendor.php:2326
msgid "On-line Vendor Store"
msgstr ""

#: core/class-wcfmmp-vendor.php:2580
msgid "Add Store Logo"
msgstr ""

#: core/class-wcfmmp-vendor.php:2588
msgid "Add Store Name"
msgstr ""

#: core/class-wcfmmp-vendor.php:2596
msgid "Add Store Banner"
msgstr ""

#: core/class-wcfmmp-vendor.php:2604
msgid "Add Store Phone"
msgstr ""

#: core/class-wcfmmp-vendor.php:2612
msgid "Add Store Description"
msgstr ""

#: core/class-wcfmmp-vendor.php:2620
msgid "Add Store Address"
msgstr ""

#: core/class-wcfmmp-vendor.php:2630
msgid "Add Store Location"
msgstr ""

#: core/class-wcfmmp-vendor.php:2639
msgid "Set your payment method"
msgstr ""

#: core/class-wcfmmp-vendor.php:2647
msgid "Setup Store Policies"
msgstr ""

#: core/class-wcfmmp-vendor.php:2655
msgid "Setup Store Customer Support"
msgstr ""

#: core/class-wcfmmp-vendor.php:2663
msgid "Setup Store SEO"
msgstr ""

#: core/class-wcfmmp-vendor.php:2678
msgid "Complete!"
msgstr ""

#: core/class-wcfmmp-vendor.php:2681
msgid "Loading"
msgstr ""

#: core/class-wcfmmp-vendor.php:2684
msgid "Suggestion(s)"
msgstr ""

#: core/class-wcfmmp-vendor.php:2701
msgid "<b>%s</b> is low in stock. There are %s left."
msgstr ""

#: core/class-wcfmmp-vendor.php:3089
msgid ""
"Item(s) from one store already in your cart. First checkout with those and "
"then purchase from other stores!"
msgstr ""

#: core/class-wcfmmp-vendor.php:3131
msgid ""
"Item(s) from one store already in your quote list. First send request for "
"those."
msgstr ""

#: core/class-wcfmmp-withdraw.php:154
msgid "Auto Withdrawal Request processing failed, please contact Store Admin."
msgstr ""

#: core/class-wcfmmp-withdraw.php:159 core/class-wcfmmp.php:495
msgid "Vendor <b>%s</b> has placed a Withdrawal Request #%s."
msgstr ""

#: core/class-wcfmmp-withdraw.php:181
msgid "Auto withdrawal request failed, please try after sometime."
msgstr ""

#: core/class-wcfmmp-withdraw.php:232 core/class-wcfmmp-withdraw.php:247
msgid "Cancelled due to refund!"
msgstr ""

#: core/class-wcfmmp-withdraw.php:431
msgid "Payment Processed"
msgstr ""

#: core/class-wcfmmp-withdraw.php:460
#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:783
#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:925
msgid "Something went wrong please try again later."
msgstr ""

#: core/class-wcfmmp-withdraw.php:464
msgid "Invalid payment method."
msgstr ""

#: core/class-wcfmmp-withdraw.php:468
msgid "No vendor for payment processing."
msgstr ""

#: core/class-wcfmmp-withdraw.php:510 core/class-wcfmmp-withdraw.php:571
msgid "Your withdrawal request #%s %s."
msgstr ""

#: core/class-wcfmmp-withdraw.php:637 core/class-wcfmmp-withdraw.php:698
msgid "Reverse withdrawal for order #%s %s."
msgstr ""

#: core/class-wcfmmp.php:351
msgid "Vendor Ledger"
msgstr ""

#: core/class-wcfmmp.php:352
msgid "Product Multivendor"
msgstr ""

#: core/class-wcfmmp.php:352
msgid ""
"Keep this enable to allow vendors to sell other vendors' products, single "
"product multiple seller."
msgstr ""

#: core/class-wcfmmp.php:353
#: views/product_multivendor/wcfmmp-view-sell-items-catalog.php:15
#: views/product_multivendor/wcfmmp-view-sell-items-catalog.php:23
msgid "Add to My Store Catalog"
msgstr ""

#: core/class-wcfmmp.php:353
msgid ""
"Other vendors' products catalog, vendors will able to add those directly to "
"their store."
msgstr ""

#: helpers/class-wcfmmp-install.php:102
msgid "Vendor Membership"
msgstr ""

#: helpers/class-wcfmmp-install.php:366
msgid "Store Vendor"
msgstr ""

#: helpers/class-wcfmmp-setup.php:90 helpers/class-wcfmmp-setup.php:302
#: helpers/class-wcfmmp-setup.php:514
msgid "WCFM Marketplace &rsaquo; Setup Wizard"
msgstr ""

#: helpers/class-wcfmmp-setup.php:161
msgid "WCFM Marketplace requires WooCommerce plugin to be active!"
msgstr ""

#: helpers/class-wcfmmp-setup.php:163
msgid "Install WooCommerce"
msgstr ""

#: helpers/class-wcfmmp-setup.php:257 helpers/class-wcfmmp-setup.php:469
#: helpers/class-wcfmmp-setup.php:681
msgid ""
"%1$s could not be installed (%2$s). <a href=\"%3$s\">Please install it "
"manually by clicking here.</a>"
msgstr ""

#: helpers/class-wcfmmp-setup.php:277 helpers/class-wcfmmp-setup.php:489
#: helpers/class-wcfmmp-setup.php:701
msgid ""
"%1$s was installed but could not be activated. <a href=\"%2$s\">Please "
"activate it manually by clicking here.</a>"
msgstr ""

#: helpers/class-wcfmmp-setup.php:373
msgid "Setup WCFM Maketplace vendor registration:"
msgstr ""

#: helpers/class-wcfmmp-setup.php:375
msgid "Setup Registration"
msgstr ""

#: helpers/class-wcfmmp-setup.php:585
msgid "WCFM Maketplace requires WCFM Core plugin to be active!"
msgstr ""

#: helpers/class-wcfmmp-setup.php:587
msgid "Install WCFM Core"
msgstr ""

#: helpers/class-wcfmmp-store-setup.php:52
msgid "Introduction"
msgstr ""

#: helpers/class-wcfmmp-store-setup.php:62
msgid "Payment"
msgstr ""

#: helpers/class-wcfmmp-store-setup.php:77
msgid "SEO"
msgstr ""

#: helpers/class-wcfmmp-store-setup.php:82
msgid "Social"
msgstr ""

#: helpers/class-wcfmmp-store-setup.php:87
msgid "Ready!"
msgstr ""

#: helpers/class-wcfmmp-store-setup.php:273
msgid "Vendor Store &rsaquo; Setup Wizard"
msgstr ""

#: helpers/class-wcfmmp-store-setup.php:297
msgid "Store Setup"
msgstr ""

#: helpers/class-wcfmmp-store-setup.php:338
msgid "Welcome to %s!"
msgstr ""

#: helpers/class-wcfmmp-store-setup.php:339
msgid ""
"Thank you for choosing %s! This quick setup wizard will help you to "
"configure the basic settings and you will have your store ready in no time."
msgstr ""

#: helpers/class-wcfmmp-store-setup.php:340
msgid ""
"If you don't want to go through the wizard right now, you can skip and "
"return to the dashboard. You may setup your store from dashboard &rsaquo; "
"setting anytime!"
msgstr ""

#: helpers/class-wcfmmp-store-setup.php:342
msgid "Let's go!"
msgstr ""

#: helpers/class-wcfmmp-store-setup.php:344
msgid "Not right now"
msgstr ""

#: helpers/class-wcfmmp-store-setup.php:411
msgid "Store setup"
msgstr ""

#: helpers/class-wcfmmp-store-setup.php:418
msgid "Shop Name"
msgstr ""

#: helpers/class-wcfmmp-store-setup.php:447
msgid "Store Address 1"
msgstr ""

#: helpers/class-wcfmmp-store-setup.php:448
msgid "Store Address 2"
msgstr ""

#: helpers/class-wcfmmp-store-setup.php:449
msgid "Store City/Town"
msgstr ""

#: helpers/class-wcfmmp-store-setup.php:450
msgid "Store Postcode/Zip"
msgstr ""

#: helpers/class-wcfmmp-store-setup.php:451
msgid "Store Country"
msgstr ""

#: helpers/class-wcfmmp-store-setup.php:452
msgid "Store State/County"
msgstr ""

#: helpers/class-wcfmmp-store-setup.php:478
#: helpers/class-wcfmmp-store-setup.php:684
#: helpers/class-wcfmmp-store-setup.php:746
#: helpers/class-wcfmmp-store-setup.php:805
#: helpers/class-wcfmmp-store-setup.php:893
#: helpers/class-wcfmmp-store-setup.php:942
msgid "Continue"
msgstr ""

#: helpers/class-wcfmmp-store-setup.php:480
#: helpers/class-wcfmmp-store-setup.php:686
#: helpers/class-wcfmmp-store-setup.php:748
#: helpers/class-wcfmmp-store-setup.php:807
#: helpers/class-wcfmmp-store-setup.php:895
#: helpers/class-wcfmmp-store-setup.php:944
msgid "Skip this step"
msgstr ""

#: helpers/class-wcfmmp-store-setup.php:520
msgid "Payment setup"
msgstr ""

#: helpers/class-wcfmmp-store-setup.php:527
msgid "Preferred Payment Method"
msgstr ""

#: helpers/class-wcfmmp-store-setup.php:616
#: helpers/class-wcfmmp-store-setup.php:663
#: helpers/wcfmmp-core-functions.php:432 helpers/wcfmmp-core-functions.php:451
msgid "Stripe"
msgstr ""

#: helpers/class-wcfmmp-store-setup.php:619
msgid "You are connected with Stripe"
msgstr ""

#: helpers/class-wcfmmp-store-setup.php:665
msgid "You are not connected with stripe."
msgstr ""

#: helpers/class-wcfmmp-store-setup.php:721
msgid "Policy setup"
msgstr ""

#: helpers/class-wcfmmp-store-setup.php:787
msgid "Support setup"
msgstr ""

#: helpers/class-wcfmmp-store-setup.php:847
msgid "Store SEO setup"
msgstr ""

#: helpers/class-wcfmmp-store-setup.php:925
msgid "Store Social setup"
msgstr ""

#: helpers/class-wcfmmp-store-setup.php:960
msgid ""
"Your store is ready. It's time to experience the things more Easily and "
"Peacefully. Add your products and start counting sales, have fun!!"
msgstr ""

#: helpers/class-wcfmmp-store-setup.php:1211
msgid "How to use dashboard?"
msgstr ""

#: helpers/wcfmmp-core-functions.php:6
msgid ""
"%sWCFM Marketplace is inactive.%s The %sWooCommerce plugin%s must be active "
"for the WCFM Marketplace to work. Please %sinstall & activate WooCommerce%s"
msgstr ""

#: helpers/wcfmmp-core-functions.php:16
msgid ""
"%sWCFM Marketplace is inactive.%s The %sWooCommerce Frontend Manager%s must "
"be active for the WCFM Marketplace to work. Please %sinstall & activate "
"WooCommerce Frontend Manager%s"
msgstr ""

#: helpers/wcfmmp-core-functions.php:26
msgid ""
"%WCFM Marketplace - Stripe Gateway%s requires PHP 5.6 or greater. We "
"recommend upgrading to PHP %s or greater."
msgstr ""

#: helpers/wcfmmp-core-functions.php:36 helpers/wcfmmp-core-functions.php:46
#: helpers/wcfmmp-core-functions.php:56
msgid ""
"%WCFM Marketplace - Stripe Gateway depends on the %s PHP extension. Please "
"enable it, or ask your hosting provider to enable it."
msgstr ""

#: helpers/wcfmmp-core-functions.php:416
msgid "By Vendor Sales"
msgstr ""

#: helpers/wcfmmp-core-functions.php:417
msgid "By Product Price"
msgstr ""

#: helpers/wcfmmp-core-functions.php:418
msgid "By Purchase Quantity"
msgstr ""

#: helpers/wcfmmp-core-functions.php:427
#: includes/payment-gateways/class-wcfmmp-gateway-paypal.php:27
msgid "PayPal"
msgstr ""

#: helpers/wcfmmp-core-functions.php:428
#: includes/payment-gateways/class-wcfmmp-gateway-skrill.php:17
msgid "Skrill"
msgstr ""

#: helpers/wcfmmp-core-functions.php:429
#: includes/payment-gateways/class-wcfmmp-gateway-bank_transfer.php:16
msgid "Bank Transfer"
msgstr ""

#: helpers/wcfmmp-core-functions.php:430
#: includes/payment-gateways/class-wcfmmp-gateway-by_cash.php:16
msgid "Cash Pay"
msgstr ""

#: helpers/wcfmmp-core-functions.php:431
msgid "Wirecard (Moip)"
msgstr ""

#: helpers/wcfmmp-core-functions.php:433
#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:496
#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:613
#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:768
#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:910
msgid "Stripe Split Pay"
msgstr ""

#: helpers/wcfmmp-core-functions.php:553
msgid "Feature"
msgstr ""

#: helpers/wcfmmp-core-functions.php:554
msgid "Varity"
msgstr ""

#: helpers/wcfmmp-core-functions.php:555
msgid "Flexibility"
msgstr ""

#: helpers/wcfmmp-core-functions.php:556
msgid "Delivery"
msgstr ""

#: helpers/wcfmmp-core-functions.php:567
#: includes/store-widgets/class-wcfmmp-widget-store-info.php:17
msgid "Store Info"
msgstr ""

#: helpers/wcfmmp-core-functions.php:568
msgid "Store Category"
msgstr ""

#: helpers/wcfmmp-core-functions.php:569
#: includes/store-widgets/class-wcfmmp-widget-store-taxonomy.php:17
#: includes/store-widgets/class-wcfmmp-widget-store-taxonomy.php:118
msgid "Store Taxonomies"
msgstr ""

#: helpers/wcfmmp-core-functions.php:571
#: includes/store-widgets/class-wcfmmp-widget-store-shipping-rules.php:17
msgid "Store Shipping Rules"
msgstr ""

#: helpers/wcfmmp-core-functions.php:572
#: includes/store-widgets/class-wcfmmp-widget-store-coupons.php:17
#: includes/store-widgets/class-wcfmmp-widget-store-coupons.php:151
msgid "Store Coupons"
msgstr ""

#: helpers/wcfmmp-core-functions.php:573
#: includes/store-widgets/class-wcfmmp-widget-store-product-search.php:17
msgid "Store Product Search"
msgstr ""

#: helpers/wcfmmp-core-functions.php:574
msgid "Store Top Products"
msgstr ""

#: helpers/wcfmmp-core-functions.php:575
#: includes/store-widgets/class-wcfmmp-widget-store-top-rated-products.php:17
msgid "Store Top Rated Products"
msgstr ""

#: helpers/wcfmmp-core-functions.php:576
#: includes/store-widgets/class-wcfmmp-widget-store-recent-products.php:17
msgid "Store Recent Products"
msgstr ""

#: helpers/wcfmmp-core-functions.php:577
#: includes/store-widgets/class-wcfmmp-widget-store-featured-products.php:17
msgid "Store Featured Products"
msgstr ""

#: helpers/wcfmmp-core-functions.php:578
#: includes/store-widgets/class-wcfmmp-widget-store-on-sale-products.php:17
msgid "Store On Sale Products"
msgstr ""

#: helpers/wcfmmp-core-functions.php:579
#: includes/store-widgets/class-wcfmmp-widget-store-recent-articles.php:17
msgid "Store Recent Articles"
msgstr ""

#: helpers/wcfmmp-core-functions.php:581
#: includes/store-widgets/class-wcfmmp-widget-store-lists-search.php:17
msgid "Store Lists Search"
msgstr ""

#: helpers/wcfmmp-core-functions.php:582
#: includes/store-widgets/class-wcfmmp-widget-store-lists-category-filter.php:17
msgid "Store Lists Category Filter"
msgstr ""

#: helpers/wcfmmp-core-functions.php:583
#: includes/store-widgets/class-wcfmmp-widget-store-lists-location-filter.php:17
msgid "Store Lists Location Filter"
msgstr ""

#: helpers/wcfmmp-core-functions.php:584
#: includes/store-widgets/class-wcfmmp-widget-store-lists-radius-filter.php:17
msgid "Store Lists Radius Filter"
msgstr ""

#: helpers/wcfmmp-core-functions.php:585
#: includes/store-widgets/class-wcfmmp-widget-store-lists-meta-filter.php:17
msgid "Store Lists Meta Filter"
msgstr ""

#: helpers/wcfmmp-core-functions.php:587
#: includes/store-widgets/class-wcfmmp-widget-products-search-by-vendors.php:79
msgid "Search by Vendors"
msgstr ""

#: helpers/wcfmmp-core-functions.php:588
#: includes/store-widgets/class-wcfmmp-widget-store-top-rated-vendors.php:16
msgid "Store Top Rated Vendors"
msgstr ""

#: helpers/wcfmmp-core-functions.php:589
#: includes/store-widgets/class-wcfmmp-widget-store-best-selling-vendors.php:16
msgid "Store Best Selling Vendors"
msgstr ""

#: helpers/wcfmmp-core-functions.php:608
#: includes/store-emails/class-wcfmmp-email-store-new-order.php:16
msgid "Store New Order"
msgstr ""

#: helpers/wcfmmp-core-functions.php:619
msgid "Please insert your comment before submit."
msgstr ""

#: helpers/wcfmmp-core-functions.php:620
msgid "Please rate atleast one category before submit."
msgstr ""

#: helpers/wcfmmp-core-functions.php:621
msgid "Your review successfully submited, will publish after approval!"
msgstr ""

#: helpers/wcfmmp-core-functions.php:622
msgid "Your review successfully submited."
msgstr ""

#: helpers/wcfmmp-core-functions.php:623
msgid "Your review response successfully submited."
msgstr ""

#: helpers/wcfmmp-core-functions.php:624 helpers/wcfmmp-core-functions.php:639
msgid "Your refund request failed, please try after sometime."
msgstr ""

#: helpers/wcfmmp-core-functions.php:637
msgid "Please insert your refund reason before submit."
msgstr ""

#: helpers/wcfmmp-core-functions.php:638
msgid "Your refund request successfully sent."
msgstr ""

#: helpers/wcfmmp-core-functions.php:651 views/ledger/wcfmmp-view-ledger.php:45
msgid "Completed"
msgstr ""

#: helpers/wcfmmp-core-functions.php:712
msgid "More Offers"
msgstr ""

#: helpers/wcfmmp-core-functions.php:856
msgid "POS"
msgstr ""

#: helpers/wcfmmp-core-functions.php:909
msgid "Select Shipping Type..."
msgstr ""

#: helpers/wcfmmp-core-functions.php:910
msgid "Shipping by Country"
msgstr ""

#: helpers/wcfmmp-core-functions.php:911
msgid "Shipping by Zone"
msgstr ""

#: helpers/wcfmmp-core-functions.php:912
msgid "Shipping by Weight"
msgstr ""

#: helpers/wcfmmp-core-functions.php:913
msgid "Shipping by Distance"
msgstr ""

#: helpers/wcfmmp-core-functions.php:922
msgid "Ready to ship in..."
msgstr ""

#: helpers/wcfmmp-core-functions.php:923
msgid "1 business day"
msgstr ""

#: helpers/wcfmmp-core-functions.php:924
msgid "1-2 business days"
msgstr ""

#: helpers/wcfmmp-core-functions.php:925
msgid "1-3 business days"
msgstr ""

#: helpers/wcfmmp-core-functions.php:926
msgid "3-5 business days"
msgstr ""

#: helpers/wcfmmp-core-functions.php:927
msgid "1-2 weeks"
msgstr ""

#: helpers/wcfmmp-core-functions.php:928
msgid "2-3 weeks"
msgstr ""

#: helpers/wcfmmp-core-functions.php:929
msgid "3-4 weeks"
msgstr ""

#: helpers/wcfmmp-core-functions.php:930
msgid "4-6 weeks"
msgstr ""

#: helpers/wcfmmp-core-functions.php:931
msgid "6-8 weeks"
msgstr ""

#: helpers/wcfmmp-core-functions.php:941
msgid "-- Select a Method --"
msgstr ""

#: helpers/wcfmmp-core-functions.php:942
msgid "Flat Rate"
msgstr ""

#: helpers/wcfmmp-core-functions.php:943
msgid "Local Pickup"
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-bank_transfer.php:39
#: includes/payment-gateways/class-wcfmmp-gateway-by_cash.php:33
#: includes/payment-gateways/class-wcfmmp-gateway-paypal.php:62
#: includes/payment-gateways/class-wcfmmp-gateway-skrill.php:36
#: includes/payment-gateways/class-wcfmmp-gateway-stripe.php:98
msgid "New transaction has been initiated"
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-paypal.php:74
msgid ""
"PayPal Payout setting is not configured properly please contact site "
"administrator"
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-paypal.php:77
msgid "Please update your PayPal email to receive commission"
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-paypal.php:114
msgid "Payment received from %1$s as commission at %2$s on %3$s"
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-stripe.php:34
msgid "Stripe connect"
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-stripe.php:110
msgid "Please connect with Stripe account"
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-stripe.php:113
msgid "Stripe setting is not configured properly please contact site administrator"
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-stripe.php:127
#: includes/payment-gateways/class-wcfmmp-gateway-stripe.php:130
msgid "Payout for withdrawal ID #"
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:44
msgid "Marketplace Stripe Split Pay"
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:81
msgid "Credit or Debit Card (Stripe)"
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:82
msgid "Pay with your credit or debit card via Stripe."
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:249
#. translators: link to Stripe testing page
msgid ""
"TEST MODE ENABLED. In test mode, you can use the card number %s with any "
"CVC and a valid expiration date or check the <a href=\"%s\" "
"target=\"_blank\">Testing Stripe documentation</a> for more card numbers."
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:266
msgid "Card ending with"
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:269
msgid "Use a new credit card"
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:278
msgid "Card Number"
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:288
msgid "Expiry Date"
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:296
msgid "Card Code (CVC)"
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:326
#: includes/payment-gateways/class-wcfmmp-gateway-wirecard.php:231
msgid ""
"An error has occurred while processing your payment, please try again. Or "
"contact us for assistance."
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:378
#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:523
#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:640
#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:718
#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:1403
#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:1431
msgid "Stripe Split Pay Error: "
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:517
#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:634
#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:712
msgid "Stripe Charge Error: "
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:544
#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:661
msgid "Payment for Order #%s"
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:792
#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:934
msgid "Error creating transfer record with Stripe: "
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:811
#: includes/payment-gateways/class-wcfmmp-gateway-wirecard.php:431
msgid "Stripe Payment Error"
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:863
#. translators: transaction id
msgid "Stripe charge awaiting payment: %s."
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:946
#: includes/wcfm-stripe/class-wcfm-stripe-order-handler.php:291
#. translators: transaction id
msgid "Stripe charge complete (Charge ID: %s)"
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:951
msgid "Payment processing failed. Please retry."
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:963
#. translators: transaction id
msgid ""
"Stripe charge authorized (Charge ID: %s). Process order to take payment, or "
"cancel to remove the pre-authorization."
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:978
#: includes/payment-gateways/class-wcfmmp-gateway-wirecard.php:124
#: includes/shipping-gateways/class-wcfmmp-shipping-by-country.php:72
#: includes/shipping-gateways/class-wcfmmp-shipping-by-distance.php:184
#: includes/shipping-gateways/class-wcfmmp-shipping-by-weight.php:188
#: includes/store-emails/class-wcfmmp-email-store-new-order.php:181
msgid "Enable/Disable"
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:1037
#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:1045
msgid "Customer for"
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:1074
msgid "Error creating customer record with Stripe: "
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:1110
msgid "Error update customer cards with Stripe: "
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:1230
msgid "Split Pay Reversal"
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:1252
msgid "Refund Processed Via Stripe ( Refund ID: #%s )"
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:1351
#: includes/payment-gateways/class-wcfmmp-gateway-wirecard.php:604
#. translators: 1) dollar amount 2) transaction id 3) refund message
msgid "Refunded %1$s - Refund ID: %2$s - Reason: %3$s"
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:1351
msgid "Pre-Authorization Released"
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:1369
#: includes/wcfm-stripe/class-wcfm-stripe-intent-controller.php:58
msgid "CSRF verification failed."
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:1382
#: includes/wcfm-stripe/class-wcfm-stripe-intent-controller.php:71
msgid "Missing order ID for payment confirmation"
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:1400
#: includes/wcfm-stripe/class-wcfm-stripe-intent-controller.php:91
#. translators: Error message text
msgid "Payment verification error: %s"
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:1467
msgid ""
"Almost there!\n"
"\n"
"Your order has already been created, the only thing that still needs to be "
"done is for you to authorize the payment with your bank."
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:1612
#. translators: 1) The error message that was received from Stripe.
msgid "Stripe SCA authentication failed. Reason: %s"
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:1613
msgid "Stripe SCA authentication failed."
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:1721
#. translators: 1) blog name 2) order number
msgid "%1$s - Order %2$s"
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:1735
msgid "customer_name"
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:1736
msgid "customer_email"
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-stripe_split.php:1961
msgid ""
"<strong>Stripe Gateway is disabled.</strong> Please re-check %swithdrawal "
"setting panel%s. This occurs mostly due to absence of Stripe Secret Key"
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-wirecard.php:40
msgid "Marketplace Wirecard"
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-wirecard.php:41
msgid "Have your customers pay with credit card."
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-wirecard.php:125
msgid "Enable Wirecard"
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-wirecard.php:131
msgid "Title"
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-wirecard.php:133
#: includes/shipping-gateways/class-wcfmmp-shipping-by-country.php:80
#: includes/shipping-gateways/class-wcfmmp-shipping-by-distance.php:192
#: includes/shipping-gateways/class-wcfmmp-shipping-by-weight.php:196
#: includes/shipping-gateways/class-wcfmmp-shipping-by-zone.php:193
msgid "This controls the title which the user sees during checkout."
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-wirecard.php:134
msgid "Wirecard Credit Card"
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-wirecard.php:139
msgid "This controls the description which the user sees during checkout."
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-wirecard.php:143
msgid "Wirecard Fee"
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-wirecard.php:149
msgid "Select who will bear the Wirecard transection fee."
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-wirecard.php:241
msgid "Wirecard access token is not found."
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-wirecard.php:269
msgid "Wirecard data is not found."
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-wirecard.php:283
msgid "Credit card nout found."
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-wirecard.php:343
msgid "Order #"
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-wirecard.php:352
msgid "Wirecard payment processing failed."
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-wirecard.php:370
msgid "Wirecard order processing failed :: "
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-wirecard.php:406
msgid "Wirecard Pay"
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-wirecard.php:418
msgid "Error creating transfer record with Wirecard: "
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-wirecard.php:488
msgid "Error creating Customer and Holder :: "
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-wirecard.php:538
msgid "Refund Processed Via Wirecard ( Refund ID: #%s )"
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-wirecard.php:613
msgid "Wirecard Refund not precessed :: "
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-wirecard.php:632
#: includes/payment-gateways/class-wcfmmp-gateway-wirecard.php:634
msgid "CPF Number"
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-wirecard.php:660
msgid "Card number is not valid"
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-wirecard.php:661
msgid "Card expriy date is not valid"
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-wirecard.php:662
msgid "Card CVC number is not valid"
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-wirecard.php:682
msgid ""
"TEST MODE ENABLED. In test mode, you can use the card number "
"**************** with any CVC and a valid expiration date."
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-wirecard.php:793
#: includes/payment-gateways/class-wcfmmp-gateway-wirecard.php:815
msgid "Wirecard"
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-wirecard.php:795
msgid "You are not connected with Wirecard."
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-wirecard.php:800
msgid "Connect Wirecard Account"
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-wirecard.php:818
msgid "You are connected with Wirecard"
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-wirecard.php:824
msgid "Disconnect Wirecard Account"
msgstr ""

#: includes/payment-gateways/class-wcfmmp-gateway-wirecard.php:833
msgid "Wirecard not setup properly, please contact your site admin."
msgstr ""

#: includes/shipping-gateways/class-wcfmmp-shipping-by-country.php:25
msgid "Marketplace Shipping by Country"
msgstr ""

#: includes/shipping-gateways/class-wcfmmp-shipping-by-country.php:26
msgid "Enable vendors to set marketplace shipping per country"
msgstr ""

#: includes/shipping-gateways/class-wcfmmp-shipping-by-country.php:32
#: includes/shipping-gateways/class-wcfmmp-shipping-by-distance.php:32
#: includes/shipping-gateways/class-wcfmmp-shipping-by-distance.php:193
#: includes/shipping-gateways/class-wcfmmp-shipping-by-weight.php:35
#: includes/shipping-gateways/class-wcfmmp-shipping-by-weight.php:197
msgid "Shipping Cost"
msgstr ""

#: includes/shipping-gateways/class-wcfmmp-shipping-by-country.php:74
#: includes/shipping-gateways/class-wcfmmp-shipping-by-distance.php:186
#: includes/shipping-gateways/class-wcfmmp-shipping-by-weight.php:190
#: views/shipping/wcfmmp-view-shipping-settings.php:53
msgid "Enable Shipping"
msgstr ""

#: includes/shipping-gateways/class-wcfmmp-shipping-by-country.php:81
msgid "Regular Shipping"
msgstr ""

#: includes/shipping-gateways/class-wcfmmp-shipping-by-country.php:85
#: includes/shipping-gateways/class-wcfmmp-shipping-by-distance.php:197
#: includes/shipping-gateways/class-wcfmmp-shipping-by-weight.php:201
#: views/shipping/wcfmmp-view-edit-method-popup.php:134
#: views/shipping/wcfmmp-view-edit-method-popup.php:214
msgid "Tax Status"
msgstr ""

#: includes/shipping-gateways/class-wcfmmp-shipping-by-country.php:89
#: includes/shipping-gateways/class-wcfmmp-shipping-by-distance.php:201
#: includes/shipping-gateways/class-wcfmmp-shipping-by-weight.php:205
#: includes/shipping-gateways/class-wcfmmp-shipping-by-zone.php:203
#: views/shipping/wcfmmp-view-edit-method-popup.php:141
#: views/shipping/wcfmmp-view-edit-method-popup.php:221
msgid "Taxable"
msgstr ""

#: includes/shipping-gateways/class-wcfmmp-shipping-by-country.php:182
#: includes/shipping-gateways/class-wcfmmp-shipping-by-distance.php:137
#: includes/shipping-gateways/class-wcfmmp-shipping-by-weight.php:139
msgid "Pickup from Store"
msgstr ""

#: includes/shipping-gateways/class-wcfmmp-shipping-by-distance.php:25
msgid "Marketplace Shipping by Distance"
msgstr ""

#: includes/shipping-gateways/class-wcfmmp-shipping-by-distance.php:26
msgid "Enable vendors to set marketplace shipping by distance range"
msgstr ""

#: includes/shipping-gateways/class-wcfmmp-shipping-by-distance.php:104
msgid "Some cart item(s) are not deliverable to your location."
msgstr ""

#: includes/shipping-gateways/class-wcfmmp-shipping-by-weight.php:28
msgid "Marketplace Shipping by Weight"
msgstr ""

#: includes/shipping-gateways/class-wcfmmp-shipping-by-weight.php:29
msgid "Enable vendors to set marketplace shipping by weight range"
msgstr ""

#: includes/shipping-gateways/class-wcfmmp-shipping-by-zone.php:30
msgid "Cloning this class could cause catastrophic disasters!"
msgstr ""

#: includes/shipping-gateways/class-wcfmmp-shipping-by-zone.php:39
msgid "Unserializing is forbidden!"
msgstr ""

#: includes/shipping-gateways/class-wcfmmp-shipping-by-zone.php:53
msgid "Charge varying rates based on user defined conditions"
msgstr ""

#: includes/shipping-gateways/class-wcfmmp-shipping-by-zone.php:191
msgid "Method title"
msgstr ""

#: includes/shipping-gateways/class-wcfmmp-shipping-by-zone.php:198
msgid "Tax status"
msgstr ""

#: includes/sms-gateways/class-wcfmmp-twilio-sms-notification.php:184
msgid "Sent"
msgstr ""

#: includes/sms-gateways/class-wcfmmp-twilio-sms-notification.php:301
#: views/shipping/wcfmmp-view-edit-method-popup.php:271
#: views/shipping/wcfmmp-view-edit-method-popup.php:293
#: views/store-lists/wcfmmp-view-store-lists-card.php:40
msgid "N/A"
msgstr ""

#: includes/sms-gateways/class-wcfmmp-twilio-sms-notification.php:306
msgid "SMS Notification"
msgstr ""

#: includes/sms-gateways/class-wcfmmp-twilio-sms-notification.php:308
msgid "Date Sent"
msgstr ""

#: includes/store-emails/class-wcfmmp-email-store-new-order.php:17
msgid "New order notification emails are sent when order is processing."
msgstr ""

#: includes/store-emails/class-wcfmmp-email-store-new-order.php:37
msgid "[{site_title}] New Store Order ({order_number}) - {order_date}"
msgstr ""

#: includes/store-emails/class-wcfmmp-email-store-new-order.php:47
msgid "New Store Order"
msgstr ""

#: includes/store-emails/class-wcfmmp-email-store-new-order.php:183
msgid "Enable this email notification."
msgstr ""

#: includes/store-emails/class-wcfmmp-email-store-new-order.php:187
msgid "Subject"
msgstr ""

#: includes/store-emails/class-wcfmmp-email-store-new-order.php:189
msgid ""
"This controls the email subject line. Leave it blank to use the default "
"subject: <code>%s</code>."
msgstr ""

#: includes/store-emails/class-wcfmmp-email-store-new-order.php:194
msgid "Email Heading"
msgstr ""

#: includes/store-emails/class-wcfmmp-email-store-new-order.php:196
msgid ""
"This controls the main heading contained within the email notification. "
"Leave it blank to use the default heading: <code>%s</code>."
msgstr ""

#: includes/store-emails/class-wcfmmp-email-store-new-order.php:201
msgid "Email Type"
msgstr ""

#: includes/store-emails/class-wcfmmp-email-store-new-order.php:203
msgid "Choose which format of email to be sent."
msgstr ""

#: includes/store-emails/class-wcfmmp-email-store-new-order.php:207
msgid "Plain Text"
msgstr ""

#: includes/store-emails/class-wcfmmp-email-store-new-order.php:208
msgid "HTML"
msgstr ""

#: includes/store-emails/class-wcfmmp-email-store-new-order.php:209
msgid "Multipart"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-products-search-by-vendors.php:17
#: includes/store-widgets/class-wcfmmp-widget-products-search-by-vendors.php:18
msgid "Filter Products by Store"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-products-search-by-vendors.php:85
#: includes/store-widgets/class-wcfmmp-widget-store-best-selling-vendors.php:94
#: includes/store-widgets/class-wcfmmp-widget-store-category.php:120
#: includes/store-widgets/class-wcfmmp-widget-store-coupons.php:157
#: includes/store-widgets/class-wcfmmp-widget-store-featured-products.php:190
#: includes/store-widgets/class-wcfmmp-widget-store-hours.php:142
#: includes/store-widgets/class-wcfmmp-widget-store-lists-category-filter.php:249
#: includes/store-widgets/class-wcfmmp-widget-store-lists-location-filter.php:155
#: includes/store-widgets/class-wcfmmp-widget-store-lists-meta-filter.php:172
#: includes/store-widgets/class-wcfmmp-widget-store-lists-radius-filter.php:114
#: includes/store-widgets/class-wcfmmp-widget-store-lists-search.php:93
#: includes/store-widgets/class-wcfmmp-widget-store-location.php:123
#: includes/store-widgets/class-wcfmmp-widget-store-on-sale-products.php:188
#: includes/store-widgets/class-wcfmmp-widget-store-product-search.php:106
#: includes/store-widgets/class-wcfmmp-widget-store-recent-articles.php:145
#: includes/store-widgets/class-wcfmmp-widget-store-recent-products.php:184
#: includes/store-widgets/class-wcfmmp-widget-store-shipping-rules.php:131
#: includes/store-widgets/class-wcfmmp-widget-store-taxonomy.php:128
#: includes/store-widgets/class-wcfmmp-widget-store-top-products.php:187
#: includes/store-widgets/class-wcfmmp-widget-store-top-rated-products.php:185
#: includes/store-widgets/class-wcfmmp-widget-store-top-rated-vendors.php:97
msgid "Title:"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-best-selling-vendors.php:17
msgid "Marketplace: Best Selling Vendors"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-best-selling-vendors.php:86
msgid "Best Selling Vendors"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-best-selling-vendors.php:98
#: includes/store-widgets/class-wcfmmp-widget-store-top-rated-vendors.php:101
msgid "Number of vendors to show:"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-category.php:17
#: includes/store-widgets/class-wcfmmp-widget-store-category.php:112
#: includes/store-widgets/class-wcfmmp-widget-store-info.php:88
msgid "Store Categories"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-category.php:18
msgid "Vendor Store: Category"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-category.php:113
#: includes/store-widgets/class-wcfmmp-widget-store-taxonomy.php:120
msgid "Enable Toggle"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-category.php:125
msgid "Enable toggle to show child categories"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-coupons.php:18
msgid "Vendor Store: Coupons"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-coupons.php:113
msgid "FREE Shipping Coupon"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-coupons.php:113
#: includes/store-widgets/class-wcfmmp-widget-store-coupons.php:115
msgid "Expiry Date: "
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-featured-products.php:18
msgid "Vendor Store: Featured Products"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-featured-products.php:180
msgid "Featured Products"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-featured-products.php:194
#: includes/store-widgets/class-wcfmmp-widget-store-on-sale-products.php:192
#: includes/store-widgets/class-wcfmmp-widget-store-recent-products.php:188
#: includes/store-widgets/class-wcfmmp-widget-store-top-products.php:191
#: includes/store-widgets/class-wcfmmp-widget-store-top-rated-products.php:189
msgid "Number of products to show:"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-featured-products.php:198
#: includes/store-widgets/class-wcfmmp-widget-store-on-sale-products.php:196
#: includes/store-widgets/class-wcfmmp-widget-store-recent-products.php:192
#: includes/store-widgets/class-wcfmmp-widget-store-top-products.php:195
#: includes/store-widgets/class-wcfmmp-widget-store-top-rated-products.php:193
msgid "Hide Free Products:"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-hours.php:18
msgid "Vendor Store: Opening/Closing Hours"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-info.php:18
msgid "Vendor Store: Info"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-lists-category-filter.php:18
msgid "Store List: Category Filter"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-lists-category-filter.php:243
msgid "Search by Category"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-lists-location-filter.php:18
msgid "Store List: Location Filter"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-lists-location-filter.php:106
#: views/store-lists/wcfmmp-view-store-lists-search-form.php:277
msgid "Search by City"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-lists-location-filter.php:110
#: views/store-lists/wcfmmp-view-store-lists-search-form.php:281
msgid "Search by ZIP"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-lists-location-filter.php:143
msgid "Search by Location"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-lists-location-filter.php:144
msgid "State Filter"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-lists-location-filter.php:145
msgid "City Filter"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-lists-location-filter.php:146
msgid "ZIP Code Filter"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-lists-location-filter.php:160
msgid "Disable State Filter"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-lists-location-filter.php:164
msgid "Disable City Filter"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-lists-location-filter.php:168
msgid "Disable ZIP Code Filter"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-lists-meta-filter.php:18
msgid "Store List: Meta Filter"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-lists-meta-filter.php:77
msgid "Search by"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-lists-meta-filter.php:144
msgid "Meta Filter"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-lists-radius-filter.php:18
msgid "Store List: Radius Filter"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-lists-radius-filter.php:108
msgid "Search by Radius"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-lists-search.php:18
msgid "Store List: Search"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-lists-search.php:54
msgid "Search &hellip;"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-lists-search.php:54
#: views/store-lists/wcfmmp-view-store-lists-search-form.php:97
msgid "Search store &hellip;"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-lists-search.php:87
#: views/store/wcfmmp-view-store-sidebar.php:32
#: views/store-lists/wcfmmp-view-store-lists-sidebar.php:34
msgid "Search"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-location.php:18
msgid "Vendor Store: Location"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-on-sale-products.php:18
msgid "Vendor Store: On Sale Products"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-on-sale-products.php:178
msgid "On Sale Products"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-product-search.php:18
msgid "Vendor Store: Product Search"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-product-search.php:100
msgid "Product Search"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-recent-articles.php:18
msgid "Vendor Store: Recent Articles"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-recent-articles.php:94
msgid "(no title)"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-recent-articles.php:137
msgid "Recent Articles"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-recent-articles.php:149
msgid "Number of articles to show:"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-recent-products.php:18
msgid "Vendor Store: Recent Products"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-recent-products.php:174
msgid "Recent Products"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-shipping-rules.php:18
msgid "Vendor Store: Shipping Rules"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-shipping-rules.php:125
msgid "Shipping Rules"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-taxonomy.php:18
msgid "Vendor Store: Taxonomy"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-taxonomy.php:119
msgid "Choose Taxonomy"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-taxonomy.php:133
msgid "Taxonomy:"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-taxonomy.php:135
msgid "-- Taxonomy --"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-taxonomy.php:152
msgid "Enable toggle to show child taxonomies"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-top-products.php:17
msgid "Store Top Selling Products"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-top-products.php:18
msgid "Vendor Store: Top Selling Products"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-top-products.php:177
msgid "Top Selling Products"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-top-rated-products.php:18
msgid "Vendor Store: Top Rated Products"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-top-rated-products.php:175
msgid "Top Rated Products"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-top-rated-vendors.php:17
msgid "Marketplace: Top Rated Vendors"
msgstr ""

#: includes/store-widgets/class-wcfmmp-widget-store-top-rated-vendors.php:89
msgid "Top Rated Vendors"
msgstr ""

#: includes/wcfm-stripe/class-wcfm-stripe-api.php:139
#: includes/wcfm-stripe/class-wcfm-stripe-api.php:173
msgid "There was a problem connecting to the Stripe API endpoint."
msgstr ""

#: includes/wcfm-stripe/class-wcfm-stripe-customer.php:119
#: views/product_multivendor/wcfmmp-view-sell-items-catalog.php:114
#: views/product_multivendor/wcfmmp-view-sell-items-catalog.php:128
msgid "Name"
msgstr ""

#: includes/wcfm-stripe/class-wcfm-stripe-customer.php:119
msgid "Username"
msgstr ""

#: includes/wcfm-stripe/class-wcfm-stripe-customer.php:203
msgid "Unable to add payment source."
msgstr ""

#: includes/wcfm-stripe/class-wcfm-stripe-helper.php:209
msgid "The card number is not a valid credit card number."
msgstr ""

#: includes/wcfm-stripe/class-wcfm-stripe-helper.php:210
msgid "The card's expiration month is invalid."
msgstr ""

#: includes/wcfm-stripe/class-wcfm-stripe-helper.php:211
msgid "The card's expiration year is invalid."
msgstr ""

#: includes/wcfm-stripe/class-wcfm-stripe-helper.php:212
msgid "The card's security code is invalid."
msgstr ""

#: includes/wcfm-stripe/class-wcfm-stripe-helper.php:213
msgid "The card number is incorrect."
msgstr ""

#: includes/wcfm-stripe/class-wcfm-stripe-helper.php:214
msgid "The card number is incomplete."
msgstr ""

#: includes/wcfm-stripe/class-wcfm-stripe-helper.php:215
msgid "The card's security code is incomplete."
msgstr ""

#: includes/wcfm-stripe/class-wcfm-stripe-helper.php:216
msgid "The card's expiration date is incomplete."
msgstr ""

#: includes/wcfm-stripe/class-wcfm-stripe-helper.php:217
msgid "The card has expired."
msgstr ""

#: includes/wcfm-stripe/class-wcfm-stripe-helper.php:218
msgid "The card's security code is incorrect."
msgstr ""

#: includes/wcfm-stripe/class-wcfm-stripe-helper.php:219
msgid "The card's zip code failed validation."
msgstr ""

#: includes/wcfm-stripe/class-wcfm-stripe-helper.php:220
msgid "The card's expiration year is in the past"
msgstr ""

#: includes/wcfm-stripe/class-wcfm-stripe-helper.php:221
msgid "The card was declined."
msgstr ""

#: includes/wcfm-stripe/class-wcfm-stripe-helper.php:222
msgid "There is no card on a customer that is being charged."
msgstr ""

#: includes/wcfm-stripe/class-wcfm-stripe-helper.php:223
msgid "An error occurred while processing the card."
msgstr ""

#: includes/wcfm-stripe/class-wcfm-stripe-helper.php:224
#: includes/wcfm-stripe/class-wcfm-stripe-order-handler.php:114
msgid "Unable to process this payment, please try again or use alternative method."
msgstr ""

#: includes/wcfm-stripe/class-wcfm-stripe-helper.php:225
msgid "The billing country is not accepted by SOFORT. Please try another country."
msgstr ""

#: includes/wcfm-stripe/class-wcfm-stripe-order-handler.php:158
msgid "This card is no longer available and has been removed."
msgstr ""

#: includes/wcfm-stripe/class-wcfm-stripe-order-handler.php:189
#. translators: error message
msgid "Stripe payment failed: %s"
msgstr ""

#: includes/wcfm-stripe/class-wcfm-stripe-order-handler.php:240
#: includes/wcfm-stripe/class-wcfm-stripe-order-handler.php:252
#: includes/wcfm-stripe/class-wcfm-stripe-order-handler.php:268
#: includes/wcfm-stripe/class-wcfm-stripe-order-handler.php:280
#. translators: error message
msgid "Unable to capture charge! %s"
msgstr ""

#: views/emails/plain/store-new-order.php:30
#: views/emails/store-new-order.php:30
msgid "Standard"
msgstr ""

#: views/emails/plain/store-new-order.php:70
#: views/emails/store-new-order.php:73
msgid "A new order was received from %s. Order details is as follows:"
msgstr ""

#: views/emails/plain/store-new-order.php:91
#: views/emails/store-new-order.php:99
#: views/reviews/wcfmmp-view-product-reviews.php:96
#: views/reviews/wcfmmp-view-product-reviews.php:108
msgid "Product"
msgstr ""

#: views/emails/plain/store-new-order.php:92
#: views/emails/store-new-order.php:100
#: views/product_multivendor/wcfmmp-view-more-offers-loop.php:76
#: views/product_multivendor/wcfmmp-view-sell-items-catalog.php:115
#: views/product_multivendor/wcfmmp-view-sell-items-catalog.php:129
msgid "Price"
msgstr ""

#: views/emails/plain/store-new-order.php:94
#: views/emails/store-new-order.php:102
#: views/refund/wcfmmp-view-refund-requests-popup.php:94
msgid "Total"
msgstr ""

#: views/emails/plain/store-new-order.php:129
#: views/emails/store-new-order.php:137
msgid "SKU:"
msgstr ""

#: views/emails/plain/store-new-order.php:132
#: views/emails/store-new-order.php:140
msgid "Variation ID:"
msgstr ""

#: views/emails/plain/store-new-order.php:136
#: views/emails/store-new-order.php:144
msgid "No longer exists"
msgstr ""

#: views/emails/plain/store-new-order.php:307
#: views/emails/store-new-order.php:315
msgid "Fee"
msgstr ""

#: views/emails/plain/store-new-order.php:367
#: views/emails/store-new-order.php:375
msgid "This is the total discount. Discounts are defined per line item."
msgstr ""

#: views/emails/plain/store-new-order.php:367
#: views/emails/store-new-order.php:375
msgid "Discount"
msgstr ""

#: views/emails/plain/store-new-order.php:378
#: views/emails/store-new-order.php:386
msgid "This is the shipping and handling total costs for the order."
msgstr ""

#: views/emails/plain/store-new-order.php:412
#: views/emails/store-new-order.php:420
msgid "Order Total"
msgstr ""

#: views/emails/plain/store-new-order.php:435
#: views/emails/store-new-order.php:443
msgid "Customer Details"
msgstr ""

#: views/emails/plain/store-new-order.php:437
#: views/emails/store-new-order.php:445
msgid "Customer Name:"
msgstr ""

#: views/emails/plain/store-new-order.php:438
#: views/emails/store-new-order.php:446
msgid "Email:"
msgstr ""

#: views/emails/plain/store-new-order.php:441
#: views/emails/store-new-order.php:449
msgid "Telephone:"
msgstr ""

#: views/emails/plain/store-new-order.php:461
#: views/emails/store-new-order.php:469
msgid "Billing address"
msgstr ""

#: views/emails/plain/store-new-order.php:468
#: views/emails/store-new-order.php:476
msgid "Shipping address"
msgstr ""

#: views/emails/store-new-order.php:75
msgid "A new order was received. Order details is as follows:"
msgstr ""

#: views/ledger/wcfmmp-view-ledger.php:44
#: views/ledger/wcfmmp-view-ledger.php:51
#: views/refund/wcfmmp-view-refund-requests.php:39
msgid "Show all .."
msgstr ""

#: views/ledger/wcfmmp-view-ledger.php:75
msgid "total earning"
msgstr ""

#: views/ledger/wcfmmp-view-ledger.php:84
msgid "total withdrawal"
msgstr ""

#: views/ledger/wcfmmp-view-ledger.php:94
msgid "total refund"
msgstr ""

#: views/ledger/wcfmmp-view-ledger.php:108
#: views/ledger/wcfmmp-view-ledger.php:118
#: views/refund/wcfmmp-view-refund-requests.php:66
#: views/refund/wcfmmp-view-refund-requests.php:79
msgid "Type"
msgstr ""

#: views/ledger/wcfmmp-view-ledger.php:109
#: views/ledger/wcfmmp-view-ledger.php:119
#: views/product_multivendor/wcfmmp-view-more-offer-single.php:64
#: views/product_multivendor/wcfmmp-view-more-offers-loop.php:78
msgid "Details"
msgstr ""

#: views/ledger/wcfmmp-view-ledger.php:110
#: views/ledger/wcfmmp-view-ledger.php:120
msgid "Credit"
msgstr ""

#: views/ledger/wcfmmp-view-ledger.php:111
#: views/ledger/wcfmmp-view-ledger.php:121
msgid "Debit"
msgstr ""

#: views/ledger/wcfmmp-view-ledger.php:112
#: views/ledger/wcfmmp-view-ledger.php:122
#: views/reviews/wcfmmp-view-product-reviews.php:98
#: views/reviews/wcfmmp-view-product-reviews.php:110
#: views/reviews/wcfmmp-view-reviews.php:96
#: views/reviews/wcfmmp-view-reviews.php:108
msgid "Dated"
msgstr ""

#: views/media/wcfmmp-view-media.php:25 views/media/wcfmmp-view-media.php:32
msgid "Media Manager"
msgstr ""

#: views/media/wcfmmp-view-media.php:36
msgid "Total Disk Space Usage: "
msgstr ""

#: views/media/wcfmmp-view-media.php:47
msgid "Bulk Delete"
msgstr ""

#: views/media/wcfmmp-view-media.php:65 views/media/wcfmmp-view-media.php:78
msgid "Select all for delete"
msgstr ""

#: views/media/wcfmmp-view-media.php:68 views/media/wcfmmp-view-media.php:81
msgid "File"
msgstr ""

#: views/media/wcfmmp-view-media.php:69 views/media/wcfmmp-view-media.php:82
msgid "Associate"
msgstr ""

#: views/media/wcfmmp-view-media.php:71 views/media/wcfmmp-view-media.php:84
msgid "Size"
msgstr ""

#: views/product-geolocate/wcfmmp-view-product-lists-search-form.php:51
msgid "Filter"
msgstr ""

#: views/product-geolocate/wcfmmp-view-product-lists-vendor-filter.php:67
#: views/product-geolocate/wcfmmp-view-product-lists-vendor-filter.php:83
msgid "Filter by"
msgstr ""

#: views/product_multivendor/wcfmmp-view-more-offer-single.php:62
msgid "Add to Cart"
msgstr ""

#: views/product_multivendor/wcfmmp-view-more-offers.php:28
#: views/product_multivendor/wcfmmp-view-more-offers.php:82
msgid "No more offers for this product!"
msgstr ""

#: views/product_multivendor/wcfmmp-view-more-offers.php:62
msgid "Default sorting"
msgstr ""

#: views/product_multivendor/wcfmmp-view-more-offers.php:63
msgid "Sort by popularity"
msgstr ""

#: views/product_multivendor/wcfmmp-view-more-offers.php:64
msgid "Sort by latest"
msgstr ""

#: views/product_multivendor/wcfmmp-view-more-offers.php:65
msgid "Sort by price: low to high"
msgstr ""

#: views/product_multivendor/wcfmmp-view-more-offers.php:66
msgid "Sort by price: high to low"
msgstr ""

#: views/product_multivendor/wcfmmp-view-sell-items-catalog.php:27
msgid "Add New Product"
msgstr ""

#: views/product_multivendor/wcfmmp-view-sell-items-catalog.php:27
msgid "Add New"
msgstr ""

#: views/product_multivendor/wcfmmp-view-sell-items-catalog.php:38
msgid "Bulk Add"
msgstr ""

#: views/product_multivendor/wcfmmp-view-sell-items-catalog.php:63
msgid "Show all "
msgstr ""

#: views/product_multivendor/wcfmmp-view-sell-items-catalog.php:79
msgid "Show all product types"
msgstr ""

#: views/product_multivendor/wcfmmp-view-sell-items-catalog.php:89
msgid "Downloadable"
msgstr ""

#: views/product_multivendor/wcfmmp-view-sell-items-catalog.php:93
msgid "Virtual"
msgstr ""

#: views/product_multivendor/wcfmmp-view-sell-items-catalog.php:111
#: views/product_multivendor/wcfmmp-view-sell-items-catalog.php:125
msgid "Select multiple and add to My Store"
msgstr ""

#: views/product_multivendor/wcfmmp-view-sell-items-catalog.php:113
#: views/product_multivendor/wcfmmp-view-sell-items-catalog.php:127
#: views/reviews/wcfmmp-view-reviews.php:91
#: views/reviews/wcfmmp-view-reviews.php:103
msgid "Image"
msgstr ""

#: views/product_multivendor/wcfmmp-view-sell-items-catalog.php:116
#: views/product_multivendor/wcfmmp-view-sell-items-catalog.php:130
msgid "Taxonomies"
msgstr ""

#: views/product_multivendor/wcfmmp-view-sell-items-catalog.php:145
msgid "Bulk Add to My Store"
msgstr ""

#: views/refund/wcfmmp-view-refund-requests-popup.php:80
msgid "Request Mode"
msgstr ""

#: views/refund/wcfmmp-view-refund-requests-popup.php:85
msgid "Refund by Item(s)"
msgstr ""

#: views/refund/wcfmmp-view-refund-requests-popup.php:91
msgid "Item"
msgstr ""

#: views/refund/wcfmmp-view-refund-requests-popup.php:93
msgid "Qty"
msgstr ""

#: views/refund/wcfmmp-view-refund-requests-popup.php:180
msgid "Refund Requests Reason"
msgstr ""

#: views/refund/wcfmmp-view-refund-requests-popup.php:248
msgid "Submit"
msgstr ""

#: views/refund/wcfmmp-view-refund-requests-popup.php:252
msgid "This order's item(s) are already requested for refund!"
msgstr ""

#: views/refund/wcfmmp-view-refund-requests.php:61
#: views/refund/wcfmmp-view-refund-requests.php:74
msgid "Requests"
msgstr ""

#: views/refund/wcfmmp-view-refund-requests.php:62
#: views/refund/wcfmmp-view-refund-requests.php:75
msgid "Request ID"
msgstr ""

#: views/refund/wcfmmp-view-refund-requests.php:63
#: views/refund/wcfmmp-view-refund-requests.php:76
msgid "Order ID"
msgstr ""

#: views/refund/wcfmmp-view-refund-requests.php:65
#: views/refund/wcfmmp-view-refund-requests.php:78
msgid "Amount"
msgstr ""

#: views/refund/wcfmmp-view-refund-requests.php:67
#: views/refund/wcfmmp-view-refund-requests.php:80
msgid "Reason"
msgstr ""

#: views/refund/wcfmmp-view-refund-requests.php:103
msgid "Reject"
msgstr ""

#: views/reviews/wcfmmp-view-product-reviews.php:47
msgid "All"
msgstr ""

#: views/reviews/wcfmmp-view-product-reviews.php:93
#: views/reviews/wcfmmp-view-product-reviews.php:105
#: views/reviews/wcfmmp-view-reviews.php:92
#: views/reviews/wcfmmp-view-reviews.php:104
msgid "Author"
msgstr ""

#: views/reviews/wcfmmp-view-product-reviews.php:94
#: views/reviews/wcfmmp-view-product-reviews.php:106
#: views/reviews/wcfmmp-view-reviews.php:93
#: views/reviews/wcfmmp-view-reviews.php:105
msgid "Comment"
msgstr ""

#: views/reviews/wcfmmp-view-product-reviews.php:95
#: views/reviews/wcfmmp-view-product-reviews.php:107
#: views/reviews/wcfmmp-view-reviews.php:94
#: views/reviews/wcfmmp-view-reviews.php:106
msgid "Rating"
msgstr ""

#: views/reviews/wcfmmp-view-reviews-latest-review.php:35
msgid "rated"
msgstr ""

#: views/reviews/wcfmmp-view-reviews-latest-review.php:44
#: views/store/wcfmmp-view-store-reviews.php:47
msgid "reviews"
msgstr ""

#: views/reviews/wcfmmp-view-reviews-latest-review.php:52
msgid "Review via Product"
msgstr ""

#: views/reviews/wcfmmp-view-reviews-latest-review.php:58
msgid "Reply"
msgstr ""

#: views/reviews/wcfmmp-view-reviews-latest-stat.php:50
msgid "and"
msgstr ""

#: views/reviews/wcfmmp-view-reviews-latest-stat.php:50
msgid "others have"
msgstr ""

#: views/reviews/wcfmmp-view-reviews-latest-stat.php:51
msgid "No user has"
msgstr ""

#: views/reviews/wcfmmp-view-reviews-latest-stat.php:52
msgid "has"
msgstr ""

#: views/reviews/wcfmmp-view-reviews-latest-stat.php:53
msgid "reviewed this store"
msgstr ""

#: views/reviews/wcfmmp-view-reviews-manage.php:53
msgid "Support Ticket"
msgstr ""

#: views/reviews/wcfmmp-view-reviews-manage.php:60
msgid "Ticket"
msgstr ""

#: views/reviews/wcfmmp-view-reviews-manage.php:64
msgid "Support Tickets"
msgstr ""

#: views/reviews/wcfmmp-view-reviews-manage.php:64
msgid "Tickets"
msgstr ""

#: views/reviews/wcfmmp-view-reviews-manage.php:93
msgid "Open"
msgstr ""

#: views/reviews/wcfmmp-view-reviews-manage.php:95
msgid "Closed"
msgstr ""

#: views/reviews/wcfmmp-view-reviews-manage.php:111
msgid "Replies"
msgstr ""

#: views/reviews/wcfmmp-view-reviews-manage.php:158
msgid "New Reply"
msgstr ""

#: views/reviews/wcfmmp-view-reviews-manage.php:166
msgid "Priority"
msgstr ""

#: views/reviews/wcfmmp-view-reviews-manage.php:181
msgid "Send"
msgstr ""

#: views/reviews/wcfmmp-view-reviews-new.php:30
#: views/reviews/wcfmmp-view-reviews-new.php:38
msgid "write a review"
msgstr ""

#: views/reviews/wcfmmp-view-reviews-new.php:32
msgid "your review"
msgstr ""

#: views/reviews/wcfmmp-view-reviews-new.php:33
msgid "Add Your Review"
msgstr ""

#: views/reviews/wcfmmp-view-reviews-new.php:38
msgid "Cancel"
msgstr ""

#: views/reviews/wcfmmp-view-reviews-new.php:47
msgid "Poor"
msgstr ""

#: views/reviews/wcfmmp-view-reviews-new.php:50
msgid "Fair"
msgstr ""

#: views/reviews/wcfmmp-view-reviews-new.php:53
msgid "Good"
msgstr ""

#: views/reviews/wcfmmp-view-reviews-new.php:56
msgid "Excellent"
msgstr ""

#: views/reviews/wcfmmp-view-reviews-new.php:59
msgid "WOW!!!"
msgstr ""

#: views/reviews/wcfmmp-view-reviews-new.php:72
msgid "Publish Review"
msgstr ""

#: views/reviews/wcfmmp-view-reviews-pagination.php:24
#: views/store-lists/wcfmmp-view-store-lists-pagination.php:23
msgid "&laquo;"
msgstr ""

#: views/reviews/wcfmmp-view-reviews-pagination.php:25
#: views/store-lists/wcfmmp-view-store-lists-pagination.php:24
msgid "&raquo;"
msgstr ""

#: views/reviews/wcfmmp-view-reviews.php:47
msgid "All (%s)"
msgstr ""

#: views/shipping/wcfmmp-view-add-method-popup.php:10
msgid "Add Shipping Methods"
msgstr ""

#: views/shipping/wcfmmp-view-add-method-popup.php:15
msgid ""
"Choose the shipping method you wish to add. Only shipping methods which "
"support zones are listed."
msgstr ""

#: views/shipping/wcfmmp-view-add-method-popup.php:22
msgid "Select Shipping Method"
msgstr ""

#: views/shipping/wcfmmp-view-edit-method-popup.php:9
msgid "Edit Shipping Methods"
msgstr ""

#: views/shipping/wcfmmp-view-edit-method-popup.php:49
#: views/shipping/wcfmmp-view-edit-method-popup.php:103
#: views/shipping/wcfmmp-view-edit-method-popup.php:180
msgid "Enter method title"
msgstr ""

#: views/shipping/wcfmmp-view-edit-method-popup.php:61
msgid "Minimum order amount for free shipping"
msgstr ""

#: views/shipping/wcfmmp-view-edit-method-popup.php:66
#: views/shipping/wcfmmp-view-edit-method-popup.php:120
#: views/shipping/wcfmmp-view-edit-method-popup.php:197
msgid "0.00"
msgstr ""

#: views/shipping/wcfmmp-view-edit-method-popup.php:140
#: views/shipping/wcfmmp-view-edit-method-popup.php:220
msgid "None"
msgstr ""

#: views/shipping/wcfmmp-view-edit-method-popup.php:205
#: views/shipping/wcfmmp-view-edit-method-popup.php:279
#: views/shipping/wcfmmp-view-edit-method-popup.php:300
msgid "Enter a cost (excl. tax) or sum, e.g. <code>10.00 * [qty]</code>."
msgstr ""

#: views/shipping/wcfmmp-view-edit-method-popup.php:205
#: views/shipping/wcfmmp-view-edit-method-popup.php:279
#: views/shipping/wcfmmp-view-edit-method-popup.php:300
msgid ""
"Use <code>[qty]</code> for the number of items, <br/><code>[cost]</code> "
"for the total cost of items, and <code>[fee percent=\"10\" min_fee=\"20\" "
"max_fee=\"\"]</code> for percentage based fees."
msgstr ""

#: views/shipping/wcfmmp-view-edit-method-popup.php:248
msgid "Shipping Class Cost"
msgstr ""

#: views/shipping/wcfmmp-view-edit-method-popup.php:250
msgid ""
"These costs can be optionally entered based on the shipping class set per "
"product( This cost will be added with the shipping cost above)."
msgstr ""

#: views/shipping/wcfmmp-view-edit-method-popup.php:266
msgid "Cost of Shipping Class: \""
msgstr ""

#: views/shipping/wcfmmp-view-edit-method-popup.php:288
msgid "No shipping class cost"
msgstr ""

#: views/shipping/wcfmmp-view-edit-method-popup.php:307
msgid "Calculation type"
msgstr ""

#: views/shipping/wcfmmp-view-edit-method-popup.php:313
msgid "Per class: Charge shipping for each shipping class individually"
msgstr ""

#: views/shipping/wcfmmp-view-edit-method-popup.php:314
msgid "Per order: Charge shipping for the most expensive shipping class"
msgstr ""

#: views/shipping/wcfmmp-view-edit-method-popup.php:332
msgid "Save Method Settings"
msgstr ""

#: views/shipping/wcfmmp-view-shipping-info.php:11
msgid "Item will be shipped in"
msgstr ""

#: views/shipping/wcfmmp-view-shipping-settings.php:53
msgid "Check this if you want to enable shipping for your store"
msgstr ""

#: views/shipping/wcfmmp-view-shipping-settings.php:56
msgid "Shipping Type"
msgstr ""

#: views/shipping/wcfmmp-view-shipping-settings.php:63
msgid "Select shipping type for your store"
msgstr ""

#: views/shipping/wcfmmp-view-shipping-settings.php:74
msgid "Shipping By Country is disabled by Admin. Please contact admin for details"
msgstr ""

#: views/shipping/wcfmmp-view-shipping-settings.php:179
msgid "Region(s)"
msgstr ""

#: views/shipping/wcfmmp-view-shipping-settings.php:210
msgid "No method found&nbsp;"
msgstr ""

#: views/shipping/wcfmmp-view-shipping-settings.php:211
msgid " Add Shipping Methods"
msgstr ""

#: views/shipping/wcfmmp-view-shipping-settings.php:215
msgid " Edit Shipping Methods"
msgstr ""

#: views/shipping/wcfmmp-view-shipping-settings.php:229
msgid ""
"No shipping zone found for configuration. Please contact with admin for "
"manage your store shipping"
msgstr ""

#: views/shipping/wcfmmp-view-shipping-settings.php:245
msgid "Shipping By Weight is disabled by Admin. Please contact admin for details"
msgstr ""

#: views/shipping/wcfmmp-view-shipping-settings.php:326
msgid "Country default cost if no matching rule"
msgstr ""

#: views/shipping/wcfmmp-view-shipping-settings.php:382
msgid "Shipping By Distance is disabled by Admin. Please contact admin for details"
msgstr ""

#: views/store/widgets/wcfmmp-view-store-category.php:23
msgid "All Categories"
msgstr ""

#: views/store/widgets/wcfmmp-view-store-product-search.php:18
msgid "Search for:"
msgstr ""

#: views/store/widgets/wcfmmp-view-store-product-search.php:19
msgid "Search products&hellip;"
msgstr ""

#: views/store/widgets/wcfmmp-view-store-shipping-rules.php:29
msgid "Shipping Rules:"
msgstr ""

#: views/store/widgets/wcfmmp-view-store-shipping-rules.php:45
msgid "Available for shopping more than <b>%s%d</b>."
msgstr ""

#: views/store/widgets/wcfmmp-view-store-shipping-rules.php:48
msgid "Available"
msgstr ""

#: views/store/widgets/wcfmmp-view-store-shipping-rules.php:66
msgid "Delivery Time"
msgstr ""

#: views/store/widgets/wcfmmp-view-store-taxonomy.php:24
msgid "Show All"
msgstr ""

#: views/store-lists/wcfmmp-view-store-lists-card.php:106
msgid "away"
msgstr ""

#: views/store-lists/wcfmmp-view-store-lists-card.php:109
msgid "You are here!"
msgstr ""

#: views/store-lists/wcfmmp-view-store-lists-card.php:121
msgid "products"
msgstr ""

#: views/store-lists/wcfmmp-view-store-lists-card.php:140
msgid "Visit <span>Store</span>"
msgstr ""

#: views/store-lists/wcfmmp-view-store-lists-loop.php:66
msgid "No store found!"
msgstr ""

#: views/store-lists/wcfmmp-view-store-lists-orderby.php:43
msgid "Sort by newness: old to new"
msgstr ""

#: views/store-lists/wcfmmp-view-store-lists-orderby.php:44
msgid "Sort by newness: new to old"
msgstr ""

#: views/store-lists/wcfmmp-view-store-lists-orderby.php:45
msgid "Sort by average rating: low to high"
msgstr ""

#: views/store-lists/wcfmmp-view-store-lists-orderby.php:46
msgid "Sort by average rating: high to low"
msgstr ""

#: views/store-lists/wcfmmp-view-store-lists-orderby.php:47
msgid "Sort Alphabetical: A to Z"
msgstr ""

#: views/store-lists/wcfmmp-view-store-lists-orderby.php:48
msgid "Sort Alphabetical: Z to A"
msgstr ""

#: views/store-lists/wcfmmp-view-store-lists-orderby.php:65
msgid "Showing %s–%s of %s results"
msgstr ""

#: views/store-lists/wcfmmp-view-store-lists-search-form.php:86
msgid "Search Results for: %s"
msgstr ""

#: views/store-lists/wcfmmp-view-store-lists-sidebar.php:36
msgid "Filter by Category"
msgstr ""

#: views/store-lists/wcfmmp-view-store-lists-sidebar.php:40
#: views/store-lists/wcfmmp-view-store-lists-sidebar.php:42
msgid "Filter by Location"
msgstr ""

#. Plugin Name of the plugin/theme
msgid "WCFM - WooCommerce Multivendor Marketplace"
msgstr ""

#. Plugin URI of the plugin/theme
msgid "https://wclovers.com/knowledgebase_category/wcfm-marketplace/"
msgstr ""

#. Description of the plugin/theme
msgid ""
"Most featured and flexible marketplace solution for your e-commerce store. "
"Simply and Smoothly."
msgstr ""

#. Author of the plugin/theme
msgid "WC Lovers"
msgstr ""

#. Author URI of the plugin/theme
msgid "https://wclovers.com"
msgstr ""

#: core/class-wcfmmp-frontend.php:315
msgctxt "placeholder"
msgid "Insert your address .."
msgstr ""

#: helpers/class-wcfmmp-install.php:102
msgctxt "page_slug"
msgid "vendor-membership"
msgstr ""

#: helpers/class-wcfmmp-install.php:103
msgctxt "page_slug"
msgid "vendor-register"
msgstr ""

#: helpers/class-wcfmmp-store-setup.php:124
msgctxt "enhanced select"
msgid "No matches found"
msgstr ""

#: helpers/class-wcfmmp-store-setup.php:125
msgctxt "enhanced select"
msgid "Loading failed"
msgstr ""

#: helpers/class-wcfmmp-store-setup.php:126
msgctxt "enhanced select"
msgid "Please enter 1 or more characters"
msgstr ""

#: helpers/class-wcfmmp-store-setup.php:127
msgctxt "enhanced select"
msgid "Please enter %qty% or more characters"
msgstr ""

#: helpers/class-wcfmmp-store-setup.php:128
msgctxt "enhanced select"
msgid "Please delete 1 character"
msgstr ""

#: helpers/class-wcfmmp-store-setup.php:129
msgctxt "enhanced select"
msgid "Please delete %qty% characters"
msgstr ""

#: helpers/class-wcfmmp-store-setup.php:130
msgctxt "enhanced select"
msgid "You can only select 1 item"
msgstr ""

#: helpers/class-wcfmmp-store-setup.php:131
msgctxt "enhanced select"
msgid "You can only select %qty% items"
msgstr ""

#: helpers/class-wcfmmp-store-setup.php:132
msgctxt "enhanced select"
msgid "Loading more results&hellip;"
msgstr ""

#: helpers/class-wcfmmp-store-setup.php:133
msgctxt "enhanced select"
msgid "Searching&hellip;"
msgstr ""

#: helpers/class-wcfmmp-store-setup.php:163
msgctxt "woocommerce"
msgid "Select an option..."
msgstr ""

#: includes/shipping-gateways/class-wcfmmp-shipping-by-country.php:90
#: includes/shipping-gateways/class-wcfmmp-shipping-by-distance.php:202
#: includes/shipping-gateways/class-wcfmmp-shipping-by-weight.php:206
#: includes/shipping-gateways/class-wcfmmp-shipping-by-zone.php:204
msgctxt "Tax status"
msgid "None"
msgstr ""

#: views/store/widgets/wcfmmp-view-store-product-search.php:20
msgctxt "submit button"
msgid "Search"
msgstr ""