# ACF Taxonomy CSV Import for WooCommerce

A powerful WordPress plugin that dynamically handles ACF (Advanced Custom Fields) taxonomy imports during WooCommerce CSV import/export operations, similar to how the default categories work.

## Features

- **ACF-Specific Detection**: Automatically detects ACF-created product taxonomies
- **Manual Override**: Manually specify ACF taxonomies if auto-detection fails
- **Flexible Mapping**: Provides mapping options just like default WooCommerce categories
- **Auto-Creation**: Optionally creates new taxonomy terms during import
- **Export Support**: Includes ACF taxonomies in product exports
- **Configurable**: Per-taxonomy settings for separators, creation rules, and more
- **Logging**: Optional logging for debugging and monitoring
- **Admin Interface**: Easy-to-use settings page with ACF taxonomy detection status

## Installation

1. Upload the plugin folder to `/wp-content/plugins/`
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Go to WooCommerce > Taxonomy Import to configure settings

## How It Works

### Import Process

1. **Detection**: The plugin automatically detects all custom taxonomies registered for products
2. **Mapping**: During CSV import, you'll see all custom taxonomies as mapping options
3. **Parsing**: CSV data is parsed based on configured separators (default: comma)
4. **Term Creation**: New terms are created if they don't exist (configurable)
5. **Assignment**: Terms are assigned to products during import

### Export Process

1. **Column Addition**: Custom taxonomy columns are added to export options
2. **Data Export**: Taxonomy terms are exported with configurable separators

## CSV Format

### Import Format
Your CSV should include columns for each custom taxonomy you want to import:

```csv
ID,Name,SKU,brands,pattern_look,texture,colors
1,"Product 1","SKU001","Brand A,Brand B","Modern,Classic","Smooth","Red,Blue"
2,"Product 2","SKU002","Brand C","Vintage","Rough","Green"
```

### Supported Separators
- Comma (`,`) - default
- Pipe (`|`)
- Semicolon (`;`)
- Custom separator (configurable per taxonomy)

## Configuration

### General Settings
- **Enable Logging**: Turn on/off logging for import/export operations

### Per-Taxonomy Settings
For each detected custom taxonomy, you can configure:

- **Include in Import/Export**: Enable/disable the taxonomy for CSV operations
- **Create New Terms**: Automatically create terms that don't exist during import
- **Append Terms**: Add to existing terms instead of replacing them
- **Import Separator**: Character used to separate multiple terms in CSV
- **Export Separator**: Character used in exported CSV files

## Usage Examples

### Basic Import
1. Prepare your CSV with custom taxonomy columns
2. Go to WooCommerce > Products > Import
3. Upload your CSV file
4. Map your custom taxonomy columns (they'll appear automatically)
5. Run the import

### Advanced Configuration
1. Go to WooCommerce > Taxonomy Import
2. Configure settings for each taxonomy:
   - Set custom separators if needed
   - Enable/disable term creation
   - Configure append vs replace behavior

### Export with Custom Taxonomies
1. Go to WooCommerce > Products > Export
2. Select which columns to export (custom taxonomies will be available)
3. Download your CSV with custom taxonomy data

## Compatibility

### Detected Taxonomies
The plugin automatically works with any custom taxonomy registered for products, including:
- Custom taxonomies from themes
- Taxonomies from other plugins
- Manually registered taxonomies

### Excluded Taxonomies
The following default WooCommerce taxonomies are excluded (handled by WooCommerce core):
- `product_cat` (Categories)
- `product_tag` (Tags)
- `product_shipping_class` (Shipping Classes)
- `product_visibility` (Visibility)
- `wcpv_product_vendors` (Vendors)

## Troubleshooting

### Common Issues

1. **Taxonomies not appearing**: 
   - Check if taxonomies are properly registered for products
   - Verify they're not in the excluded list
   - Check plugin settings to ensure they're enabled

2. **Terms not being created**:
   - Enable "Create New Terms" in settings
   - Check error logs if logging is enabled
   - Verify CSV format and separators

3. **Import not working**:
   - Check CSV column mapping
   - Verify separator settings
   - Enable logging to see detailed error messages

### Debug Information
Enable logging in the plugin settings to get detailed information about:
- Which taxonomies are being processed
- Term creation attempts
- Assignment operations
- Any errors encountered

## Hooks and Filters

### Available Filters
- `dtci_custom_taxonomies`: Modify the list of detected taxonomies
- `dtci_term_creation_args`: Modify arguments when creating new terms
- `dtci_import_separator`: Override separator for specific taxonomy
- `dtci_export_format`: Modify export format for taxonomy data

### Example Usage
```php
// Exclude specific taxonomy from processing
add_filter('dtci_custom_taxonomies', function($taxonomies) {
    unset($taxonomies['unwanted_taxonomy']);
    return $taxonomies;
});

// Custom term creation arguments
add_filter('dtci_term_creation_args', function($args, $term_name, $taxonomy) {
    if ($taxonomy === 'brands') {
        $args['description'] = 'Auto-created brand: ' . $term_name;
    }
    return $args;
}, 10, 3);
```

## Requirements

- WordPress 5.0+
- WooCommerce 5.0+
- PHP 7.4+

## Support

For support and feature requests, please contact the plugin developer or check the plugin documentation.

## Changelog

### 1.0.0
- Initial release
- Dynamic taxonomy detection
- Import/export functionality
- Admin settings interface
- Logging system
