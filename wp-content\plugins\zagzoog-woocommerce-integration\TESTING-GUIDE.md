# Zagzoog WooCommerce Integration - Testing Guide

## 🔍 **Understanding the Log Response**

Your log shows:
```json
{
    "timestamp": "2025-07-03 11:15:36",
    "order_id": 17725,
    "request_data": {
        "orderid": "17725",
        "firstname": "ABC",
        "lastname": "<PERSON>",
        "mobile": "+************",
        "email": "<EMAIL>",
        "shippingcity": "Est omnis sed dolor",
        "shippingaddress": "959 East Oak Freeway, Jubail",
        "products": []  // ← This was empty (now fixed)
    },
    "response": "{\"msg\":\"success\",\"desc\":80850}",
    "response_code": ""
}
```

### **✅ Success Indicators:**
- **Response**: `{"msg":"success","desc":80850}`
- **Zagzoog Order ID**: `80850` (this is your reference number)
- **Status**: Order was successfully received by Zagzoog

### **❌ Issue Fixed:**
- **Empty products array** - Now fixed to include product SKU/ID and quantity

---

## 🧪 **Testing Webhook with Postman**

### **Step 1: Setup Postman Request**

**Method:** `POST`
**URL:** `https://yoursite.com/zagzoog-webhook/`

**Headers:**
```
Content-Type: application/json
```

**Body (JSON):**
```json
{
  "orderid": "17725",
  "status": "Shipped"
}
```

### **Step 2: Test Different Status Updates**

#### **Test 1: Pending Status**
```json
{
  "orderid": "17725",
  "status": "Pending"
}
```
**Expected Result:** Order status changes to "Processing"

#### **Test 2: Shipment Ready**
```json
{
  "orderid": "17725",
  "status": "Shipment Ready"
}
```
**Expected Result:** Order status changes to "Processing"

#### **Test 3: Shipped Status**
```json
{
  "orderid": "17725",
  "status": "Shipped"
}
```
**Expected Result:** Order status changes to "Shipped"

#### **Test 4: Delivered Status**
```json
{
  "orderid": "17725",
  "status": "Delivered"
}
```
**Expected Result:** Order status changes to "Completed"

### **Step 3: Verify Results**

After each Postman request:

1. **Check Order Notes** in WooCommerce admin
2. **Look for:** "Zagzoog status update: [Status]"
3. **Verify:** Order status changed accordingly
4. **Check:** Order meta box shows Zagzoog status

---

## 🔧 **Debugging Products Issue**

### **Before Fix (Empty Products):**
```json
"products": []
```

### **After Fix (With Products):**
```json
"products": [
  {
    "productid": "ZRF93H",
    "quantity": "1"
  },
  {
    "productid": "123",
    "quantity": "2"
  }
]
```

### **Product ID Priority:**
1. **Product SKU** (if available)
2. **Product ID** (if SKU is empty)
3. **Product Name** (as fallback)

---

## 📊 **Monitoring Success**

### **1. Check Order Meta Box**
- Go to **WooCommerce → Orders → Edit Order**
- Look for **"Zagzoog Integration"** meta box
- Shows:
  - API Status
  - Zagzoog Order ID (80850)
  - Products Sent
  - Last Status Update

### **2. Check Order Notes**
Look for notes like:
- ✅ "Order sent to Zagzoog API successfully. Products sent: 2. Zagzoog Order ID: 80850"
- ✅ "Zagzoog status update: Shipped"

### **3. Check Admin Logs**
- Go to **Settings → Zagzoog Integration**
- Review **"Recent API Logs"** section
- Look for successful response codes

### **4. Check WordPress Error Logs**
Look for entries like:
```
Zagzoog Product Debug - Order 17725: Product ID: 123, SKU: ZRF93H, Name: Product Name, Quantity: 1
```

---

## 🎯 **Complete Test Workflow**

### **Step 1: Create Test Order**
1. Add products with SKUs to WooCommerce
2. Create a test order
3. Check if order was sent to Zagzoog

### **Step 2: Verify API Call**
1. Check order notes for success message
2. Note the Zagzoog Order ID
3. Verify products were included in request

### **Step 3: Test Webhook**
1. Use Postman with the webhook URL
2. Send status update for the order
3. Verify order status changed in WooCommerce

### **Step 4: Monitor Results**
1. Check order meta box for updated status
2. Review logs for webhook activity
3. Confirm status mapping worked correctly

---

## 🚨 **Troubleshooting**

### **Empty Products Array:**
- **Fixed:** Plugin now includes product SKU/ID/Name
- **Check:** Product has SKU or valid ID
- **Debug:** Look for product debug logs

### **Webhook Not Working:**
- **URL:** Ensure correct webhook URL format
- **JSON:** Verify Content-Type header
- **Order ID:** Use existing WooCommerce order ID
- **Status:** Use exact status values (case-sensitive)

### **API Failures:**
- **Check:** Network connectivity to Zagzoog
- **Review:** Error logs for specific issues
- **Retry:** Use manual resend button in order meta box

---

## 📋 **Expected Responses**

### **Successful API Call:**
```json
{
  "msg": "success",
  "desc": 80850
}
```

### **Successful Webhook:**
```json
{
  "success": true,
  "data": "Webhook received"
}
```

The plugin is now fixed and ready for production use with proper product data and comprehensive webhook testing capabilities!
