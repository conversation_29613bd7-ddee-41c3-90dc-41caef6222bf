<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class WCFMmpComposerStaticInitfb541153061cc4d3c9a9b69826460016
{
    public static $files = array (
        '7785487378a7be19e13559cd0ddda925' => __DIR__ . '/..' . '/moip/moip-sdk-php/src/Helper/helpers.php',
    );

    public static $prefixLengthsPsr4 = array (
        'M' => 
        array (
            'Moip\\' => 5,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'Moip\\' => 
        array (
            0 => __DIR__ . '/..' . '/moip/moip-sdk-php/src',
        ),
    );

    public static $prefixesPsr0 = array (
        'R' => 
        array (
            'Requests' => 
            array (
                0 => __DIR__ . '/..' . '/rmccue/requests/library',
            ),
        ),
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = WCFMmpComposerStaticInitfb541153061cc4d3c9a9b69826460016::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = WCFMmpComposerStaticInitfb541153061cc4d3c9a9b69826460016::$prefixDirsPsr4;
            $loader->prefixesPsr0 = WCFMmpComposerStaticInitfb541153061cc4d3c9a9b69826460016::$prefixesPsr0;

        }, null, ClassLoader::class);
    }
}
