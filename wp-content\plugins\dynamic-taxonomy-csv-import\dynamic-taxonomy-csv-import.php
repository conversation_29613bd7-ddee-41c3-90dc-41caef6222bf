<?php
/**
 * Plugin Name: Dynamic Taxonomy CSV Import for WooCommerce
 * Plugin URI: https://yoursite.com
 * Description: Dynamically handles ACF (Advanced Custom Fields) taxonomy imports during WooCommerce CSV import, similar to how categories work. Automatically detects ACF taxonomies and provides mapping options.
 * Version: 1.0.0
 * Author: Your Name
 * License: GPL v2 or later
 * Text Domain: dynamic-taxonomy-import
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * WC requires at least: 5.0
 * WC tested up to: 8.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Check if WooCommerce is active - safer method
function dtci_is_woocommerce_active() {
    return class_exists('WooCommerce') || function_exists('WC');
}

if (!dtci_is_woocommerce_active()) {
    return;
}

class Dynamic_Taxonomy_CSV_Import {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('init', array($this, 'init'));
    }
    
    /**
     * Initialize the plugin
     */
    public function init() {
        // Add error handling
        try {
            // Check compatibility first
            if (!$this->is_woocommerce_compatible()) {
                add_action('admin_notices', array($this, 'admin_notices'));
                return;
            }

            // Add import mapping options
            add_filter('woocommerce_csv_product_import_mapping_options', array($this, 'add_taxonomy_import_columns'));

            // Add automatic mapping support
            add_filter('woocommerce_csv_product_import_mapping_default_columns', array($this, 'add_taxonomy_mapping_screen'));

            // Parse taxonomy data from CSV
            add_filter('woocommerce_product_importer_parsed_data', array($this, 'parse_taxonomy_csv_data'), 10, 2);

            // Set taxonomy after product import
            add_filter('woocommerce_product_import_inserted_product_object', array($this, 'set_product_taxonomies'), 10, 2);

            // Add export support
            add_filter('woocommerce_product_export_column_names', array($this, 'add_taxonomy_export_columns'));
            add_filter('woocommerce_product_export_product_default_columns', array($this, 'add_taxonomy_export_columns'));

            // Add dynamic export filters
            add_action('wp_loaded', array($this, 'add_dynamic_export_filters'), 20);

            // Add admin settings
            add_action('admin_menu', array($this, 'add_admin_menu'));
            add_action('admin_init', array($this, 'register_settings'));

        } catch (Exception $e) {
            error_log('Dynamic Taxonomy CSV Import Error in init(): ' . $e->getMessage());
        }
    }
    
    /**
     * Get ACF product taxonomies only
     */
    public function get_custom_product_taxonomies() {
        try {
            $product_taxonomies = get_object_taxonomies('product', 'objects');
            $acf_taxonomies = array();

            // Exclude default WooCommerce taxonomies
            $excluded_taxonomies = array(
                'product_cat',
                'product_tag',
                'product_shipping_class',
                'product_visibility',
                'wcpv_product_vendors'
            );

            if (!is_array($product_taxonomies)) {
                return array();
            }

            foreach ($product_taxonomies as $taxonomy) {
                if (!is_object($taxonomy) || !isset($taxonomy->name)) {
                    continue;
                }

                if (!in_array($taxonomy->name, $excluded_taxonomies)) {
                    // Check if this taxonomy was created by ACF
                    if ($this->is_acf_taxonomy($taxonomy->name)) {
                        // Check if taxonomy should be included based on settings
                        $include_taxonomy = get_option('dtci_include_' . $taxonomy->name, 'yes');
                        if ($include_taxonomy === 'yes') {
                            $acf_taxonomies[$taxonomy->name] = $taxonomy;
                        }
                    }
                }
            }

            return $acf_taxonomies;

        } catch (Exception $e) {
            error_log('Dynamic Taxonomy CSV Import Error in get_custom_product_taxonomies(): ' . $e->getMessage());
            return array();
        }
    }

    /**
     * Check if a taxonomy was created by ACF
     */
    private function is_acf_taxonomy($taxonomy_name) {
        try {
            if (empty($taxonomy_name) || !is_string($taxonomy_name)) {
                return false;
            }

            // Method 1: Check if ACF is active and has this taxonomy registered
            if (function_exists('acf_get_taxonomy')) {
                try {
                    $acf_taxonomy = acf_get_taxonomy($taxonomy_name);
                    if ($acf_taxonomy) {
                        return true;
                    }
                } catch (Exception $e) {
                    // Continue to other methods
                }
            }

            // Method 2: Check ACF field groups for taxonomy fields
            if (function_exists('acf_get_field_groups')) {
                try {
                    $field_groups = acf_get_field_groups();
                    if (is_array($field_groups)) {
                        foreach ($field_groups as $field_group) {
                            if (isset($field_group['location']) && is_array($field_group['location'])) {
                                foreach ($field_group['location'] as $location_group) {
                                    if (is_array($location_group)) {
                                        foreach ($location_group as $location_rule) {
                                            if (isset($location_rule['param']) && isset($location_rule['value']) &&
                                                $location_rule['param'] === 'taxonomy' && $location_rule['value'] === $taxonomy_name) {
                                                return true;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                } catch (Exception $e) {
                    // Continue to other methods
                }
            }

            // Method 3: Check if taxonomy has ACF-specific meta or options
            $taxonomy_object = get_taxonomy($taxonomy_name);
            if ($taxonomy_object && is_object($taxonomy_object)) {
                // Check for ACF-specific patterns in taxonomy registration
                if (isset($taxonomy_object->_acf) ||
                    (isset($taxonomy_object->meta_box_cb) && is_string($taxonomy_object->meta_box_cb) && strpos($taxonomy_object->meta_box_cb, 'acf') !== false)) {
                    return true;
                }
            }

            // Method 4: Check for ACF taxonomy options in database
            $acf_taxonomy_options = get_option('acf_taxonomy_' . $taxonomy_name);
            if ($acf_taxonomy_options) {
                return true;
            }

            // Method 5: Check common ACF taxonomy patterns
            $acf_patterns = array(
                'field_', // ACF field key pattern
                'acf_'    // ACF prefix pattern
            );

            foreach ($acf_patterns as $pattern) {
                if (strpos($taxonomy_name, $pattern) === 0) {
                    return true;
                }
            }

            return false;

        } catch (Exception $e) {
            error_log('Dynamic Taxonomy CSV Import Error in is_acf_taxonomy(): ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get manually specified ACF taxonomies from settings
     */
    public function get_manual_acf_taxonomies() {
        try {
            $manual_taxonomies = get_option('dtci_manual_acf_taxonomies', '');
            if (empty($manual_taxonomies) || !is_string($manual_taxonomies)) {
                return array();
            }

            $taxonomy_names = array_map('trim', explode(',', $manual_taxonomies));
            $taxonomies = array();

            foreach ($taxonomy_names as $taxonomy_name) {
                if (!empty($taxonomy_name) && is_string($taxonomy_name) && taxonomy_exists($taxonomy_name)) {
                    $taxonomy_object = get_taxonomy($taxonomy_name);
                    if ($taxonomy_object && is_object($taxonomy_object) &&
                        isset($taxonomy_object->object_type) && is_array($taxonomy_object->object_type) &&
                        in_array('product', $taxonomy_object->object_type)) {
                        $taxonomies[$taxonomy_name] = $taxonomy_object;
                    }
                }
            }

            return $taxonomies;

        } catch (Exception $e) {
            error_log('Dynamic Taxonomy CSV Import Error in get_manual_acf_taxonomies(): ' . $e->getMessage());
            return array();
        }
    }

    /**
     * Get combined ACF taxonomies (auto-detected + manual)
     */
    public function get_all_acf_taxonomies() {
        try {
            $auto_detected = $this->get_custom_product_taxonomies();
            $manual = $this->get_manual_acf_taxonomies();

            if (!is_array($auto_detected)) {
                $auto_detected = array();
            }
            if (!is_array($manual)) {
                $manual = array();
            }

            // Merge and remove duplicates
            return array_merge($auto_detected, $manual);

        } catch (Exception $e) {
            error_log('Dynamic Taxonomy CSV Import Error in get_all_acf_taxonomies(): ' . $e->getMessage());
            return array();
        }
    }
    
    /**
     * Add taxonomy columns to import mapping options
     */
    public function add_taxonomy_import_columns($columns) {
        $acf_taxonomies = $this->get_all_acf_taxonomies();

        foreach ($acf_taxonomies as $taxonomy_name => $taxonomy) {
            $columns[$taxonomy_name] = $taxonomy->label . ' (ACF)';
        }

        return $columns;
    }

    /**
     * Add automatic mapping support for taxonomy columns
     */
    public function add_taxonomy_mapping_screen($columns) {
        $acf_taxonomies = $this->get_all_acf_taxonomies();

        foreach ($acf_taxonomies as $taxonomy_name => $taxonomy) {
            // Try different variations of column names
            $possible_names = array(
                $taxonomy->label,
                $taxonomy->labels->name,
                $taxonomy->labels->singular_name,
                ucfirst(str_replace('_', ' ', $taxonomy_name)),
                ucwords(str_replace('_', ' ', $taxonomy_name))
            );

            foreach ($possible_names as $name) {
                $columns[$name] = $taxonomy_name;
            }
        }

        return $columns;
    }

    /**
     * Parse taxonomy data from CSV
     */
    public function parse_taxonomy_csv_data($parsed_data, $importer) {
        $acf_taxonomies = $this->get_all_acf_taxonomies();

        foreach ($acf_taxonomies as $taxonomy_name => $taxonomy) {
            if (!empty($parsed_data[$taxonomy_name])) {
                // Split by comma or pipe to handle multiple terms
                $separator = get_option('dtci_separator_' . $taxonomy_name, ',');
                $data = explode($separator, $parsed_data[$taxonomy_name]);

                // Clean up the data
                $data = array_map('trim', $data);
                $data = array_filter($data);

                unset($parsed_data[$taxonomy_name]);

                if (is_array($data)) {
                    $parsed_data[$taxonomy_name] = array();

                    foreach ($data as $term_name) {
                        if (!empty($term_name)) {
                            $parsed_data[$taxonomy_name][] = $term_name;
                        }
                    }
                }
            }
        }

        return $parsed_data;
    }

    /**
     * Set product taxonomies after import
     */
    public function set_product_taxonomies($product, $data) {
        if (!is_a($product, 'WC_Product')) {
            return $product;
        }

        $acf_taxonomies = $this->get_all_acf_taxonomies();

        foreach ($acf_taxonomies as $taxonomy_name => $taxonomy) {
            if (!empty($data[$taxonomy_name])) {
                $term_ids = array();

                foreach ($data[$taxonomy_name] as $term_name) {
                    $term_id = $this->get_or_create_term($term_name, $taxonomy_name, $taxonomy);
                    if ($term_id) {
                        $term_ids[] = $term_id;
                    }
                }

                // Assign terms to product
                if (!empty($term_ids)) {
                    $append = get_option('dtci_append_' . $taxonomy_name, 'no') === 'yes';
                    wp_set_object_terms($product->get_id(), $term_ids, $taxonomy_name, $append);

                    // Log the assignment
                    if (get_option('dtci_enable_logging', 'yes') === 'yes') {
                        error_log("Dynamic Taxonomy Import: Assigned ACF {$taxonomy_name} terms to product {$product->get_id()}: " . implode(', ', $data[$taxonomy_name]));
                    }
                }
            }
        }
        
        return $product;
    }
    
    /**
     * Get or create taxonomy term
     */
    private function get_or_create_term($term_name, $taxonomy_name, $taxonomy) {
        // Check if term exists
        $term = get_term_by('name', $term_name, $taxonomy_name);
        
        if (!$term) {
            // Check if we should create new terms
            $create_terms = get_option('dtci_create_terms_' . $taxonomy_name, 'yes');
            
            if ($create_terms === 'yes') {
                // Create the term if it doesn't exist
                $term_result = wp_insert_term($term_name, $taxonomy_name, array(
                    'slug' => sanitize_title($term_name)
                ));
                
                if (!is_wp_error($term_result)) {
                    if (get_option('dtci_enable_logging', 'yes') === 'yes') {
                        error_log("Dynamic Taxonomy Import: Created {$taxonomy_name} term: {$term_name}");
                    }
                    return $term_result['term_id'];
                }
            }
            return false;
        }
        
        return $term->term_id;
    }
    
    /**
     * Add taxonomy columns to export
     */
    public function add_taxonomy_export_columns($columns) {
        $acf_taxonomies = $this->get_all_acf_taxonomies();

        foreach ($acf_taxonomies as $taxonomy_name => $taxonomy) {
            $include_in_export = get_option('dtci_export_' . $taxonomy_name, 'yes');
            if ($include_in_export === 'yes') {
                $columns[$taxonomy_name] = $taxonomy->label . ' (ACF)';
            }
        }

        return $columns;
    }

    /**
     * Add dynamic export filters for each taxonomy
     */
    public function add_dynamic_export_filters() {
        $acf_taxonomies = $this->get_all_acf_taxonomies();

        foreach ($acf_taxonomies as $taxonomy_name => $taxonomy) {
            add_filter("woocommerce_product_export_product_column_{$taxonomy_name}", array($this, 'export_taxonomy_data'), 10, 2);
        }
    }
    
    /**
     * Export taxonomy data
     */
    public function export_taxonomy_data($value, $product) {
        $current_filter = current_filter();
        $taxonomy_name = str_replace('woocommerce_product_export_product_column_', '', $current_filter);
        
        $terms = get_the_terms($product->get_id(), $taxonomy_name);
        
        if (!is_wp_error($terms) && !empty($terms)) {
            $data = array();
            
            foreach ($terms as $term) {
                $data[] = $term->name;
            }
            
            $separator = get_option('dtci_export_separator_' . $taxonomy_name, ',');
            $value = implode($separator, $data);
        }
        
        return $value;
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_submenu_page(
            'woocommerce',
            __('Dynamic Taxonomy Import', 'dynamic-taxonomy-import'),
            __('Taxonomy Import', 'dynamic-taxonomy-import'),
            'manage_woocommerce',
            'dynamic-taxonomy-import',
            array($this, 'admin_page')
        );
    }
    
    /**
     * Register settings
     */
    public function register_settings() {
        register_setting('dtci_settings', 'dtci_enable_logging');
        register_setting('dtci_settings', 'dtci_manual_acf_taxonomies');

        $acf_taxonomies = $this->get_all_acf_taxonomies();

        foreach ($acf_taxonomies as $taxonomy_name => $taxonomy) {
            register_setting('dtci_settings', 'dtci_include_' . $taxonomy_name);
            register_setting('dtci_settings', 'dtci_create_terms_' . $taxonomy_name);
            register_setting('dtci_settings', 'dtci_append_' . $taxonomy_name);
            register_setting('dtci_settings', 'dtci_separator_' . $taxonomy_name);
            register_setting('dtci_settings', 'dtci_export_' . $taxonomy_name);
            register_setting('dtci_settings', 'dtci_export_separator_' . $taxonomy_name);
        }
    }
    
    /**
     * Admin page content
     */
    public function admin_page() {
        if (isset($_POST['submit'])) {
            // Handle form submission
            foreach ($_POST as $key => $value) {
                if (strpos($key, 'dtci_') === 0) {
                    update_option($key, sanitize_text_field($value));
                }
            }
            echo '<div class="notice notice-success"><p>' . __('Settings saved!', 'dynamic-taxonomy-import') . '</p></div>';
        }
        
        $acf_taxonomies = $this->get_all_acf_taxonomies();
        $auto_detected = $this->get_custom_product_taxonomies();
        $manual = $this->get_manual_acf_taxonomies();
        ?>
        <div class="wrap">
            <h1><?php _e('ACF Taxonomy Import Settings', 'dynamic-taxonomy-import'); ?></h1>
            <p><?php _e('Configure import/export settings for ACF (Advanced Custom Fields) taxonomies only.', 'dynamic-taxonomy-import'); ?></p>

            <form method="post" action="">
                <?php wp_nonce_field('dtci_settings'); ?>

                <h2><?php _e('General Settings', 'dynamic-taxonomy-import'); ?></h2>
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('Enable Logging', 'dynamic-taxonomy-import'); ?></th>
                        <td>
                            <input type="checkbox" name="dtci_enable_logging" value="yes" <?php checked(get_option('dtci_enable_logging', 'yes'), 'yes'); ?> />
                            <p class="description"><?php _e('Enable logging for import/export operations', 'dynamic-taxonomy-import'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Manual ACF Taxonomies', 'dynamic-taxonomy-import'); ?></th>
                        <td>
                            <input type="text" name="dtci_manual_acf_taxonomies" value="<?php echo esc_attr(get_option('dtci_manual_acf_taxonomies', '')); ?>" class="regular-text" />
                            <p class="description"><?php _e('Comma-separated list of ACF taxonomy names to include if auto-detection fails (e.g., brands,colors,texture)', 'dynamic-taxonomy-import'); ?></p>
                        </td>
                    </tr>
                </table>

                <?php if (!empty($auto_detected)): ?>
                <h3><?php _e('Auto-Detected ACF Taxonomies', 'dynamic-taxonomy-import'); ?></h3>
                <p><?php _e('These taxonomies were automatically detected as ACF taxonomies:', 'dynamic-taxonomy-import'); ?></p>
                <ul>
                    <?php foreach ($auto_detected as $taxonomy_name => $taxonomy): ?>
                        <li><strong><?php echo esc_html($taxonomy->label); ?></strong> (<?php echo esc_html($taxonomy_name); ?>)</li>
                    <?php endforeach; ?>
                </ul>
                <?php endif; ?>

                <?php if (!empty($manual)): ?>
                <h3><?php _e('Manually Specified ACF Taxonomies', 'dynamic-taxonomy-import'); ?></h3>
                <p><?php _e('These taxonomies were manually specified:', 'dynamic-taxonomy-import'); ?></p>
                <ul>
                    <?php foreach ($manual as $taxonomy_name => $taxonomy): ?>
                        <li><strong><?php echo esc_html($taxonomy->label); ?></strong> (<?php echo esc_html($taxonomy_name); ?>)</li>
                    <?php endforeach; ?>
                </ul>
                <?php endif; ?>

                <?php if (empty($acf_taxonomies)): ?>
                <div class="notice notice-warning">
                    <p><?php _e('No ACF taxonomies detected. Please ensure ACF is installed and you have created taxonomies, or manually specify them above.', 'dynamic-taxonomy-import'); ?></p>
                </div>
                <?php endif; ?>

                <h2><?php _e('ACF Taxonomy Settings', 'dynamic-taxonomy-import'); ?></h2>
                <?php foreach ($acf_taxonomies as $taxonomy_name => $taxonomy): ?>
                    <h3><?php echo esc_html($taxonomy->label); ?> (<?php echo esc_html($taxonomy_name); ?>)</h3>
                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Include in Import/Export', 'dynamic-taxonomy-import'); ?></th>
                            <td>
                                <input type="checkbox" name="dtci_include_<?php echo esc_attr($taxonomy_name); ?>" value="yes" <?php checked(get_option('dtci_include_' . $taxonomy_name, 'yes'), 'yes'); ?> />
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><?php _e('Create New Terms', 'dynamic-taxonomy-import'); ?></th>
                            <td>
                                <input type="checkbox" name="dtci_create_terms_<?php echo esc_attr($taxonomy_name); ?>" value="yes" <?php checked(get_option('dtci_create_terms_' . $taxonomy_name, 'yes'), 'yes'); ?> />
                                <p class="description"><?php _e('Automatically create terms that don\'t exist during import', 'dynamic-taxonomy-import'); ?></p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><?php _e('Append Terms', 'dynamic-taxonomy-import'); ?></th>
                            <td>
                                <input type="checkbox" name="dtci_append_<?php echo esc_attr($taxonomy_name); ?>" value="yes" <?php checked(get_option('dtci_append_' . $taxonomy_name, 'no'), 'yes'); ?> />
                                <p class="description"><?php _e('Append to existing terms instead of replacing them', 'dynamic-taxonomy-import'); ?></p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><?php _e('Import Separator', 'dynamic-taxonomy-import'); ?></th>
                            <td>
                                <input type="text" name="dtci_separator_<?php echo esc_attr($taxonomy_name); ?>" value="<?php echo esc_attr(get_option('dtci_separator_' . $taxonomy_name, ',')); ?>" class="small-text" />
                                <p class="description"><?php _e('Character used to separate multiple terms in CSV (default: comma)', 'dynamic-taxonomy-import'); ?></p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><?php _e('Include in Export', 'dynamic-taxonomy-import'); ?></th>
                            <td>
                                <input type="checkbox" name="dtci_export_<?php echo esc_attr($taxonomy_name); ?>" value="yes" <?php checked(get_option('dtci_export_' . $taxonomy_name, 'yes'), 'yes'); ?> />
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><?php _e('Export Separator', 'dynamic-taxonomy-import'); ?></th>
                            <td>
                                <input type="text" name="dtci_export_separator_<?php echo esc_attr($taxonomy_name); ?>" value="<?php echo esc_attr(get_option('dtci_export_separator_' . $taxonomy_name, ',')); ?>" class="small-text" />
                                <p class="description"><?php _e('Character used to separate multiple terms in export (default: comma)', 'dynamic-taxonomy-import'); ?></p>
                            </td>
                        </tr>
                    </table>
                    <hr />
                <?php endforeach; ?>
                
                <?php submit_button(); ?>
            </form>
        </div>
        <?php
    }

    /**
     * Plugin activation
     */
    public static function activate() {
        try {
            // Set default options
            if (get_option('dtci_enable_logging') === false) {
                update_option('dtci_enable_logging', 'yes');
            }

            // Set default manual ACF taxonomies based on existing hardcoded ones
            if (get_option('dtci_manual_acf_taxonomies') === false) {
                $default_acf_taxonomies = 'brands,pattern_look,texture,durability,quickship,scale,category_type,application,style,format,dimension,enviroment,acoustic,colors';
                update_option('dtci_manual_acf_taxonomies', $default_acf_taxonomies);
            }

            // Set default options for known taxonomies without instantiating the class
            $known_taxonomies = array('brands', 'pattern_look', 'texture', 'durability', 'quickship', 'scale', 'category_type', 'application', 'style', 'format', 'dimension', 'enviroment', 'acoustic', 'colors');

            foreach ($known_taxonomies as $taxonomy_name) {
                // Set default options for each taxonomy
                if (get_option('dtci_include_' . $taxonomy_name) === false) {
                    update_option('dtci_include_' . $taxonomy_name, 'yes');
                }
                if (get_option('dtci_create_terms_' . $taxonomy_name) === false) {
                    update_option('dtci_create_terms_' . $taxonomy_name, 'yes');
                }
                if (get_option('dtci_append_' . $taxonomy_name) === false) {
                    update_option('dtci_append_' . $taxonomy_name, 'no');
                }
                if (get_option('dtci_separator_' . $taxonomy_name) === false) {
                    update_option('dtci_separator_' . $taxonomy_name, ',');
                }
                if (get_option('dtci_export_' . $taxonomy_name) === false) {
                    update_option('dtci_export_' . $taxonomy_name, 'yes');
                }
                if (get_option('dtci_export_separator_' . $taxonomy_name) === false) {
                    update_option('dtci_export_separator_' . $taxonomy_name, ',');
                }
            }

            // Log activation
            error_log('Dynamic ACF Taxonomy CSV Import: Plugin activated successfully');

        } catch (Exception $e) {
            error_log('Dynamic ACF Taxonomy CSV Import: Activation error - ' . $e->getMessage());
        }
    }

    /**
     * Plugin deactivation
     */
    public static function deactivate() {
        // Log deactivation
        error_log('Dynamic Taxonomy CSV Import: Plugin deactivated');

        // Note: We don't delete options on deactivation in case user wants to reactivate
        // Options will be cleaned up on uninstall if an uninstall.php file is created
    }

    /**
     * Get plugin version
     */
    public function get_version() {
        return '1.0.0';
    }

    /**
     * Check if WooCommerce is active and compatible
     */
    public function is_woocommerce_compatible() {
        try {
            if (!class_exists('WooCommerce') && !function_exists('WC')) {
                return false;
            }

            // Simple version check
            if (defined('WC_VERSION') && version_compare(WC_VERSION, '5.0', '>=')) {
                return true;
            }

            // Fallback - if we can't determine version, assume it's compatible
            return class_exists('WooCommerce');

        } catch (Exception $e) {
            error_log('Dynamic Taxonomy CSV Import Error in is_woocommerce_compatible(): ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Display admin notices
     */
    public function admin_notices() {
        if (!$this->is_woocommerce_compatible()) {
            echo '<div class="notice notice-error"><p>';
            echo __('Dynamic Taxonomy CSV Import requires WooCommerce 5.0 or higher.', 'dynamic-taxonomy-import');
            echo '</p></div>';
        }
    }
}

// Activation hook
register_activation_hook(__FILE__, array('Dynamic_Taxonomy_CSV_Import', 'activate'));

// Deactivation hook
register_deactivation_hook(__FILE__, array('Dynamic_Taxonomy_CSV_Import', 'deactivate'));

// Initialize the plugin
new Dynamic_Taxonomy_CSV_Import();
