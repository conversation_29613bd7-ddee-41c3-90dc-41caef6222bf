/* WooCommerce Bundle Products Admin Styles */

/* Bundle Product Tab */
.woocommerce_options_panel#bundle_product_data {
    padding: 12px;
}

.woocommerce_options_panel#bundle_product_data .options_group {
    border-bottom: 1px solid #eee;
    margin-bottom: 20px;
    padding-bottom: 20px;
}

.woocommerce_options_panel#bundle_product_data .options_group:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

/* Bundle Products Container */
#bundle_products_container h4 {
    margin: 0 0 15px 0;
    font-size: 14px;
    font-weight: 600;
    color: #23282d;
}

#bundle_products_list {
    margin-bottom: 15px;
}

/* Bundle Product Rows */
.bundle-product-row {
    background: #fafafa;
    border: 1px solid #e1e1e1;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 10px;
    position: relative;
}

.bundle-product-row:last-child {
    margin-bottom: 0;
}

.bundle-product-row > div {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.bundle-product-row .wc-product-search {
    min-width: 300px;
    flex: 1;
}

.bundle-product-row input[type="number"] {
    width: 80px;
    text-align: center;
}

.bundle-product-row input[name="bundle_discounts[]"] {
    width: 100px;
}

.bundle-product-row label {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 13px;
    color: #666;
    white-space: nowrap;
}

.bundle-product-row .remove-bundle-product {
    background: #dc3232;
    border-color: #dc3232;
    color: white;
    padding: 6px 12px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
}

.bundle-product-row .remove-bundle-product:hover {
    background: #c62d2d;
    border-color: #c62d2d;
}

/* Add Product Button */
#add_bundle_product {
    background: #0073aa;
    border-color: #0073aa;
    color: white;
    padding: 8px 16px;
    border-radius: 3px;
    cursor: pointer;
}

#add_bundle_product:hover {
    background: #005a87;
    border-color: #005a87;
}

/* Bundle Pricing Fields */
.bundle_discount_field {
    display: none;
}

.bundle_discount_field.show {
    display: block;
}

/* Order Admin Styles */
.wc-bundle-order-section {
    background: #f9f9f9;
    padding: 20px;
    margin: 20px 0;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.wc-bundle-order-section h3 {
    margin: 0 0 15px 0;
    font-size: 16px;
    color: #23282d;
}

.wc-bundle-search-container {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.wc-bundle-search-container .wc-bundle-search {
    min-width: 300px;
    flex: 1;
}

#add-bundle-to-order {
    background: #0073aa;
    border-color: #0073aa;
    color: white;
    padding: 8px 16px;
    border-radius: 3px;
    cursor: pointer;
    white-space: nowrap;
}

#add-bundle-to-order:hover {
    background: #005a87;
    border-color: #005a87;
}

#add-bundle-to-order:disabled {
    background: #ccc;
    border-color: #ccc;
    cursor: not-allowed;
}

/* Bundle Preview */
#bundle-preview {
    background: white;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-top: 15px;
}

#bundle-preview h4 {
    margin: 0 0 10px 0;
    font-size: 14px;
    color: #23282d;
}

#bundle-contents ul {
    margin: 0 0 10px 0;
    padding-left: 20px;
}

#bundle-contents li {
    margin-bottom: 5px;
    font-size: 13px;
    color: #666;
}

#bundle-total {
    font-size: 14px;
    color: #0073aa;
    font-weight: 600;
}

/* Select2 Customization */
.select2-container--default .select2-selection--single {
    height: 32px;
    border: 1px solid #ddd;
    border-radius: 3px;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 30px;
    padding-left: 8px;
    color: #32373c;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 30px;
    right: 6px;
}

.select2-dropdown {
    border: 1px solid #ddd;
    border-radius: 3px;
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: #0073aa;
}

/* Responsive Design */
@media (max-width: 768px) {
    .bundle-product-row > div {
        flex-direction: column;
        align-items: stretch;
    }
    
    .bundle-product-row .wc-product-search {
        min-width: auto;
        width: 100%;
    }
    
    .bundle-product-row input[type="number"] {
        width: 100%;
        text-align: left;
    }
    
    .wc-bundle-search-container {
        flex-direction: column;
        align-items: stretch;
    }
    
    .wc-bundle-search-container .wc-bundle-search {
        min-width: auto;
        width: 100%;
    }
}

/* Loading States */
.bundle-loading {
    opacity: 0.6;
    pointer-events: none;
}

.bundle-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: bundle-spin 1s linear infinite;
}

@keyframes bundle-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success/Error Messages */
.bundle-message {
    padding: 10px 15px;
    margin: 10px 0;
    border-radius: 3px;
    font-size: 13px;
}

.bundle-message.success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.bundle-message.error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

/* Tooltips */
.bundle-tooltip {
    position: relative;
    cursor: help;
}

.bundle-tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #333;
    color: white;
    padding: 5px 8px;
    border-radius: 3px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s;
    z-index: 1000;
}

.bundle-tooltip:hover::after {
    opacity: 1;
}
