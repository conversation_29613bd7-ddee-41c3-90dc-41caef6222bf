{"id": "MPY-V5CEK24WDD27", "status": "WAITING", "amount": {"total": 8000, "gross": 8000, "currency": "BRL"}, "installmentCount": 1, "fundingInstrument": {"boleto": {"expirationDate": "2017-09-30", "lineCode": "23793.**********.***********.747904672980000008000", "logoUri": "http://", "instructionLines": {"first": "Primeiralinhaseinstrução", "second": "Segundalinhaseinstrução", "third": "Terceiralinhaseinstrução"}}, "method": "BOLETO"}, "payments": [{"id": "PAY-ATNIHH3XI4KJ", "status": "WAITING", "delayCapture": false, "amount": {"total": 4000, "gross": 4000, "fees": 0, "refunds": 0, "liquid": 4000, "currency": "BRL"}, "installmentCount": 1, "fundingInstrument": {"boleto": {"expirationDate": "2017-09-30", "lineCode": "23793.**********.***********.747904672980000008000", "logoUri": "http://", "instructionLines": {"first": "Primeiralinhaseinstrução", "second": "Segundalinhaseinstrução", "third": "Terceiralinhaseinstrução"}}, "method": "BOLETO"}, "fees": [{"type": "TRANSACTION", "amount": 0}], "events": [{"type": "PAYMENT.CREATED", "createdAt": "2017-09-29T10:33:46.000-03"}, {"type": "PAYMENT.WAITING", "createdAt": "2017-09-29T10:33:46.000-03"}], "receivers": [{"moipAccount": {"id": "MPA-E3C8493A06AE", "login": "<EMAIL>", "fullname": "MarcosSantanaSantos"}, "type": "PRIMARY", "amount": {"total": 3500, "refunds": 0}}, {"moipAccount": {"id": "MPA-8D5DBB4EF8B8", "login": "<EMAIL>", "fullname": "CaioGaspar"}, "type": "SECONDARY", "amount": {"total": 500, "fees": 0, "refunds": 0}, "feePayor": false}], "_links": {"self": {"href": "https://sandbox.moip.com.br/v2/payments/PAY-ATNIHH3XI4KJ"}, "order": {"href": "https://sandbox.moip.com.br/v2/orders/ORD-U15QTT5DPYSZ", "title": "ORD-U15QTT5DPYSZ"}}, "createdAt": "2017-09-29T10:33:46.000-03", "updatedAt": "2017-09-29T10:33:46.000-03"}, {"id": "PAY-4YIMXKU1SNHW", "status": "WAITING", "delayCapture": false, "amount": {"total": 4000, "gross": 4000, "fees": 0, "refunds": 0, "liquid": 4000, "currency": "BRL"}, "installmentCount": 1, "fundingInstrument": {"boleto": {"expirationDate": "2017-09-30", "lineCode": "23793.**********.***********.747904672980000008000", "logoUri": "http://", "instructionLines": {"first": "Primeiralinhaseinstrução", "second": "Segundalinhaseinstrução", "third": "Terceiralinhaseinstrução"}}, "method": "BOLETO"}, "fees": [{"type": "TRANSACTION", "amount": 0}], "events": [{"type": "PAYMENT.CREATED", "createdAt": "2017-09-29T10:33:46.000-03"}, {"type": "PAYMENT.WAITING", "createdAt": "2017-09-29T10:33:46.000-03"}], "receivers": [{"moipAccount": {"id": "MPA-8D5DCB4EF8B8", "login": "<EMAIL>", "fullname": "MoipTest"}, "type": "PRIMARY", "amount": {"total": 4000, "refunds": 0}}], "_links": {"self": {"href": "https://sandbox.moip.com.br/v2/payments/PAY-4YIMXKU1SNHW"}, "order": {"href": "https://sandbox.moip.com.br/v2/orders/ORD-Q3R12CHPQPGO", "title": "ORD-Q3R12CHPQPGO"}}, "createdAt": "2017-09-29T10:33:46.000-03", "updatedAt": "2017-09-29T10:33:46.000-03"}], "_links": {"self": {"href": "https://sandbox.moip.com.br/v2/multipayments/MPY-V5CEK24WDD27"}, "multiorder": {"href": "https://sandbox.moip.com.br/v2/multiorders/MOR-R0T7IYR36W3T"}, "checkout": {"payBoleto": {"printHref": "https://checkout-sandbox.moip.com.br/boleto/MPY-V5CEK24WDD27/print", "redirectHref": "https://checkout-sandbox.moip.com.br/boleto/MPY-V5CEK24WDD27"}}}}