<?php
/**
 * The Template for displaying product archives, including the main shop page which is a post type archive
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/archive-product.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see https://docs.woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 3.4.0
 */

defined( 'ABSPATH' ) || exit;

get_header( 'shop' );

/**
 * Hook: woocommerce_before_main_content.
 *
 * @hooked woocommerce_output_content_wrapper - 10 (outputs opening divs for the content)
 * @hooked woocommerce_breadcrumb - 20
 * @hooked WC_Structured_Data::generate_website_data() - 30
 */
do_action( 'woocommerce_before_main_content' );
// Force correct pagination calculation for 4 products per page
add_action('pre_get_posts', 'force_products_per_page_archive');
function force_products_per_page_archive($query) {
    if (!is_admin() && $query->is_main_query()) {
        if (is_shop() || is_product_category() || is_product_tag()) {
            $query->set('posts_per_page', 4);
        }
    }
}

add_action('woocommerce_product_query', 'custom_woocommerce_sort_by_date');
function custom_woocommerce_sort_by_date($query) {
	$query->set('orderby', 'date');
	$query->set('order', 'DESC'); // Change to 'ASC' for oldest first
	$query->set('posts_per_page', 4); // Force 4 products per page
}
?>
	
	<?php 
		$term = get_queried_object();
        $taxonomy = $term->taxonomy;
        $term_id = $term->term_id;  
	if( have_rows('category_banner', $taxonomy . '_' . $term_id) ):?>
	<section class="product-listing-banner">
		<div class="product-listing-banner-inner">
		  	<div class="product-listing-banner-holder">
		  		<?php
					while( have_rows('category_banner', $taxonomy . '_' . $term_id) ): the_row(); 
						$small_title = get_sub_field( 'small_title', $taxonomy . '_' . $term_id);	
						$large_title = get_sub_field( 'large_title', $taxonomy . '_' . $term_id );
						$content_position = get_sub_field( 'content_position', $taxonomy . '_' . $term_id);  
						$title_color = get_sub_field( 'title_color', $taxonomy . '_' . $term_id);
					?>
				        <div class="product-listing-banner-gallery <?php echo $title_color ; ?> <?php echo $content_position ; ?>">
					        <div>
				        	<?php 
				        		$slider_desktop_image = get_sub_field( 'slider_desktop_image', $taxonomy . '_' . $term_id );
				        		if( $slider_desktop_image ): 
					                $image_url = $slider_desktop_image['url'];
					                $image_alt = $slider_desktop_image['alt'];  ?>
					               <img class="desktop-only" src="<?php echo $image_url; ?>" alt="<?php echo $image_alt; ?>">
					            <?php endif;
				        	 	$slider_mobile_image = get_sub_field( 'slider_mobile_image', $taxonomy . '_' . $term_id );
				        	 	 if( $slider_mobile_image ): 
					                $image_url = $slider_mobile_image['url'];
					                $image_alt = $slider_mobile_image['alt'];  ?>
					          		<img class="mobile-only" src="<?php echo $image_url; ?>" alt="<?php echo $image_alt; ?>">
					          	<?php endif; ?>
					        </div>
					        <div class="product-listing-banner-content">
						        <div class="product-listing-banner-content-inner">
						          	<?php if( $small_title ): ?>
						            	<h3><?php echo $small_title; ?></h3>
						            <?php endif; 
						             if( $large_title ): ?>
						            	<h1><?php echo $large_title; ?></h1>
						        	<?php endif; ?>
						            <div class="btn-container">
							            <?php 
							            $button_1 = get_sub_field( 'button_1', $taxonomy . '_' . $term_id);
							            if( $button_1 ): 
							                $link_url = $button_1['url'];  
							                $link_title = $button_1['title'];
							                $link_target = $button_1['target'] ? $link['target'] : '_blank';  ?> 
							              <a class="btn-hisense" href="<?php echo esc_url( $link_url ); ?>"><?php echo esc_html( $link_title ); ?></a>
							            <?php endif; 
							            $button_2 = get_sub_field( 'button_2', $taxonomy . '_' . $term_id);
							            	if( $button_2 ): 
							                $link_url = $button_2['url'];  
							                $link_title = $button_2['title'];
							                $link_target = $button_2['target'] ? $link['target'] : '_blank';  ?> 
							                <a class="btn-hisense" href="<?php echo esc_url( $link_url ); ?>"><?php echo esc_html( $link_title ); ?></a>
							            <?php endif; ?> 
						            </div>
						        </div>
					        </div>
				      	</div><?php 
					endwhile; 
					?>
					</div>
			<div class="product-listing-dots"></div>
		</div>
	</section><?php
	endif;?>  
		    
	<div class="product-listing tab product-listing-new-sec pb_100_fixed">
		<div class="mobile-product-filter"> 
			<div class="product-name">tv</div>
			<div class="product-filter-button">
				<div class="mobile-filter"><?php _e( 'Filter', 'hisense' ); ?></div> 
			</div>
		</div>
		
			<?php if ( woocommerce_product_loop() ) { ?>

				
				<div class="product-filter-container product-filter-container-new-sec">
				<h3> Filter By</h3>
				
				<?php
				// echo do_shortcode('[yith_wcan_filters slug="default-preset"]');
				echo do_shortcode('[br_filters_group group_id=20078]');

				?>
				</div>
				<?php

				/**
				 * Hook: woocommerce_before_shop_loop. 
				 *
				 * @hooked woocommerce_output_all_notices - 10
				 * @hooked woocommerce_result_count - 20
				 * @hooked woocommerce_catalog_ordering - 30
				 */
				//do_action( 'woocommerce_before_shop_loop' );

				woocommerce_product_loop_start();

				if ( wc_get_loop_prop( 'total' ) ) {
					while ( have_posts() ) {
						the_post();

						/**
						 * Hook: woocommerce_shop_loop.
						 */
						do_action( 'woocommerce_shop_loop' );

						wc_get_template_part( 'content', 'product' );
					}
				}

				woocommerce_product_loop_end();

				/**
				 * Hook: woocommerce_after_shop_loop.
				 *
				 * @hooked woocommerce_pagination - 10
				 */
				do_action( 'woocommerce_after_shop_loop' );
				// Custom pagination for better control
				
			} else {
				/**
				 * Hook: woocommerce_no_products_found.
				 *
				 * @hooked wc_no_products_found - 10
				 */
				do_action( 'woocommerce_no_products_found' );
			} ?>
			<script>
				jQuery(document).ready(function(e){
					jQuery(".product-listing-banner").each((function(e,t){
						jQuery(t).find(".product-listing-banner-holder").slick({
							rtl:is_rtl,
							speed:300,
							adaptiveHeight:!0,
							autoplay:!0,
							lazyLoaded:!0,
							arrows:!0,
							autoplaySpeed:5000,
							dots:!0,
							appendDots:jQuery(".product-listing-dots").eq(e)
						});
					}));
					let t = jQuery('.product-listing-tab');
					t.find(".product-listing-img").addClass("trackin");
					t.find("h4").addClass("trackin");
					t.find("h3").addClass("trackin");
					t.find(".product-highlights").addClass("trackin");
					t.find(".product-listing-tab-buttons").addClass("trackin");
					// t.querySelector(".product-highlights-cursor"),r=$(e.target).offset().left-$(e.target).parent().offset().left;anime({targets:o,translateX:r,width:$(e.target).outerWidth(),opacity:1,easing:"cubicBezier(.51,.01,.53,1)",duration:400});

					jQuery(".product-listing-tab").on("mouseenter click", ".product-highlight", function (e) {
						//alert();
                                e.preventDefault();
                                var t = e.target.closest(".product-listing-tab"),
                                    n = t.querySelector(".product-highlights-description"),
                                    i = jQuery(e.target).find(".product-highlight-text").text();
                                (n.innerHTML = i.replace(/\S/g, "<span class='letter'>$&</span>")),
                                    anime.timeline({ loop: !1 }).add({
                                        targets: n.querySelectorAll(".letter"),
                                        opacity: [0, 1],
                                        translateZ: 0,
                                        easing: "easeOutExpo",
                                        duration: 1600,
                                        delay: function (e, t) {
                                            return 10 * t;
                                        },
                                    });
                                var o = t.querySelector(".product-highlights-cursor"),
                                    r = jQuery(e.target).offset().left - jQuery(e.target).parent().offset().left;
                                anime({ targets: o, translateX: r, width: jQuery(e.target).outerWidth(), opacity: 1, easing: "cubicBezier(.51,.01,.53,1)", duration: 400 });
                            });
					
				});
			</script>
		</div>
<?php 
/**
 * Hook: woocommerce_after_main_content.
 *
 * @hooked woocommerce_output_content_wrapper_end - 10 (outputs closing divs for the content)
 */
do_action( 'woocommerce_after_main_content' );

/**
 * Hook: woocommerce_sidebar.
 *
 * @hooked woocommerce_get_sidebar - 10
 */
//do_action( 'woocommerce_sidebar' );

get_footer();