{"name": "moip/moip-sdk-php", "description": "Cliente em PHP para integração server-side com APIs Moip v2", "keywords": ["libraries", "moip", "intermediary", "checkout", "marketplace"], "homepage": "http://dev.moip.com.br", "license": "MIT", "type": "libraries", "support": {"issues": "https://github.com/moip/moip-sdk-php/issues", "source": "https://github.com/moip/moip-sdk-php"}, "authors": [{"name": "<PERSON>", "email": "jeances<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Caio <PERSON>par", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=5.5", "rmccue/requests": ">=1.0"}, "require-dev": {"phpunit/phpunit": "~4.8.35 || ^5.7 || ^6.4", "codacy/coverage": "dev-master"}, "autoload": {"psr-4": {"Moip\\": "src/"}, "files": ["src/Helper/helpers.php"]}, "autoload-dev": {"psr-4": {"Moip\\Tests\\": "tests/"}}, "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "minimum-stability": "dev"}