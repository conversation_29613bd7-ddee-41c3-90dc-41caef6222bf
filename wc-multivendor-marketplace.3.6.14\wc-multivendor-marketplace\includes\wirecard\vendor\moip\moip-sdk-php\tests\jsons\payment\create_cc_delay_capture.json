{"id": "PAY-SNZZN80MZHPX", "status": "IN_ANALYSIS", "delayCapture": true, "amount": {"total": 7300, "gross": 7300, "fees": 0, "refunds": 0, "liquid": 7300, "currency": "BRL"}, "installmentCount": 1, "fundingInstrument": {"creditCard": {"id": "CRC-7D197TPTPYWQ", "brand": "VISA", "first6": "401200", "last4": "1112", "store": true, "holder": {"birthdate": "1988-12-30", "birthDate": "1988-12-30", "taxDocument": {"type": "CPF", "number": "***********"}, "fullname": "<PERSON>"}}, "method": "CREDIT_CARD"}, "fees": [{"type": "TRANSACTION", "amount": 0}], "events": [{"type": "PAYMENT.IN_ANALYSIS", "createdAt": "2017-10-02T09:31:31.080-03"}, {"type": "PAYMENT.CREATED", "createdAt": "2017-10-02T09:31:30.955-03"}], "receivers": [{"moipAccount": {"id": "MPA-8D5DCB4EF8B8", "login": "<EMAIL>", "fullname": "<PERSON><PERSON>"}, "type": "PRIMARY", "amount": {"total": 7300, "refunds": 0}}], "_links": {"self": {"href": "https://sandbox.moip.com.br/v2/payments/PAY-SNZZN80MZHPX"}, "order": {"href": "https://sandbox.moip.com.br/v2/orders/ORD-M91RU6997NRD", "title": "ORD-M91RU6997NRD"}}, "createdAt": "2017-10-02T09:31:30.953-03", "updatedAt": "2017-10-02T09:31:31.080-03"}