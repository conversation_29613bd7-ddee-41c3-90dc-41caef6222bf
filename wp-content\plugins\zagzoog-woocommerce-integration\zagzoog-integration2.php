<?php
/**
 * Plugin Name: Zagzoog WooCommerce Integration
 * Plugin URI: https://hisense.com
 * Description: Integrates WooCommerce orders with Zagzoog API for order processing and status updates
 * Version: 1.0.0
 * Author: Hisense Team
 * License: GPL v2 or later
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class ZagzoogWooCommerceIntegration {

    private $default_api_url = 'https://zagzoog.com/hisenseapi/saveorder.php';
    private $webhook_endpoint = 'zagzoog-webhook';

    // Saudi cities from your registration template
    private $saudi_cities = array(
        'riyadh' => 'Riyadh',
        'jeddah' => 'Jeddah',
        'mecca' => 'Mecca',
        'medina' => 'Medina',
        'dammam' => 'Dammam',
        'taif' => 'Ta\'if',
        'tabuk' => 'Tabuk',
        'al-kharj' => 'Al-Kharj',
        'buraydah' => 'Buraydah',
        'khamis-mushait' => '<PERSON>ham<PERSON>',
        'al-hofuf' => 'Al-Hofu<PERSON>',
        'al-mubarraz' => 'Al-Mubarraz',
        'hafr-al-batin' => 'Hafr Al-Batin',
        'hail' => 'Ha\'il',
        'najran' => 'Najran',
        'abha' => 'Abha',
        'yanbu' => 'Yanbu',
        'khobar' => 'Khobar',
        'arar' => 'Arar',
        'sakaka' => 'Sakaka',
        'al-qatif' => 'Al-Qatif',
        'al-bahah' => 'Al-Bahah',
        'al-jubail' => 'Al-Jubail',
        'jizan' => 'Jizan',
        'al-ula' => 'Al-`Ula',
        'duba' => 'Duba',
        'al-wajh' => 'Al-Wajh',
        'qurayyat' => 'Al Qurayyat',
        'dhahran' => 'Dhahran'
    );
    
    public function __construct() {
        add_action('init', array($this, 'init'));
        // Use the correct hook for frontend orders (after items are saved)
        add_action('woocommerce_checkout_order_processed', array($this, 'send_order_to_zagzoog'), 20, 1);
        // Keep new_order for manual dashboard orders
        add_action('woocommerce_new_order', array($this, 'send_order_to_zagzoog_manual'), 20, 1);
        add_action('woocommerce_order_status_changed', array($this, 'handle_order_status_change'), 10, 4);
        add_action('init', array($this, 'handle_webhook'));
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'register_settings'));
        add_action('woocommerce_order_actions', array($this, 'add_order_action'));
        add_action('woocommerce_order_action_send_to_zagzoog', array($this, 'manual_send_to_zagzoog'));
        add_action('add_meta_boxes', array($this, 'add_order_meta_box'));
        add_action('wp_ajax_zagzoog_resend_order', array($this, 'ajax_resend_order'));
        add_action('wp_ajax_nopriv_zagzoog_webhook', array($this, 'ajax_webhook_handler'));
        add_action('wp_ajax_zagzoog_webhook', array($this, 'ajax_webhook_handler'));
    }
    
    public function init() {
        // Add rewrite rule for webhook
        add_rewrite_rule(
            '^zagzoog-webhook/?$',
            'index.php?zagzoog_webhook=1',
            'top'
        );
        add_filter('query_vars', array($this, 'add_query_vars'));
    }
    
    public function add_query_vars($vars) {
        $vars[] = 'zagzoog_webhook';
        return $vars;
    }
    
    /**
     * Send new order to Zagzoog API
     */
    public function send_order_to_zagzoog($order_id) {
        // Check if WooCommerce is available
        if (!function_exists('wc_get_order')) {
            error_log("ERROR: WooCommerce not available when processing order {$order_id}");
            return;
        }

        $order = wc_get_order($order_id);
        if (!$order) {
            error_log("ERROR: Could not retrieve order {$order_id}");
            return;
        }

        // Check if already processed to avoid duplicates
        if ($order->get_meta('_zagzoog_api_sent')) {
            error_log("Order {$order_id} already sent to Zagzoog API - skipping");
            return;
        }

        error_log("Processing order {$order_id} for Zagzoog API");
        error_log("Hook triggered: " . current_action());

        // Get the appropriate API URL based on city
        $api_url = $this->get_api_url_for_order($order);
        if (!$api_url) {
            $city = $order->get_shipping_city() ?: $order->get_billing_city();
            $order->add_order_note("No API configured for city '{$city}'. Order processed normally without external API call.");
            error_log("No API URL found for order {$order_id} city '{$city}' - order processed normally");
            return;
        }

        // Prepare order data for Zagzoog API
        $order_data = $this->prepare_order_data($order);

        // Send to Zagzoog API
        $response = $this->send_api_request($order_data, $api_url);
        
        // Log the response
        $this->log_api_response($order_id, $order_data, $response, $api_url);
        
        // Store Zagzoog response in order meta
        if ($response && !is_wp_error($response)) {
            $order->update_meta_data('_zagzoog_api_sent', current_time('mysql'));
            $order->update_meta_data('_zagzoog_api_response', $response);

            // Parse response to get Zagzoog order ID
            $response_data = json_decode($response, true);
            if ($response_data && isset($response_data['desc'])) {
                $order->update_meta_data('_zagzoog_order_id', $response_data['desc']);
            }

            $order->save();

            // Create detailed order note
            $products_sent = count($order_data['products']);
            $note = "Order sent to Zagzoog API successfully. Products sent: {$products_sent}";
            if ($response_data && isset($response_data['desc'])) {
                $note .= ". Zagzoog Order ID: {$response_data['desc']}";
            }
            $order->add_order_note($note);
        } else {
            $error_message = is_wp_error($response) ? $response->get_error_message() : 'Unknown error';
            $order->add_order_note('Failed to send order to Zagzoog API: ' . $error_message);
        }
    }
    
    /**
     * Prepare order data in Zagzoog API format
     */
    private function prepare_order_data($order) {
        $products = array();

        error_log("=== Zagzoog Product Debug - Order {$order->get_id()} ===");
        error_log("Order status: " . $order->get_status());
        error_log("Order total: " . $order->get_total());
        error_log("Total order items: " . count($order->get_items()));
        error_log("All order items: " . json_encode($order->get_items()));

        // Check if order items exist but are different types
        $all_items = $order->get_items('line_item');
        error_log("Line items specifically: " . count($all_items));

        foreach ($order->get_items() as $item_id => $item) {
            error_log("Processing item ID: {$item_id}");

            $product = $item->get_product();
            if ($product) {
                $product_sku = $product->get_sku();
                $product_id = $product->get_id();
                $product_name = $product->get_name();
                $quantity = $item->get_quantity();

                error_log("Product details - ID: {$product_id}, SKU: '{$product_sku}', Name: '{$product_name}', Quantity: {$quantity}");

                // Determine product identifier
                $product_identifier = '';
                if (!empty($product_sku)) {
                    $product_identifier = $product_sku;
                    error_log("Using SKU: {$product_sku}");
                } elseif (!empty($product_id)) {
                    $product_identifier = (string)$product_id;
                    error_log("Using Product ID: {$product_id}");
                } else {
                    $product_identifier = $product_name;
                    error_log("Using Product Name: {$product_name}");
                }

                if (!empty($product_identifier)) {
                    $product_data = array(
                        'productid' => (string)$product_identifier,
                        'quantity' => (string)$quantity
                    );

                    $products[] = $product_data;
                    error_log("Added product to array: " . json_encode($product_data));
                } else {
                    error_log("ERROR: Could not determine product identifier for item {$item_id}");
                }
            } else {
                error_log("ERROR: Could not get product object for item {$item_id}");
            }
        }

        error_log("Final products array: " . json_encode($products));
        error_log("Total products to send: " . count($products));
        
        $order_data = array(
            'orderid' => (string)$order->get_id(),
            'firstname' => $order->get_billing_first_name(),
            'lastname' => $order->get_billing_last_name(),
            'mobile' => $order->get_billing_phone(),
            'email' => $order->get_billing_email(),
            'shippingcity' => $order->get_shipping_city() ?: $order->get_billing_city(),
            'shippingaddress' => $this->get_full_shipping_address($order),
            'products' => $products
        );
        
        return $order_data;
    }
    
    /**
     * Get full shipping address
     */
    private function get_full_shipping_address($order) {
        $address_parts = array();
        
        if ($order->get_shipping_address_1()) {
            $address_parts[] = $order->get_shipping_address_1();
        } elseif ($order->get_billing_address_1()) {
            $address_parts[] = $order->get_billing_address_1();
        }
        
        if ($order->get_shipping_address_2()) {
            $address_parts[] = $order->get_shipping_address_2();
        } elseif ($order->get_billing_address_2()) {
            $address_parts[] = $order->get_billing_address_2();
        }
        
        if ($order->get_shipping_state()) {
            $address_parts[] = $order->get_shipping_state();
        } elseif ($order->get_billing_state()) {
            $address_parts[] = $order->get_billing_state();
        }
        
        return implode(', ', array_filter($address_parts));
    }
    
    /**
     * Get API URL for order based on city
     */
    private function get_api_url_for_order($order) {
        // Get city from shipping address first, then billing address
        $city = strtolower(trim($order->get_shipping_city()));
        if (empty($city)) {
            $city = strtolower(trim($order->get_billing_city()));
        }

        if (empty($city)) {
            error_log("No city found for order {$order->get_id()} - skipping API call");
            return null;
        }

        // Normalize city name to match our city keys
        $city = $this->normalize_city_name($city);

        // Get API mappings from settings
        $api_mappings = get_option('zagzoog_city_api_mappings', array());

        // Only return API URL if specifically configured for this city
        if (isset($api_mappings[$city]) && !empty($api_mappings[$city])) {
            error_log("Using configured API URL for city '{$city}': {$api_mappings[$city]}");
            return $api_mappings[$city];
        }

        // No API configured for this city - skip API call
        error_log("No API configured for city '{$city}' - skipping API call");
        return null;
    }

    /**
     * Normalize city name to match our keys
     */
    private function normalize_city_name($city) {
        $city = strtolower(trim($city));

        // Handle common variations
        $city_variations = array(
            'riyadh' => 'riyadh',
            'al-riyadh' => 'riyadh',
            'jeddah' => 'jeddah',
            'jidda' => 'jeddah',
            'makkah' => 'mecca',
            'mecca' => 'mecca',
            'madinah' => 'medina',
            'medina' => 'medina',
            'al-madinah' => 'medina',
            'dammam' => 'dammam',
            'ad-dammam' => 'dammam',
            'khobar' => 'khobar',
            'al-khobar' => 'khobar',
            'dhahran' => 'dhahran',
            'az-zahran' => 'dhahran'
        );

        return isset($city_variations[$city]) ? $city_variations[$city] : $city;
    }

    /**
     * Send API request to Zagzoog
     */
    private function send_api_request($data, $api_url) {
        error_log("Sending request to API: {$api_url}");

        $response = wp_remote_post($api_url, array(
            'method' => 'POST',
            'timeout' => 30,
            'headers' => array(
                'Content-Type' => 'application/json',
            ),
            'body' => json_encode($data)
        ));

        if (is_wp_error($response)) {
            return $response;
        }

        return wp_remote_retrieve_body($response);
    }
    
    /**
     * Log API response for debugging
     */
    private function log_api_response($order_id, $request_data, $response, $api_url = '') {
        $order = wc_get_order($order_id);
        $city = '';
        if ($order) {
            $city = $order->get_shipping_city() ?: $order->get_billing_city();
        }

        $log_entry = array(
            'timestamp' => current_time('mysql'),
            'order_id' => $order_id,
            'city' => $city,
            'api_url' => $api_url,
            'request_data' => $request_data,
            'response' => $response,
            'response_code' => is_wp_error($response) ? 'ERROR' : wp_remote_retrieve_response_code($response)
        );

        error_log('Zagzoog API Log: ' . json_encode($log_entry));

        // Store in database for admin review
        $logs = get_option('zagzoog_api_logs', array());
        $logs[] = $log_entry;

        // Keep only last 100 logs
        if (count($logs) > 100) {
            $logs = array_slice($logs, -100);
        }

        update_option('zagzoog_api_logs', $logs);
    }
    
    /**
     * Handle webhook from Zagzoog for order status updates
     */
    public function handle_webhook() {
        // Check if this is our webhook endpoint
        if (get_query_var('zagzoog_webhook') == '1') {
            // Set proper headers
            header('Content-Type: application/json');

            // Get input data
            $input = file_get_contents('php://input');
            $data = json_decode($input, true);

            // Log webhook received
            error_log('Zagzoog Webhook Received: ' . $input);

            if ($data && isset($data['orderid']) && isset($data['status'])) {
                $result = $this->process_webhook_data($data);
                if ($result) {
                    wp_send_json_success('Webhook processed successfully');
                } else {
                    wp_send_json_error('Failed to process webhook');
                }
            } else {
                wp_send_json_error('Invalid webhook data');
            }

            exit;
        }

        // Alternative check using REQUEST_URI
        if (isset($_SERVER['REQUEST_URI']) && strpos($_SERVER['REQUEST_URI'], '/zagzoog-webhook') !== false) {
            header('Content-Type: application/json');

            $input = file_get_contents('php://input');
            $data = json_decode($input, true);

            error_log('Zagzoog Webhook (URI check): ' . $input);

            if ($data && isset($data['orderid']) && isset($data['status'])) {
                $result = $this->process_webhook_data($data);
                echo json_encode(array('success' => true, 'message' => 'Webhook received'));
            } else {
                echo json_encode(array('success' => false, 'message' => 'Invalid data'));
            }

            exit;
        }
    }
    
    /**
     * Process webhook data from Zagzoog
     */
    private function process_webhook_data($data) {
        $order_id = sanitize_text_field($data['orderid']);
        $zagzoog_status = sanitize_text_field($data['status']);

        error_log("Processing webhook for Order ID: {$order_id}, Status: {$zagzoog_status}");

        $order = wc_get_order($order_id);
        if (!$order) {
            error_log("Order {$order_id} not found");
            return false;
        }

        // Map Zagzoog status to WooCommerce status
        $wc_status = $this->map_zagzoog_status_to_wc($zagzoog_status);

        if ($wc_status) {
            $order->update_status($wc_status, 'Status updated by Zagzoog: ' . $zagzoog_status);
            error_log("Order {$order_id} status updated to: {$wc_status}");
        }

        // Store Zagzoog status in order meta
        $order->update_meta_data('_zagzoog_status', $zagzoog_status);
        $order->update_meta_data('_zagzoog_last_update', current_time('mysql'));
        $order->save();

        $order->add_order_note('Zagzoog status update: ' . $zagzoog_status);

        return true;
    }
    
    /**
     * Map Zagzoog status to WooCommerce status
     */
    private function map_zagzoog_status_to_wc($zagzoog_status) {
        $status_map = array(
            'Pending' => 'pending',           // Maps to "Pending payment"
            'Shipment Ready' => 'processing', // Maps to "Processing"
            'Shipped' => 'processing',        // Maps to "Processing" (since you don't have shipped status)
            'Delivered' => 'completed',       // Maps to "Completed"
            'Refunded' => 'refunded'          // Maps to "Refunded"
        );

        return isset($status_map[$zagzoog_status]) ? $status_map[$zagzoog_status] : null;
    }
    
    /**
     * Handle WooCommerce order status changes
     */
    public function handle_order_status_change($order_id, $old_status, $new_status, $order) {
        // Log status changes for debugging
        error_log("Order {$order_id} status changed from {$old_status} to {$new_status}");
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_options_page(
            'Zagzoog Integration',
            'Zagzoog Integration',
            'manage_options',
            'zagzoog-integration',
            array($this, 'admin_page')
        );
    }
    
    /**
     * Register plugin settings
     */
    public function register_settings() {
        register_setting('zagzoog_settings', 'zagzoog_enable_logging');
        register_setting('zagzoog_settings', 'zagzoog_city_api_mappings');
    }
    
    /**
     * Admin page content
     */
    public function admin_page() {
        // Handle form submission for city API mappings
        if (isset($_POST['save_city_mappings'])) {
            $this->save_city_api_mappings();
        }

        ?>
        <div class="wrap">
            <h1>Zagzoog Integration Settings</h1>

            <div id="zagzoog-tabs">
                <h2 class="nav-tab-wrapper">
                    <a href="#general" class="nav-tab nav-tab-active">General Settings</a>
                    <a href="#city-apis" class="nav-tab">City API Configuration</a>
                    <a href="#logs" class="nav-tab">API Logs</a>
                </h2>

                <!-- General Settings Tab -->
                <div id="general" class="tab-content">
                    <h2>General Settings</h2>
                    <p><strong>How it works:</strong> Orders are only sent to APIs when a specific city has an API configured. If no API is configured for a city, the order is processed normally through WooCommerce without any external API calls.</p>

                    <form method="post" action="options.php">
                        <?php settings_fields('zagzoog_settings'); ?>
                        <?php do_settings_sections('zagzoog_settings'); ?>

                        <table class="form-table">
                            <tr>
                                <th scope="row">Enable Logging</th>
                                <td>
                                    <input type="checkbox" name="zagzoog_enable_logging" value="1" <?php checked(get_option('zagzoog_enable_logging'), 1); ?> />
                                    <p class="description">Enable detailed API logging for debugging</p>
                                </td>
                            </tr>
                        </table>

                        <?php submit_button(); ?>
                    </form>

                    <h3>Current Configuration Status</h3>
                    <?php $this->display_configuration_status(); ?>
                </div>

                <!-- City API Configuration Tab -->
                <div id="city-apis" class="tab-content" style="display: none;">
                    <h2>City-Based API Configuration</h2>
                    <p>Configure different API endpoints for different Saudi cities. Orders will be sent to the appropriate API based on the customer's billing/shipping city.</p>

                    <form method="post" action="">
                        <?php wp_nonce_field('save_city_mappings', 'city_mappings_nonce'); ?>

                        <table class="wp-list-table widefat fixed striped">
                            <thead>
                                <tr>
                                    <th style="width: 30%;">City</th>
                                    <th style="width: 60%;">API URL</th>
                                    <th style="width: 10%;">Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $this->render_city_api_mappings(); ?>
                            </tbody>
                        </table>

                        <p class="submit">
                            <input type="submit" name="save_city_mappings" class="button-primary" value="Save City API Mappings" />
                        </p>
                    </form>
                </div>

                <!-- API Logs Tab -->
                <div id="logs" class="tab-content" style="display: none;">
                    <h2>Recent API Logs</h2>
                    <?php $this->display_recent_logs(); ?>
                </div>
            </div>

            <script>
            jQuery(document).ready(function($) {
                $('.nav-tab').click(function(e) {
                    e.preventDefault();

                    // Remove active class from all tabs
                    $('.nav-tab').removeClass('nav-tab-active');
                    $('.tab-content').hide();

                    // Add active class to clicked tab
                    $(this).addClass('nav-tab-active');

                    // Show corresponding content
                    var target = $(this).attr('href');
                    $(target).show();
                });
            });
            </script>

            <style>
            .tab-content {
                padding: 20px 0;
            }
            .nav-tab-wrapper {
                margin-bottom: 20px;
            }
            </style>
        </div>
        <?php
    }

    /**
     * Render city API mappings table
     */
    private function render_city_api_mappings() {
        $api_mappings = get_option('zagzoog_city_api_mappings', array());

        foreach ($this->saudi_cities as $city_key => $city_name) {
            $api_url = isset($api_mappings[$city_key]) ? $api_mappings[$city_key] : '';
            $status = !empty($api_url) ? '<span style="color: green;">✓ Configured</span>' : '<span style="color: #999;">Not configured</span>';

            echo '<tr>';
            echo '<td><strong>' . esc_html($city_name) . '</strong><br><small>' . esc_html($city_key) . '</small></td>';
            echo '<td><input type="url" name="city_apis[' . esc_attr($city_key) . ']" value="' . esc_attr($api_url) . '" class="regular-text" placeholder="https://api.example.com/saveorder.php" /></td>';
            echo '<td>' . $status . '</td>';
            echo '</tr>';
        }
    }

    /**
     * Save city API mappings
     */
    private function save_city_api_mappings() {
        if (!wp_verify_nonce($_POST['city_mappings_nonce'], 'save_city_mappings')) {
            wp_die('Security check failed');
        }

        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        $city_apis = isset($_POST['city_apis']) ? $_POST['city_apis'] : array();
        $sanitized_mappings = array();

        foreach ($city_apis as $city => $api_url) {
            if (!empty($api_url) && filter_var($api_url, FILTER_VALIDATE_URL)) {
                $sanitized_mappings[sanitize_key($city)] = esc_url_raw($api_url);
            }
        }

        update_option('zagzoog_city_api_mappings', $sanitized_mappings);

        echo '<div class="notice notice-success"><p>City API mappings saved successfully!</p></div>';
    }

    /**
     * Display configuration status
     */
    private function display_configuration_status() {
        $api_mappings = get_option('zagzoog_city_api_mappings', array());
        $configured_count = count(array_filter($api_mappings));
        $total_cities = count($this->saudi_cities);

        echo '<div style="background: #f1f1f1; padding: 15px; border-radius: 5px;">';
        echo '<p><strong>Cities with API configured:</strong> ' . $configured_count . ' out of ' . $total_cities . '</p>';

        if ($configured_count > 0) {
            echo '<p><strong>Configured cities:</strong></p>';
            echo '<ul>';
            foreach ($api_mappings as $city_key => $api_url) {
                if (!empty($api_url)) {
                    $city_name = isset($this->saudi_cities[$city_key]) ? $this->saudi_cities[$city_key] : $city_key;
                    echo '<li>' . esc_html($city_name) . ' → <small>' . esc_html($api_url) . '</small></li>';
                }
            }
            echo '</ul>';
        } else {
            echo '<p style="color: #d63638;"><strong>No cities configured yet.</strong> All orders will be processed normally without API calls.</p>';
        }

        echo '</div>';
    }

    /**
     * Display recent API logs
     */
    private function display_recent_logs() {
        $logs = get_option('zagzoog_api_logs', array());
        $logs = array_reverse(array_slice($logs, -10)); // Show last 10 logs
        
        if (empty($logs)) {
            echo '<p>No logs available.</p>';
            return;
        }
        
        echo '<table class="wp-list-table widefat fixed striped">';
        echo '<thead><tr><th>Timestamp</th><th>Order ID</th><th>City</th><th>API URL</th><th>Response Code</th><th>Details</th></tr></thead>';
        echo '<tbody>';

        foreach ($logs as $log) {
            $city = isset($log['city']) ? $log['city'] : 'N/A';
            $api_url = isset($log['api_url']) ? $log['api_url'] : 'N/A';

            echo '<tr>';
            echo '<td>' . esc_html($log['timestamp']) . '</td>';
            echo '<td><a href="' . admin_url('post.php?post=' . $log['order_id'] . '&action=edit') . '">#' . esc_html($log['order_id']) . '</a></td>';
            echo '<td>' . esc_html($city) . '</td>';
            echo '<td><small>' . esc_html($api_url) . '</small></td>';
            echo '<td>' . esc_html($log['response_code']) . '</td>';
            echo '<td><details><summary>View</summary><pre>' . esc_html(json_encode($log, JSON_PRETTY_PRINT)) . '</pre></details></td>';
            echo '</tr>';
        }
        
        echo '</tbody></table>';
    }

    /**
     * Add manual send action to order actions
     */
    public function add_order_action($actions) {
        $actions['send_to_zagzoog'] = __('Send to Zagzoog API', 'zagzoog-integration');
        return $actions;
    }

    /**
     * Handle manual send to Zagzoog
     */
    public function manual_send_to_zagzoog($order) {
        $this->send_order_to_zagzoog($order->get_id());
    }

    /**
     * Add meta box to order edit page
     */
    public function add_order_meta_box() {
        add_meta_box(
            'zagzoog-order-info',
            'Zagzoog Integration',
            array($this, 'order_meta_box_content'),
            'shop_order',
            'side',
            'default'
        );
    }

    /**
     * Meta box content
     */
    public function order_meta_box_content($post) {
        $order = wc_get_order($post->ID);
        if (!$order) return;

        $api_sent = $order->get_meta('_zagzoog_api_sent');
        $api_response = $order->get_meta('_zagzoog_api_response');
        $zagzoog_status = $order->get_meta('_zagzoog_status');
        $zagzoog_order_id = $order->get_meta('_zagzoog_order_id');
        $last_update = $order->get_meta('_zagzoog_last_update');

        echo '<div class="zagzoog-meta-box">';
        echo '<p><strong>API Status:</strong> ' . ($api_sent ? 'Sent on ' . $api_sent : 'Not sent') . '</p>';

        if ($zagzoog_order_id) {
            echo '<p><strong>Zagzoog Order ID:</strong> ' . esc_html($zagzoog_order_id) . '</p>';
        }

        if ($zagzoog_status) {
            echo '<p><strong>Zagzoog Status:</strong> ' . esc_html($zagzoog_status) . '</p>';
        }

        if ($last_update) {
            echo '<p><strong>Last Update:</strong> ' . esc_html($last_update) . '</p>';
        }

        // Show products that were sent
        $products_data = array();
        foreach ($order->get_items() as $item) {
            $product = $item->get_product();
            if ($product) {
                $product_id = $product->get_sku() ?: $product->get_id();
                $products_data[] = $product_id . ' (Qty: ' . $item->get_quantity() . ')';
            }
        }

        if (!empty($products_data)) {
            echo '<p><strong>Products Sent:</strong><br>' . implode('<br>', $products_data) . '</p>';
        }

        if ($api_response) {
            echo '<details><summary>API Response</summary>';
            echo '<pre style="font-size: 11px; max-height: 200px; overflow-y: auto;">' . esc_html($api_response) . '</pre>';
            echo '</details>';
        }

        echo '<p><button type="button" class="button" onclick="zagzoogResendOrder(' . $order->get_id() . ')">Resend to Zagzoog</button></p>';
        echo '</div>';

        // Add JavaScript for resend functionality
        ?>
        <script>
        function zagzoogResendOrder(orderId) {
            if (confirm('Are you sure you want to resend this order to Zagzoog?')) {
                jQuery.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'zagzoog_resend_order',
                        order_id: orderId,
                        nonce: '<?php echo wp_create_nonce('zagzoog_resend'); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            alert('Order sent to Zagzoog successfully!');
                            location.reload();
                        } else {
                            alert('Error: ' + response.data);
                        }
                    },
                    error: function() {
                        alert('Error sending order to Zagzoog');
                    }
                });
            }
        }
        </script>
        <?php
    }

    /**
     * AJAX handler for manual order resending
     */
    public function ajax_resend_order() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'zagzoog_resend')) {
            wp_send_json_error('Invalid nonce');
            return;
        }

        // Check permissions
        if (!current_user_can('edit_shop_orders')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        $order_id = absint($_POST['order_id']);
        if (!$order_id) {
            wp_send_json_error('Invalid order ID');
            return;
        }

        // Send order to Zagzoog
        $this->send_order_to_zagzoog($order_id);

        wp_send_json_success('Order sent to Zagzoog successfully');
    }

    /**
     * Debug function to check order items manually
     */
    public function debug_order_items($order_id) {
        $order = wc_get_order($order_id);
        if (!$order) {
            error_log("DEBUG: Order {$order_id} not found");
            return;
        }

        error_log("=== DEBUG ORDER {$order_id} ===");
        error_log("Order exists: YES");
        error_log("Order status: " . $order->get_status());
        error_log("Order total: " . $order->get_total());
        error_log("Order date: " . $order->get_date_created()->format('Y-m-d H:i:s'));

        $items = $order->get_items();
        error_log("get_items() count: " . count($items));

        $line_items = $order->get_items('line_item');
        error_log("get_items('line_item') count: " . count($line_items));

        if (!empty($items)) {
            foreach ($items as $item_id => $item) {
                $product = $item->get_product();
                if ($product) {
                    error_log("Item {$item_id}: Product ID {$product->get_id()}, SKU '{$product->get_sku()}', Name '{$product->get_name()}', Qty {$item->get_quantity()}");
                } else {
                    error_log("Item {$item_id}: No product found");
                }
            }
        } else {
            error_log("No items found in order");
        }

        error_log("=== END DEBUG ===");
    }

    /**
     * Alternative AJAX webhook handler (doesn't require rewrite rules)
     */
    public function ajax_webhook_handler() {
        // Get input data
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);

        error_log('Zagzoog AJAX Webhook Received: ' . $input);

        if ($data && isset($data['orderid']) && isset($data['status'])) {
            $result = $this->process_webhook_data($data);
            if ($result) {
                wp_send_json_success('Webhook processed successfully');
            } else {
                wp_send_json_error('Failed to process webhook');
            }
        } else {
            wp_send_json_error('Invalid webhook data');
        }
    }

    /**
     * Handle manual orders created in dashboard
     */
    public function send_order_to_zagzoog_manual($order_id) {
        // Only process if this is from admin dashboard
        if (!is_admin()) {
            error_log("Order {$order_id} created from frontend - skipping woocommerce_new_order hook");
            return;
        }

        error_log("Order {$order_id} created from admin dashboard - processing via woocommerce_new_order");
        $this->send_order_to_zagzoog($order_id);
    }
}

// Initialize the plugin
new ZagzoogWooCommerceIntegration();

// Activation hook
register_activation_hook(__FILE__, function() {
    flush_rewrite_rules();
});

// Deactivation hook
register_deactivation_hook(__FILE__, function() {
    flush_rewrite_rules();
});
