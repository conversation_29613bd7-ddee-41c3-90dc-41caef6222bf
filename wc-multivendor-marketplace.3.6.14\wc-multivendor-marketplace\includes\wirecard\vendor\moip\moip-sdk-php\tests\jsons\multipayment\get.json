{"id": "MPY-BTFCRTZYEVXU", "status": "IN_ANALYSIS", "amount": {"total": 77000, "gross": 77000, "currency": "BRL"}, "installmentCount": 1, "payments": [{"id": "PAY-51DUCPZH7T4O", "status": "AUTHORIZED", "delayCapture": false, "amount": {"total": 45000, "gross": 45000, "fees": 3369, "refunds": 0, "liquid": 41631, "currency": "BRL"}, "installmentCount": 1, "fundingInstrument": {"creditCard": {"id": "CRC-YIWYYPV0W6KJ", "brand": "VISA", "first6": "401200", "last4": "1112", "store": true, "holder": {"birthdate": "1989-06-01", "birthDate": "1989-06-01", "taxDocument": {"type": "CPF", "number": "***********"}, "fullname": "<PERSON>"}}, "method": "CREDIT_CARD"}, "fees": [{"type": "TRANSACTION", "amount": 3369}], "events": [{"type": "PAYMENT.AUTHORIZED", "createdAt": "2017-10-02T11:49:29.000-03"}, {"type": "PAYMENT.CREATED", "createdAt": "2017-10-02T11:49:28.000-03"}, {"type": "PAYMENT.IN_ANALYSIS", "createdAt": "2017-10-02T11:49:28.000-03"}, {"type": "PAYMENT.WAITING", "createdAt": "2017-10-02T11:49:28.000-03"}], "receivers": [{"moipAccount": {"id": "MPA-VB5OGTVPCI52", "login": "<EMAIL>", "fullname": "<PERSON>"}, "type": "PRIMARY", "amount": {"total": 45000, "refunds": 0}}], "_links": {"self": {"href": "https://sandbox.moip.com.br/v2/payments/PAY-51DUCPZH7T4O"}, "order": {"href": "https://sandbox.moip.com.br/v2/orders/ORD-8AHAZIHUH2SH", "title": "ORD-8AHAZIHUH2SH"}}, "createdAt": "2017-10-02T11:49:28.000-03", "updatedAt": "2017-10-02T11:49:29.000-03"}, {"id": "PAY-AVX93WB7A10U", "status": "AUTHORIZED", "delayCapture": false, "amount": {"total": 32000, "gross": 32000, "fees": 2407, "refunds": 0, "liquid": 29593, "currency": "BRL"}, "installmentCount": 1, "fundingInstrument": {"creditCard": {"id": "CRC-KW5IN75IRUHT", "brand": "VISA", "first6": "401200", "last4": "1112", "store": true, "holder": {"birthdate": "1989-06-01", "birthDate": "1989-06-01", "taxDocument": {"type": "CPF", "number": "***********"}, "fullname": "<PERSON>"}}, "method": "CREDIT_CARD"}, "fees": [{"type": "TRANSACTION", "amount": 2407}], "events": [{"type": "PAYMENT.AUTHORIZED", "createdAt": "2017-10-02T11:49:29.000-03"}, {"type": "PAYMENT.CREATED", "createdAt": "2017-10-02T11:49:28.000-03"}, {"type": "PAYMENT.IN_ANALYSIS", "createdAt": "2017-10-02T11:49:28.000-03"}, {"type": "PAYMENT.WAITING", "createdAt": "2017-10-02T11:49:28.000-03"}], "receivers": [{"moipAccount": {"id": "MPA-IFYRB1HBL73Z", "login": "<EMAIL>", "fullname": "Lojista 3 Moip"}, "type": "PRIMARY", "amount": {"total": 32000, "refunds": 0}}], "_links": {"self": {"href": "https://sandbox.moip.com.br/v2/payments/PAY-AVX93WB7A10U"}, "order": {"href": "https://sandbox.moip.com.br/v2/orders/ORD-6RX3N3FK37XE", "title": "ORD-6RX3N3FK37XE"}}, "createdAt": "2017-10-02T11:49:28.000-03", "updatedAt": "2017-10-02T11:49:29.000-03"}], "_links": {"self": {"href": "https://sandbox.moip.com.br/v2/multipayments/MPY-BTFCRTZYEVXU"}, "multiorder": {"href": "https://sandbox.moip.com.br/v2/multiorders/MOR-QVK65HN7MF34"}}}