{"id": "MPY-NKZBOXU4B44U", "status": "AUTHORIZED", "amount": {"total": 8000, "gross": 8000, "currency": "BRL"}, "installmentCount": 1, "payments": [{"id": "PAY-LBUR73TATCIC", "status": "AUTHORIZED", "delayCapture": true, "amount": {"total": 4000, "gross": 4000, "fees": 289, "refunds": 0, "liquid": 3711, "currency": "BRL"}, "installmentCount": 1, "fundingInstrument": {"creditCard": {"id": "CRC-UARTCOXFHAQI", "brand": "VISA", "first6": "401200", "last4": "1112", "store": true, "holder": {"birthdate": "1988-12-30", "birthDate": "1988-12-30", "taxDocument": {"type": "CPF", "number": "***********"}, "fullname": "<PERSON>"}}, "method": "CREDIT_CARD"}, "fees": [{"type": "TRANSACTION", "amount": 289}], "escrows": [{"id": "ECW-ITVY5KCWP3BF", "status": "HOLD_PENDING", "description": "<PERSON>e", "amount": 4000, "createdAt": "2017-10-02T10:12:24.000-03", "updatedAt": "2017-10-02T10:12:24.000-03", "_links": {"self": {"href": "https://sandbox.moip.com.br/v2/escrows/ECW-ITVY5KCWP3BF"}, "order": {"href": "https://sandbox.moip.com.br/v2/orders/ORD-VQ9OAU9CVLRT", "title": "ORD-VQ9OAU9CVLRT"}, "payment": {"href": "https://sandbox.moip.com.br/v2/payments/PAY-LBUR73TATCIC", "title": "PAY-LBUR73TATCIC"}}}], "events": [{"type": "PAYMENT.AUTHORIZED", "createdAt": "2017-10-02T10:13:21.427-03"}, {"type": "PAYMENT.IN_ANALYSIS", "createdAt": "2017-10-02T10:12:25.000-03"}, {"type": "PAYMENT.PRE_AUTHORIZED", "createdAt": "2017-10-02T10:12:25.000-03"}, {"type": "PAYMENT.CREATED", "createdAt": "2017-10-02T10:12:24.000-03"}, {"type": "PAYMENT.WAITING", "createdAt": "2017-10-02T10:12:24.000-03"}], "receivers": [{"moipAccount": {"id": "MPA-E3D8493A06AE", "login": "<EMAIL>", "fullname": "<PERSON><PERSON>"}, "type": "PRIMARY", "amount": {"total": 3500, "refunds": 0}}, {"moipAccount": {"id": "MPA-8D5DCB4EF8B8", "login": "<EMAIL>", "fullname": "Moip Test2"}, "type": "SECONDARY", "amount": {"total": 500, "fees": 0, "refunds": 0}, "feePayor": false}], "_links": {"self": {"href": "https://sandbox.moip.com.br/v2/payments/PAY-LBUR73TATCIC"}, "order": {"href": "https://sandbox.moip.com.br/v2/orders/ORD-VQ9OAU9CVLRT", "title": "ORD-VQ9OAU9CVLRT"}}, "createdAt": "2017-10-02T10:12:24.000-03", "updatedAt": "2017-10-02T10:13:21.426-03"}, {"id": "PAY-6ICL332BUEXT", "status": "AUTHORIZED", "delayCapture": true, "amount": {"total": 4000, "gross": 4000, "fees": 289, "refunds": 0, "liquid": 3711, "currency": "BRL"}, "installmentCount": 1, "fundingInstrument": {"creditCard": {"id": "CRC-UARTCOXFHAQI", "brand": "VISA", "first6": "401200", "last4": "1112", "store": true, "holder": {"birthdate": "1988-12-30", "birthDate": "1988-12-30", "taxDocument": {"type": "CPF", "number": "***********"}, "fullname": "<PERSON>"}}, "method": "CREDIT_CARD"}, "fees": [{"type": "TRANSACTION", "amount": 289}], "escrows": [{"id": "ECW-OX2Z8PFN6EG5", "status": "HOLD_PENDING", "description": "<PERSON>e", "amount": 4000, "createdAt": "2017-10-02T10:12:24.000-03", "updatedAt": "2017-10-02T10:12:24.000-03", "_links": {"self": {"href": "https://sandbox.moip.com.br/v2/escrows/ECW-OX2Z8PFN6EG5"}, "order": {"href": "https://sandbox.moip.com.br/v2/orders/ORD-CMBFPT9AIVPC", "title": "ORD-CMBFPT9AIVPC"}, "payment": {"href": "https://sandbox.moip.com.br/v2/payments/PAY-6ICL332BUEXT", "title": "PAY-6ICL332BUEXT"}}}], "events": [{"type": "PAYMENT.AUTHORIZED", "createdAt": "2017-10-02T10:13:21.428-03"}, {"type": "PAYMENT.IN_ANALYSIS", "createdAt": "2017-10-02T10:12:25.000-03"}, {"type": "PAYMENT.PRE_AUTHORIZED", "createdAt": "2017-10-02T10:12:25.000-03"}, {"type": "PAYMENT.CREATED", "createdAt": "2017-10-02T10:12:24.000-03"}, {"type": "PAYMENT.WAITING", "createdAt": "2017-10-02T10:12:24.000-03"}], "receivers": [{"moipAccount": {"id": "MPA-8D5DBB4EF8B8", "login": "<EMAIL>", "fullname": "Caio <PERSON>par"}, "type": "PRIMARY", "amount": {"total": 4000, "refunds": 0}}], "_links": {"self": {"href": "https://sandbox.moip.com.br/v2/payments/PAY-6ICL332BUEXT"}, "order": {"href": "https://sandbox.moip.com.br/v2/orders/ORD-CMBFPT9AIVPC", "title": "ORD-CMBFPT9AIVPC"}}, "createdAt": "2017-10-02T10:12:24.000-03", "updatedAt": "2017-10-02T10:13:21.427-03"}], "_links": {"self": {"href": "https://sandbox.moip.com.br/v2/multipayments/MPY-NKZBOXU4B44U"}, "multiorder": {"href": "https://sandbox.moip.com.br/v2/multiorders/MOR-GZ5OKB7R36GO"}}}