{"updatedAt": "2015-10-01T17:24:25.160-03", "fee": 0, "amount": 500, "id": "TRA-28HRLYNLMUFH", "transferInstrument": {"method": "BANK_ACCOUNT", "bankAccount": {"id": "BKA-I268MOXX85BF", "agencyNumber": "1111", "holder": {"taxDocument": {"number": "033.575.852-51", "type": "CPF"}, "fullname": "Integração Taxa por canal"}, "accountNumber": "9999", "accountCheckNumber": "8", "bankName": "BANCO DO BRASIL S.A.", "type": "CHECKING", "agencyCheckNumber": "2", "bankNumber": "001"}}, "status": "REQUESTED", "createdAt": "2015-10-01T17:24:25.160-03", "events": [{"createdAt": "2015-10-01T17:24:25.000-03", "description": "Requested", "type": "TRANSFER.REQUESTED"}], "entries": [{"external_id": "ENT-X3CG3TR5DRW4", "scheduledFor": "2015-10-01T17:24:25.000-03", "status": "SETTLED", "moipAccount": {"account": "MPA-UVJPXXO25KYX"}, "fees": [{"amount": 0, "type": "FIXED_FEE"}], "type": "TRANSFER_TO_BANK_ACCOUNT", "grossAmount": -500, "moipAccountId": 9125, "updatedAt": "2015-10-01T17:24:25.835-03", "id": 197194, "installment": {"amount": 1, "number": 1}, "references": [{"value": "APP-GLQRIP429OK7", "type": "CHANNEL"}], "eventId": "TRA-28HRLYNLMUFH", "createdAt": "2015-10-01T17:24:25.775-03", "description": "Transferencia para conta bancaria - Transferencia TRA-28HRLYNLMUFH", "settledAt": "2015-10-01T17:24:25.835-03", "liquidAmount": -500}], "_links": {"self": {"href": "https://sandbox.moip.com.br/v2/transfers/TRA-28HRLYNLMUFH"}}}