{"id": "MPA-7E9B1F907512", "person": {"lastName": "For Tests", "phone": {"areaCode": "11", "countryCode": "55", "number": "*********"}, "parentsName": {"mother": "", "father": ""}, "address": {"zipcode": "01234-000", "zipCode": "01234-000", "street": "Av. <PERSON><PERSON>", "state": "SP", "streetNumber": "2927", "district": "Itaim", "country": "BRA", "city": "São Paulo"}, "taxDocument": {"number": "794.663.228-26", "type": "CPF"}, "name": "Mine", "birthDate": "1990-01-01"}, "transparentAccount": false, "email": {"confirmed": true, "address": "<EMAIL>"}, "createdAt": "2017-10-25T10:57:15.000-02:00", "businessSegment": {"id": 0}, "_links": {"self": {"href": "https://sandbox.moip.com.br/accounts/MPA-7E9B1F907512"}}, "monthlyRevenueId": 0, "softDescriptor": "fortestsmine", "login": "<EMAIL>", "type": "MERCHANT"}