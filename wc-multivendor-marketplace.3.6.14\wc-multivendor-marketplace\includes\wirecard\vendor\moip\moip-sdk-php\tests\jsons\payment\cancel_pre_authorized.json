{"id": "PAY-OJ6HY61197IH", "status": "CANCELLED", "delayCapture": true, "cancellationDetails": {"code": "7", "description": "Política do Moip", "cancelledBy": "MOIP"}, "amount": {"total": 7300, "gross": 7300, "fees": 470, "refunds": 0, "liquid": 6830, "currency": "BRL"}, "installmentCount": 1, "fundingInstrument": {"creditCard": {"id": "CRC-7D197TPTPYWQ", "brand": "VISA", "first6": "401200", "last4": "1112", "store": true, "holder": {"birthdate": "1988-12-30", "birthDate": "1988-12-30", "taxDocument": {"type": "CPF", "number": "***********"}, "fullname": "<PERSON>"}}, "method": "CREDIT_CARD"}, "fees": [{"type": "TRANSACTION", "amount": 470}], "events": [{"type": "PAYMENT.CANCELLED", "createdAt": "2017-10-02T09:54:51.928-03"}, {"type": "PAYMENT.CREATED", "createdAt": "2017-10-02T09:54:30.000-03"}, {"type": "PAYMENT.IN_ANALYSIS", "createdAt": "2017-10-02T09:54:30.000-03"}, {"type": "PAYMENT.PRE_AUTHORIZED", "createdAt": "2017-10-02T09:54:30.000-03"}], "receivers": [{"moipAccount": {"id": "MPA-8D5DCB4EF8B8", "login": "<EMAIL>", "fullname": "<PERSON><PERSON>"}, "type": "PRIMARY", "amount": {"total": 7300, "fees": 0, "refunds": 0}, "feePayor": true}], "_links": {"self": {"href": "https://sandbox.moip.com.br/v2/payments/PAY-OJ6HY61197IH"}, "order": {"href": "https://sandbox.moip.com.br/v2/orders/ORD-QSIHV6257YJK", "title": "ORD-QSIHV6257YJK"}}, "createdAt": "2017-10-02T09:54:30.000-03", "updatedAt": "2017-10-02T09:54:51.927-03"}