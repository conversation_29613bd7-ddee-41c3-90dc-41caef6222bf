{"id": "MPY-1PUFJXHYNREW", "status": "WAITING", "amount": {"total": 8000, "gross": 8000, "currency": "BRL"}, "installmentCount": 1, "payments": [{"id": "PAY-A26ANBMOC22W", "status": "WAITING", "delayCapture": true, "amount": {"total": 4000, "gross": 4000, "fees": 0, "refunds": 0, "liquid": 4000, "currency": "BRL"}, "installmentCount": 1, "fundingInstrument": {"creditCard": {"id": "CRC-UARTCOXFHAQI", "brand": "VISA", "first6": "401200", "last4": "1112", "store": true, "holder": {"birthdate": "1988-12-30", "birthDate": "1988-12-30", "taxDocument": {"type": "CPF", "number": "***********"}, "fullname": "<PERSON>"}}, "method": "CREDIT_CARD"}, "fees": [{"type": "TRANSACTION", "amount": 0}], "escrows": [{"id": "ECW-6AM3SM03KEEK", "status": "HOLD_PENDING", "description": "<PERSON>e", "amount": 4000, "createdAt": "2017-10-02T09:13:36.000-03", "updatedAt": "2017-10-02T09:13:36.000-03", "_links": {"self": {"href": "https://sandbox.moip.com.br/v2/escrows/ECW-6AM3SM03KEEK"}, "order": {"href": "https://sandbox.moip.com.br/v2/orders/ORD-FTD2TF7ZMGH6", "title": "ORD-FTD2TF7ZMGH6"}, "payment": {"href": "https://sandbox.moip.com.br/v2/payments/PAY-A26ANBMOC22W", "title": "PAY-A26ANBMOC22W"}}}], "events": [{"type": "PAYMENT.CREATED", "createdAt": "2017-10-02T09:13:36.000-03"}, {"type": "PAYMENT.WAITING", "createdAt": "2017-10-02T09:13:36.000-03"}], "receivers": [{"moipAccount": {"id": "MPA-E3C8494A06AE", "login": "<EMAIL>", "fullname": "Moip Test2"}, "type": "PRIMARY", "amount": {"total": 3500, "refunds": 0}}, {"moipAccount": {"id": "MPA-8D5DCB4EF8B8", "login": "<EMAIL>", "fullname": "<PERSON><PERSON>"}, "type": "SECONDARY", "amount": {"total": 500, "fees": 0, "refunds": 0}, "feePayor": false}], "_links": {"self": {"href": "https://sandbox.moip.com.br/v2/payments/PAY-A26ANBMOC22W"}, "order": {"href": "https://sandbox.moip.com.br/v2/orders/ORD-FTD2TF7ZMGH6", "title": "ORD-FTD2TF7ZMGH6"}}, "createdAt": "2017-10-02T09:13:36.000-03", "updatedAt": "2017-10-02T09:13:36.000-03"}, {"id": "PAY-2IGF8GV63T34", "status": "WAITING", "delayCapture": true, "amount": {"total": 4000, "gross": 4000, "fees": 0, "refunds": 0, "liquid": 4000, "currency": "BRL"}, "installmentCount": 1, "fundingInstrument": {"creditCard": {"id": "CRC-UARTCOXFHAQI", "brand": "VISA", "first6": "401200", "last4": "1112", "store": true, "holder": {"birthdate": "1988-12-30", "birthDate": "1988-12-30", "taxDocument": {"type": "CPF", "number": "***********"}, "fullname": "<PERSON>"}}, "method": "CREDIT_CARD"}, "fees": [{"type": "TRANSACTION", "amount": 0}], "escrows": [{"id": "ECW-98BNMEVEGPUH", "status": "HOLD_PENDING", "description": "<PERSON>e", "amount": 4000, "createdAt": "2017-10-02T09:13:36.000-03", "updatedAt": "2017-10-02T09:13:36.000-03", "_links": {"self": {"href": "https://sandbox.moip.com.br/v2/escrows/ECW-98BNMEVEGPUH"}, "order": {"href": "https://sandbox.moip.com.br/v2/orders/ORD-2Q0NPHVH0WD0", "title": "ORD-2Q0NPHVH0WD0"}, "payment": {"href": "https://sandbox.moip.com.br/v2/payments/PAY-2IGF8GV63T34", "title": "PAY-2IGF8GV63T34"}}}], "events": [{"type": "PAYMENT.CREATED", "createdAt": "2017-10-02T09:13:36.000-03"}, {"type": "PAYMENT.WAITING", "createdAt": "2017-10-02T09:13:36.000-03"}], "receivers": [{"moipAccount": {"id": "MPA-8D5DBB4EF8B8", "login": "<EMAIL>", "fullname": "Caio <PERSON>par"}, "type": "PRIMARY", "amount": {"total": 4000, "refunds": 0}}], "_links": {"self": {"href": "https://sandbox.moip.com.br/v2/payments/PAY-2IGF8GV63T34"}, "order": {"href": "https://sandbox.moip.com.br/v2/orders/ORD-2Q0NPHVH0WD0", "title": "ORD-2Q0NPHVH0WD0"}}, "createdAt": "2017-10-02T09:13:36.000-03", "updatedAt": "2017-10-02T09:13:36.000-03"}], "_links": {"self": {"href": "https://sandbox.moip.com.br/v2/multipayments/MPY-1PUFJXHYNREW"}, "multiorder": {"href": "https://sandbox.moip.com.br/v2/multiorders/MOR-PNK44ZCR3PVK"}}}