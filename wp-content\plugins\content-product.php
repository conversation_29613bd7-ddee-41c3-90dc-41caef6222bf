<?php
/**
 * The template for displaying product content within loops
 *
 * @package WooCommerce\Templates
 * @version 3.6.0
 */

defined('ABSPATH') || exit;

global $product;

if (empty($product) || !$product->is_visible()) {
    return;
}
?>

<div <?php wc_product_class('product-listing-tab1 product_item common_card_component_sec notify_card', $product); ?> style="width:100%">
    
    <?php
    // Start product link
    // do_action('woocommerce_before_shop_loop_item');
    ?>
	 <div class="common_card_wishlist_sec">
          <!-- <div class="common_card_wishlist_icon active">
            <img src="https://www.hisenseksa.com/wp-content/webp-express/webp-images/uploads/2025/05/heart.png.webp" alt="">
          </div>
          <div class="common_card_wishlist_icon hidden ">

            <img src="https://www.hisenseksa.com/wp-content/webp-express/webp-images/uploads/2025/05/heart-fill.png.webp" alt="">
          </div> -->
          <?php
          echo do_shortcode('[ti_wishlists_addtowishlist loop="yes" product_id="' .$product->get_id() . '"]');
          ?>
        </div>

    <div class="common_card_main_img_sec">
		<div class="common_card_img">
			
    <a href="<?php echo get_the_permalink(); ?>" class="product-image-link">
        <?php
        // Show sale flash if applicable
        do_action('woocommerce_show_product_loop_sale_flash');

        // Show product image or fallback
        if (has_post_thumbnail($product->get_id())) {
            echo get_the_post_thumbnail($product->get_id(), 'woocommerce_thumbnail');
        } else {
            echo '<img src="https://www.hisenseksa.com/wp-content/uploads/2025/06/new-dummy.svg" alt="' . esc_attr(get_the_title()) . '" class="woocommerce-placeholder wp-post-image" />';
        }
        ?>
    </a>
		</div>
		<?php if (!$product->is_in_stock()): ?>
			<div class="out_of_stock_sec">
				<span>OUT OF STOCK</span>
			</div>
		<?php endif; ?>
	</div>

    <div class="product-listing-tab-content1 product_content">
        <?php $listing_page_subtitle = get_field('listing_page_subtitle'); ?>
        <a href="<?php echo get_the_permalink(); ?>" class="cover-link"><h4><?php echo $listing_page_subtitle; ?></h4></a>

        <?php echo '<a href="'.get_the_permalink().'" class="cover-link"><h3 class="' . esc_attr(apply_filters('woocommerce_product_loop_title_classes', 'woocommerce-loop-product__title')) . '">' . get_the_title() . '</h3></a>'; ?>

        <div class="common_card_spec_sec">
		<?php
		if (have_rows('image_and_text', $product_id)):
			$index = 0;
			while (have_rows('image_and_text', $product_id) && $index < 3): the_row();
				$index++;
				$image = get_sub_field('image');
				$text = get_sub_field('text');
		?>
			<div class="common_card_spec_img" id="spec_img_<?php echo $index; ?>">
				<img src="<?php echo esc_url($image['url']); ?>" alt="<?php echo esc_attr($image['alt']); ?>">
			</div>
			<div class="spec_detail_content" id="spec_detail_<?php echo $index; ?>">
				<?php echo esc_html($text); ?>
			</div>
		<?php endwhile; endif; ?>
	</div>

	 <?php
  if ( $product->is_type('variable') ) {
    $available_variations = $product->get_available_variations();
    $attribute_keys = array_keys( $product->get_variation_attributes() );
    $sizes = [];

    // Collect unique sizes
    foreach ( $available_variations as $variation ) {
        foreach ( $attribute_keys as $attribute ) {
            $value = $variation['attributes'][ 'attribute_' . sanitize_title( $attribute ) ];
            if ( !empty( $value ) && !in_array( $value, $sizes ) ) {
                $sizes[] = $value;
            }
        }
    }

    // Limit to 3 sizes
    $sizes = array_slice($sizes, 0, 3);

    // Output
    if ( !empty($sizes) ) {
        echo '<div class="common_card_size_sec">';
        foreach ( $sizes as $size ) {
            echo '<div class="common_card_size_card" style="aspect-ratio:auto">' . esc_html( $size ) . '</div>';
        }
        echo '</div>';
    }
}


  ?>
        <div class="product-highlights-description"></div>
    </div>

    <?php
    /**
     * Show rating and price
     */
    do_action('woocommerce_after_shop_loop_item_title');
    ?>

	<div class="common_card_price_sec">
    <?php
    if ( $product->is_type('variable') ) {
        $regular_price = $product->get_variation_regular_price('min', true);
        $sale_price    = $product->get_variation_sale_price('min', true);
    } else {
        $regular_price = $product->get_regular_price();
        $sale_price    = $product->get_sale_price();
    }

    if ( $sale_price || $regular_price ) :
    ?>
        <h4 class="current_price">
            <?php echo wc_price( $sale_price ? $sale_price : $regular_price ); ?>
        </h4>

        <?php if ( $sale_price && $regular_price && $sale_price < $regular_price ) : ?>
            <h4 class="original_price">
                <?php echo wc_price($regular_price); ?>
            </h4>
        <?php endif; ?>
    <?php endif; ?>
</div>


    <div class="product_listing_buttons">
        <?php
        $coming_soon_button = get_field('coming_soon_button');
        $detail_button = get_field('detail_button');
        if ($detail_button) {
            $button_url = $detail_button['url'];
        }
        ?>

        <a class="btn_outline" href="<?php echo get_the_permalink(); ?>"><?php _e('Details', 'hisense'); ?></a>

        <?php if ($coming_soon_button) { ?>
            <a class="btn_outline_soon"><?php echo $coming_soon_button; ?></a>
        <?php } elseif (!$product->is_in_stock()) { ?>
            <button type="button" id="notify-me-btn" class="notify-me-button show-popup-btn btn_bg_black">
                <?php _e('Notify Me', 'hisense'); ?>
            </button>

            <div id="notify-popup" class="woo-detail-popup notify-popup">
                <div class="popup-inner">
                    <span class="close-btn-popup">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M1 14.2864L14.2864 1" stroke="black" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M14.2864 14.2864L1 1" stroke="black" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </span>
                    <div class="popup-content">
                        <h3><?php _e('Notify When Product is back', 'hisense'); ?></h3>
                        <p><?php _e('Leave your email address below to receive an email as soon as the selected item is back in stock', 'hisense'); ?></p>
                        <form>
                            <div class="notify-input-wrap">
                                <label for="notify-email"><?php _e('Email Address*', 'hisense'); ?></label>
                                <input type="email" id="notify-email">
                                <h4 class="form-error-msg"></h4>
                            </div>
                        </form>
                        <div class="login-notify-btn-wrap">
                            <?php
                            if (is_user_logged_in()) {
                                $userEmail = wp_get_current_user()->user_email;
                            } else { ?>
                                <a href="javascript:void(0);" id="login-notify"><?php _e('Login to continue', 'hisense'); ?></a>
                            <?php } ?>
                            <a href="javascript:void(0);" id="submit-notify"><?php _e('Notify Me', 'hisense'); ?></a>

                            <input type="hidden" class="admin-url" value="<?php echo admin_url("admin-ajax.php"); ?>" />
                            <input type="hidden" class="product-id" value="<?php echo get_the_ID(); ?>" />
                            <input type="hidden" class="current-user-mail" value="<?php echo $userEmail; ?>" />
                        </div>
                        <div class="messages-form"></div>
                    </div>
                </div>
            </div>
        <?php } else {
            $product_id = $product->get_id();

		if ( !$product->is_type( 'variable' ) ) {
			// Direct checkout link with ?add-to-cart=
			$checkout_url = wc_get_checkout_url();
			echo '<a href="' . esc_url( add_query_arg( 'add-to-cart', $product_id, $checkout_url ) ) . '" class="buyNowBtn buy-now-button button alt btn_fill_gradient">Buy Now</a>';
		} else {
			echo '<a href="' . esc_url( get_the_permalink() ) . '" class="button buy-now-button btn_fill_gradient">Select Options</a>';
		}
        }
        ?>
    </div>

    <?php
    // End product link and add to cart
    do_action('woocommerce_after_shop_loop_item');
    ?>
</div>
