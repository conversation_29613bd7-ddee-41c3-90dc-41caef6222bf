jQuery(document).ready(function($) {
    'use strict';

    // Check if wc_bundle_ajax is available
    if (typeof wc_bundle_ajax === 'undefined') {
        console.error('wc_bundle_ajax object not found. Plugin scripts may not be loaded correctly.');
        return;
    }

    console.log('WC Bundle Products admin script loaded', wc_bundle_ajax);

    // Add test button for debugging
    if ($('#bundle_product_data').length) {
        $('#bundle_product_data').prepend('<button type="button" id="test-ajax" class="button">Test AJAX</button>');
        $('#test-ajax').click(function() {
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'test_bundle_ajax'
                },
                success: function(response) {
                    console.log('Test AJAX response:', response);
                    alert('AJAX Test: ' + JSON.stringify(response));
                },
                error: function(xhr, status, error) {
                    console.error('AJAX Test failed:', xhr, status, error);
                    alert('AJAX Test failed: ' + error);
                }
            });
        });
    }

    // Bundle product type handling
    function toggleBundleFields() {
        var productType = $('select#product-type').val();
        if (productType === 'bundle') {
            $('.show_if_bundle').show();
            $('.hide_if_bundle').hide();
        } else {
            $('.show_if_bundle').hide();
            $('.hide_if_bundle').show();
        }
    }
    
    // Initialize on page load
    toggleBundleFields();
    
    // Handle product type change
    $('select#product-type').on('change', toggleBundleFields);
    
    // Bundle pricing type handling
    function togglePricingFields() {
        var pricingType = $('#bundle_pricing_type').val();
        if (pricingType === 'dynamic') {
            $('.bundle_discount_field').show();
            $('#_regular_price, #_sale_price').prop('readonly', true).css('background-color', '#f0f0f0');
        } else {
            $('.bundle_discount_field').hide();
            $('#_regular_price, #_sale_price').prop('readonly', false).css('background-color', '');
        }
    }
    
    // Initialize pricing fields
    togglePricingFields();
    $('#bundle_pricing_type').on('change', togglePricingFields);
    
    // Add product to bundle
    $(document).on('click', '#add_bundle_product', function(e) {
        e.preventDefault();
        
        var rowHtml = '<div class="bundle-product-row" style="margin-bottom: 10px; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">' +
            '<div style="display: flex; align-items: center; gap: 10px;">' +
                '<select name="bundle_products[]" class="wc-product-search" style="width: 300px;" data-placeholder="Search for a product..."></select>' +
                '<input type="number" name="bundle_quantities[]" placeholder="Qty" value="1" min="1" style="width: 60px;" />' +
                '<input type="number" name="bundle_discounts[]" placeholder="Discount %" step="0.01" min="0" max="100" style="width: 100px;" />' +
                '<label><input type="checkbox" name="bundle_optional[]" value="1" /> Optional</label>' +
                '<button type="button" class="remove-bundle-product button" style="background: #dc3232; color: white;">Remove</button>' +
            '</div>' +
        '</div>';
        
        $('#bundle_products_list').append(rowHtml);
        
        // Initialize select2 for the new product search field
        initializeProductSearch($('#bundle_products_list .wc-product-search').last());
    });
    
    // Remove product from bundle
    $(document).on('click', '.remove-bundle-product', function(e) {
        e.preventDefault();
        $(this).closest('.bundle-product-row').remove();
        calculateBundlePrice();
    });
    
    // Initialize existing product search fields
    $('.wc-product-search').each(function() {
        initializeProductSearch($(this));
    });
    
    // Function to initialize product search
    function initializeProductSearch($element) {
        $element.select2({
            ajax: {
                url: ajaxurl,
                dataType: 'json',
                delay: 250,
                data: function (params) {
                    return {
                        q: params.term,
                        action: 'search_products_for_bundle',
                        nonce: wc_bundle_ajax.nonce
                    };
                },
                processResults: function (data) {
                    console.log('Product search response:', data);
                    var terms = [];
                    if (data.success && data.data) {
                        $.each(data.data, function (id, text) {
                            terms.push({id: id, text: text});
                        });
                    } else {
                        console.error('Product search failed:', data);
                    }
                    return {results: terms};
                },
                cache: true
            },
            minimumInputLength: 3,
            placeholder: 'Search for a product...',
            allowClear: true
        });
        
        // Calculate price when product selection changes
        $element.on('select2:select select2:unselect', function() {
            calculateBundlePrice();
        });
    }
    
    // Calculate bundle price for dynamic pricing
    function calculateBundlePrice() {
        if ($('#bundle_pricing_type').val() !== 'dynamic') {
            return;
        }
        
        var totalPrice = 0;
        var bundleDiscount = parseFloat($('#bundle_discount').val()) || 0;
        
        $('.bundle-product-row').each(function() {
            var $row = $(this);
            var productId = $row.find('.wc-product-search').val();
            var quantity = parseInt($row.find('input[name="bundle_quantities[]"]').val()) || 1;
            var discount = parseFloat($row.find('input[name="bundle_discounts[]"]').val()) || 0;
            
            if (productId) {
                // Get product price via AJAX
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    async: false,
                    data: {
                        action: 'get_product_price',
                        product_id: productId,
                        nonce: wc_bundle_ajax.nonce
                    },
                    success: function(response) {
                        if (response.success) {
                            var productPrice = parseFloat(response.data);
                            var discountedPrice = productPrice - (productPrice * discount / 100);
                            totalPrice += discountedPrice * quantity;
                        }
                    }
                });
            }
        });
        
        // Apply bundle discount
        if (bundleDiscount > 0) {
            totalPrice = totalPrice - (totalPrice * bundleDiscount / 100);
        }
        
        // Update price fields
        $('#_regular_price').val(totalPrice.toFixed(2));
        $('#_price').val(totalPrice.toFixed(2));
    }
    
    // Recalculate price when quantities or discounts change
    $(document).on('input', 'input[name="bundle_quantities[]"], input[name="bundle_discounts[]"], #bundle_discount', function() {
        calculateBundlePrice();
    });
    
    // Order admin functionality
    if ($('#bundle-product-search').length) {
        // Initialize bundle product search in order admin
        $('#bundle-product-search').select2({
            ajax: {
                url: ajaxurl,
                dataType: 'json',
                delay: 250,
                data: function (params) {
                    return {
                        q: params.term,
                        action: 'search_bundle_products',
                        nonce: wc_bundle_ajax.nonce
                    };
                },
                processResults: function (data) {
                    var terms = [];
                    if (data.success && data.data) {
                        $.each(data.data, function (id, text) {
                            terms.push({id: id, text: text});
                        });
                    }
                    return {results: terms};
                },
                cache: true
            },
            minimumInputLength: 3,
            placeholder: 'Search for bundle products...'
        });
        
        // Preview bundle contents when selected
        $('#bundle-product-search').on('select2:select', function (e) {
            var bundleId = e.params.data.id;
            if (bundleId) {
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'get_bundle_contents',
                        bundle_id: bundleId,
                        nonce: wc_bundle_ajax.nonce
                    },
                    success: function(response) {
                        if (response.success) {
                            $('#bundle-contents').html(response.data.contents);
                            $('#bundle-total').html('<strong>Total: ' + response.data.total + '</strong>');
                            $('#bundle-preview').show();
                        }
                    }
                });
            }
        });
        
        // Add bundle to order
        $('#add-bundle-to-order').on('click', function(e) {
            e.preventDefault();
            
            var bundleId = $('#bundle-product-search').val();
            if (!bundleId) {
                alert('Please select a bundle product first.');
                return;
            }
            
            var orderId = $('#post_ID').val() || $('input[name="post_ID"]').val();
            if (!orderId) {
                alert('Order ID not found.');
                return;
            }
            
            $(this).prop('disabled', true).text('Adding...');
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'add_bundle_to_order',
                    bundle_id: bundleId,
                    order_id: orderId,
                    nonce: wc_bundle_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        alert('Bundle added to order successfully!');
                        location.reload(); // Reload to show updated order items
                    } else {
                        alert('Error: ' + response.data);
                        $('#add-bundle-to-order').prop('disabled', false).text('Add Bundle to Order');
                    }
                },
                error: function() {
                    alert('An error occurred while adding the bundle to the order.');
                    $('#add-bundle-to-order').prop('disabled', false).text('Add Bundle to Order');
                }
            });
        });
    }
    
    // Style improvements
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .wc-bundle-order-section {
                background: #f9f9f9;
                padding: 15px;
                margin: 15px 0;
                border: 1px solid #ddd;
                border-radius: 4px;
            }
            .wc-bundle-search-container {
                display: flex;
                align-items: center;
                gap: 10px;
                margin-bottom: 10px;
            }
            #bundle-preview {
                background: white;
                padding: 10px;
                border: 1px solid #ddd;
                border-radius: 4px;
            }
            #bundle-contents ul {
                margin: 0;
                padding-left: 20px;
            }
            #bundle-contents li {
                margin-bottom: 5px;
            }
            .bundle-product-row {
                background: #fafafa;
                border: 1px solid #e1e1e1;
                border-radius: 4px;
                padding: 10px;
                margin-bottom: 10px;
            }
            .bundle-product-row:last-child {
                margin-bottom: 0;
            }
        `)
        .appendTo('head');
});
