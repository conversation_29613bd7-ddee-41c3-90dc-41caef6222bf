<?php
/**
 * Plugin Name: WooCommerce Bundle Products
 * Plugin URI: https://hisenseksa.com
 * Description: Custom plugin for creating and managing WooCommerce bundle products with backend order creation support.
 * Version: 1.0.0
 * Author: Hisense KSA
 * Text Domain: wc-bundle-products
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * WC requires at least: 5.0
 * WC tested up to: 8.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Check if WooCommerce is active
if (!in_array('woocommerce/woocommerce.php', apply_filters('active_plugins', get_option('active_plugins')))) {
    return;
}

// Define plugin constants
define('WC_BUNDLE_PRODUCTS_VERSION', '1.0.0');
define('WC_BUNDLE_PRODUCTS_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('WC_BUNDLE_PRODUCTS_PLUGIN_URL', plugin_dir_url(__FILE__));

/**
 * Main WooCommerce Bundle Products Class
 */
class WC_Bundle_Products {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('init', array($this, 'init'));
        add_action('plugins_loaded', array($this, 'load_textdomain'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }
    
    /**
     * Initialize the plugin
     */
    public function init() {
        // Add bundle product type
        add_filter('product_type_selector', array($this, 'add_bundle_product_type'));
        add_filter('woocommerce_product_data_tabs', array($this, 'add_bundle_product_tab'));
        add_action('woocommerce_product_data_panels', array($this, 'add_bundle_product_panel'));
        add_action('woocommerce_process_product_meta', array($this, 'save_bundle_product_data'));
        
        // Admin order creation support
        add_action('woocommerce_admin_order_data_after_order_details', array($this, 'add_bundle_products_to_order'));
        add_action('wp_ajax_add_bundle_to_order', array($this, 'ajax_add_bundle_to_order'));
        add_action('wp_ajax_search_bundle_products', array($this, 'ajax_search_bundle_products'));
        add_action('wp_ajax_search_products_for_bundle', array($this, 'ajax_search_products'));
        add_action('wp_ajax_get_bundle_contents', array($this, 'ajax_get_bundle_contents'));
        add_action('wp_ajax_get_product_price', array($this, 'ajax_get_product_price'));
        add_action('wp_ajax_test_bundle_ajax', array($this, 'ajax_test'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        
        // Frontend display
        add_action('woocommerce_single_product_summary', array($this, 'display_bundle_products'), 25);
        add_filter('woocommerce_add_to_cart_handler', array($this, 'add_to_cart_handler'), 10, 2);
        add_action('woocommerce_add_to_cart', array($this, 'add_bundle_to_cart'), 10, 6);
        
        // Order item display
        add_filter('woocommerce_order_item_name', array($this, 'order_item_name'), 10, 2);
        add_action('woocommerce_checkout_create_order_line_item', array($this, 'add_bundle_data_to_order_item'), 10, 4);
    }
    
    /**
     * Load plugin textdomain
     */
    public function load_textdomain() {
        load_plugin_textdomain('wc-bundle-products', false, dirname(plugin_basename(__FILE__)) . '/languages');
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Create bundle products table
        $this->create_bundle_table();
        flush_rewrite_rules();
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        flush_rewrite_rules();
    }
    
    /**
     * Create bundle products database table
     */
    private function create_bundle_table() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'wc_bundle_products';
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            bundle_id bigint(20) NOT NULL,
            product_id bigint(20) NOT NULL,
            quantity int(11) NOT NULL DEFAULT 1,
            discount_type varchar(20) DEFAULT 'percentage',
            discount_value decimal(10,2) DEFAULT 0.00,
            optional tinyint(1) DEFAULT 0,
            sort_order int(11) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY bundle_id (bundle_id),
            KEY product_id (product_id)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
    
    /**
     * Add bundle product type to selector
     */
    public function add_bundle_product_type($types) {
        $types['bundle'] = __('Bundle Product', 'wc-bundle-products');
        return $types;
    }
    
    /**
     * Add bundle product tab
     */
    public function add_bundle_product_tab($tabs) {
        $tabs['bundle'] = array(
            'label'    => __('Bundle Products', 'wc-bundle-products'),
            'target'   => 'bundle_product_data',
            'class'    => array('show_if_bundle'),
            'priority' => 60,
        );
        return $tabs;
    }
    
    /**
     * Add bundle product panel
     */
    public function add_bundle_product_panel() {
        global $post;
        ?>
        <div id="bundle_product_data" class="panel woocommerce_options_panel">
            <div class="options_group">
                <p class="form-field">
                    <label for="bundle_pricing_type"><?php _e('Bundle Pricing', 'wc-bundle-products'); ?></label>
                    <select id="bundle_pricing_type" name="bundle_pricing_type" class="select short">
                        <option value="fixed" <?php selected(get_post_meta($post->ID, '_bundle_pricing_type', true), 'fixed'); ?>><?php _e('Fixed Price', 'wc-bundle-products'); ?></option>
                        <option value="dynamic" <?php selected(get_post_meta($post->ID, '_bundle_pricing_type', true), 'dynamic'); ?>><?php _e('Dynamic Price (Sum of products)', 'wc-bundle-products'); ?></option>
                    </select>
                </p>
                
                <p class="form-field bundle_discount_field">
                    <label for="bundle_discount"><?php _e('Bundle Discount (%)', 'wc-bundle-products'); ?></label>
                    <input type="number" id="bundle_discount" name="bundle_discount" value="<?php echo esc_attr(get_post_meta($post->ID, '_bundle_discount', true)); ?>" step="0.01" min="0" max="100" />
                </p>
            </div>
            
            <div class="options_group">
                <div id="bundle_products_container">
                    <h4><?php _e('Bundle Products', 'wc-bundle-products'); ?></h4>
                    <div id="bundle_products_list">
                        <?php $this->display_bundle_products_admin($post->ID); ?>
                    </div>
                    <button type="button" id="add_bundle_product" class="button"><?php _e('Add Product', 'wc-bundle-products'); ?></button>
                </div>
            </div>
        </div>
        
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            // Show/hide bundle tab based on product type
            $('select#product-type').change(function() {
                if ($(this).val() === 'bundle') {
                    $('.show_if_bundle').show();
                } else {
                    $('.show_if_bundle').hide();
                }
            }).change();

            // Function to initialize product search
            function initializeProductSearch($element) {
                $element.select2({
                    ajax: {
                        url: ajaxurl,
                        dataType: 'json',
                        delay: 250,
                        data: function (params) {
                            return {
                                q: params.term,
                                action: 'search_products_for_bundle',
                                nonce: wc_bundle_ajax.nonce
                            };
                        },
                        processResults: function (data) {
                            console.log('Product search response:', data);
                            var terms = [];
                            if (data.success && data.data) {
                                $.each(data.data, function (id, text) {
                                    terms.push({id: id, text: text});
                                });
                            } else {
                                console.error('Product search failed:', data);
                            }
                            return {results: terms};
                        },
                        cache: true
                    },
                    minimumInputLength: 3,
                    placeholder: 'Search for a product...',
                    allowClear: true
                });
            }

            // Initialize existing product search fields
            $('.wc-product-search').each(function() {
                initializeProductSearch($(this));
            });

            // Add product to bundle
            $('#add_bundle_product').click(function() {
                var html = '<div class="bundle-product-row">' +
                    '<select name="bundle_products[]" class="wc-product-search" style="width: 300px;"></select>' +
                    '<input type="number" name="bundle_quantities[]" placeholder="Qty" value="1" min="1" style="width: 60px;" />' +
                    '<input type="number" name="bundle_discounts[]" placeholder="Discount %" step="0.01" min="0" max="100" style="width: 80px;" />' +
                    '<button type="button" class="remove-bundle-product button">Remove</button>' +
                    '</div>';
                $('#bundle_products_list').append(html);

                // Initialize select2 for the new product search field
                initializeProductSearch($('.wc-product-search').last());
            });
            
            // Remove product from bundle
            $(document).on('click', '.remove-bundle-product', function() {
                $(this).closest('.bundle-product-row').remove();
            });
        });
        </script>
        <?php
    }
    
    /**
     * Display bundle products in admin
     */
    private function display_bundle_products_admin($bundle_id) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'wc_bundle_products';
        $bundle_products = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $table_name WHERE bundle_id = %d ORDER BY sort_order",
            $bundle_id
        ));
        
        foreach ($bundle_products as $bundle_product) {
            $product = wc_get_product($bundle_product->product_id);
            if ($product) {
                echo '<div class="bundle-product-row">';
                echo '<select name="bundle_products[]" class="wc-product-search" style="width: 300px;">';
                echo '<option value="' . $bundle_product->product_id . '" selected>' . $product->get_name() . '</option>';
                echo '</select>';
                echo '<input type="number" name="bundle_quantities[]" value="' . $bundle_product->quantity . '" min="1" style="width: 60px;" />';
                echo '<input type="number" name="bundle_discounts[]" value="' . $bundle_product->discount_value . '" step="0.01" min="0" max="100" style="width: 80px;" />';
                echo '<button type="button" class="remove-bundle-product button">Remove</button>';
                echo '</div>';
            }
        }
    }
    
    /**
     * Save bundle product data
     */
    public function save_bundle_product_data($post_id) {
        if (isset($_POST['product-type']) && $_POST['product-type'] === 'bundle') {
            // Save bundle settings
            update_post_meta($post_id, '_bundle_pricing_type', sanitize_text_field($_POST['bundle_pricing_type']));
            update_post_meta($post_id, '_bundle_discount', floatval($_POST['bundle_discount']));
            
            // Save bundle products
            global $wpdb;
            $table_name = $wpdb->prefix . 'wc_bundle_products';
            
            // Delete existing bundle products
            $wpdb->delete($table_name, array('bundle_id' => $post_id));
            
            // Add new bundle products
            if (isset($_POST['bundle_products']) && is_array($_POST['bundle_products'])) {
                $products = $_POST['bundle_products'];
                $quantities = $_POST['bundle_quantities'];
                $discounts = $_POST['bundle_discounts'];
                
                for ($i = 0; $i < count($products); $i++) {
                    if (!empty($products[$i])) {
                        $wpdb->insert(
                            $table_name,
                            array(
                                'bundle_id' => $post_id,
                                'product_id' => intval($products[$i]),
                                'quantity' => intval($quantities[$i]),
                                'discount_value' => floatval($discounts[$i]),
                                'sort_order' => $i
                            )
                        );
                    }
                }
            }
            
            // Calculate and save bundle price if dynamic pricing
            if ($_POST['bundle_pricing_type'] === 'dynamic') {
                $this->calculate_bundle_price($post_id);
            }
        }
    }
    
    /**
     * Calculate bundle price for dynamic pricing
     */
    private function calculate_bundle_price($bundle_id) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'wc_bundle_products';
        $bundle_products = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $table_name WHERE bundle_id = %d",
            $bundle_id
        ));
        
        $total_price = 0;
        $bundle_discount = floatval(get_post_meta($bundle_id, '_bundle_discount', true));
        
        foreach ($bundle_products as $bundle_product) {
            $product = wc_get_product($bundle_product->product_id);
            if ($product) {
                $product_price = $product->get_price();
                $discounted_price = $product_price - ($product_price * $bundle_product->discount_value / 100);
                $total_price += $discounted_price * $bundle_product->quantity;
            }
        }
        
        // Apply bundle discount
        if ($bundle_discount > 0) {
            $total_price = $total_price - ($total_price * $bundle_discount / 100);
        }
        
        // Update product price
        update_post_meta($bundle_id, '_price', $total_price);
        update_post_meta($bundle_id, '_regular_price', $total_price);
    }

    /**
     * Enqueue admin scripts
     */
    public function enqueue_admin_scripts($hook) {
        if ($hook === 'post-new.php' || $hook === 'post.php' || strpos($hook, 'shop_order') !== false) {
            wp_enqueue_script('select2');
            wp_enqueue_style('select2');
            wp_enqueue_style('wc-bundle-admin-css', WC_BUNDLE_PRODUCTS_PLUGIN_URL . 'assets/admin.css', array(), WC_BUNDLE_PRODUCTS_VERSION);
            wp_enqueue_script('wc-bundle-admin', WC_BUNDLE_PRODUCTS_PLUGIN_URL . 'assets/admin.js', array('jquery', 'select2'), WC_BUNDLE_PRODUCTS_VERSION, true);
            wp_localize_script('wc-bundle-admin', 'wc_bundle_ajax', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('wc_bundle_nonce')
            ));
        }
    }

    /**
     * Add bundle products section to admin order creation
     */
    public function add_bundle_products_to_order($order) {
        ?>
        <div class="wc-bundle-order-section">
            <h3><?php _e('Add Bundle Products', 'wc-bundle-products'); ?></h3>
            <div class="wc-bundle-search-container">
                <select id="bundle-product-search" class="wc-bundle-search" style="width: 300px;">
                    <option value=""><?php _e('Search for bundle products...', 'wc-bundle-products'); ?></option>
                </select>
                <button type="button" id="add-bundle-to-order" class="button"><?php _e('Add Bundle to Order', 'wc-bundle-products'); ?></button>
            </div>
            <div id="bundle-preview" style="margin-top: 15px; display: none;">
                <h4><?php _e('Bundle Contents:', 'wc-bundle-products'); ?></h4>
                <div id="bundle-contents"></div>
                <div id="bundle-total"></div>
            </div>
        </div>

        <script type="text/javascript">
        jQuery(document).ready(function($) {
            // Initialize bundle product search
            $('#bundle-product-search').select2({
                ajax: {
                    url: ajaxurl,
                    dataType: 'json',
                    delay: 250,
                    data: function (params) {
                        return {
                            q: params.term,
                            action: 'search_bundle_products',
                            nonce: wc_bundle_ajax.nonce
                        };
                    },
                    processResults: function (data) {
                        var terms = [];
                        if (data.success && data.data) {
                            $.each(data.data, function (id, text) {
                                terms.push({id: id, text: text});
                            });
                        }
                        return {results: terms};
                    },
                    cache: true
                },
                minimumInputLength: 3
            });

            // Preview bundle contents
            $('#bundle-product-search').on('select2:select', function (e) {
                var bundleId = e.params.data.id;
                if (bundleId) {
                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'get_bundle_contents',
                            bundle_id: bundleId,
                            nonce: wc_bundle_ajax.nonce
                        },
                        success: function(response) {
                            if (response.success) {
                                $('#bundle-contents').html(response.data.contents);
                                $('#bundle-total').html('<strong>Total: ' + response.data.total + '</strong>');
                                $('#bundle-preview').show();
                            }
                        }
                    });
                }
            });

            // Add bundle to order
            $('#add-bundle-to-order').click(function() {
                var bundleId = $('#bundle-product-search').val();
                if (!bundleId) {
                    alert('Please select a bundle product first.');
                    return;
                }

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'add_bundle_to_order',
                        bundle_id: bundleId,
                        order_id: $('#post_ID').val(),
                        nonce: wc_bundle_ajax.nonce
                    },
                    success: function(response) {
                        if (response.success) {
                            alert('Bundle added to order successfully!');
                            location.reload(); // Reload to show updated order items
                        } else {
                            alert('Error: ' + response.data);
                        }
                    }
                });
            });
        });
        </script>
        <?php
    }

    /**
     * AJAX handler for searching bundle products
     */
    public function ajax_search_bundle_products() {
        // Check permissions
        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error('Insufficient permissions');
        }

        // Check nonce
        if (!wp_verify_nonce($_REQUEST['nonce'], 'wc_bundle_nonce')) {
            wp_send_json_error('Invalid nonce');
        }

        $term = sanitize_text_field($_REQUEST['q']);
        $results = array();

        if (empty($term) || strlen($term) < 3) {
            wp_send_json_success($results);
        }

        $args = array(
            'post_type' => 'product',
            'post_status' => 'publish',
            's' => $term,
            'meta_query' => array(
                array(
                    'key' => '_product_type',
                    'value' => 'bundle',
                    'compare' => '='
                )
            ),
            'posts_per_page' => 20
        );

        $products = get_posts($args);

        foreach ($products as $product) {
            $results[$product->ID] = $product->post_title . ' (#' . $product->ID . ')';
        }

        wp_send_json_success($results);
    }

    /**
     * AJAX handler for searching regular products (for bundle contents)
     */
    public function ajax_search_products() {
        // Allow both logged in and non-logged in users for admin
        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error('Insufficient permissions');
        }

        // Check nonce
        if (!wp_verify_nonce($_REQUEST['nonce'], 'wc_bundle_nonce')) {
            wp_send_json_error('Invalid nonce');
        }

        $term = sanitize_text_field($_REQUEST['q']);
        $results = array();

        if (empty($term) || strlen($term) < 3) {
            wp_send_json_success($results);
        }

        $args = array(
            'post_type' => 'product',
            'post_status' => 'publish',
            's' => $term,
            'meta_query' => array(
                array(
                    'key' => '_product_type',
                    'value' => array('simple', 'variable'),
                    'compare' => 'IN'
                )
            ),
            'posts_per_page' => 20
        );

        $products = get_posts($args);

        foreach ($products as $product) {
            $product_obj = wc_get_product($product->ID);
            if ($product_obj) {
                $results[$product->ID] = $product->post_title . ' (#' . $product->ID . ')';
            }
        }

        wp_send_json_success($results);
    }

    /**
     * AJAX handler for getting bundle contents
     */
    public function ajax_get_bundle_contents() {
        check_ajax_referer('wc_bundle_nonce', 'nonce');

        $bundle_id = intval($_REQUEST['bundle_id']);
        $bundle_product = wc_get_product($bundle_id);

        if (!$bundle_product) {
            wp_send_json_error('Bundle product not found');
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'wc_bundle_products';
        $bundle_items = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $table_name WHERE bundle_id = %d ORDER BY sort_order",
            $bundle_id
        ));

        $contents_html = '<ul>';
        $total_price = 0;

        foreach ($bundle_items as $item) {
            $product = wc_get_product($item->product_id);
            if ($product) {
                $price = $product->get_price();
                $discounted_price = $price - ($price * $item->discount_value / 100);
                $item_total = $discounted_price * $item->quantity;
                $total_price += $item_total;

                $contents_html .= '<li>' . $product->get_name() . ' x ' . $item->quantity;
                if ($item->discount_value > 0) {
                    $contents_html .= ' (' . $item->discount_value . '% off)';
                }
                $contents_html .= ' - ' . wc_price($item_total) . '</li>';
            }
        }

        // Apply bundle discount
        $bundle_discount = floatval(get_post_meta($bundle_id, '_bundle_discount', true));
        if ($bundle_discount > 0) {
            $total_price = $total_price - ($total_price * $bundle_discount / 100);
            $contents_html .= '<li><strong>Bundle Discount: -' . $bundle_discount . '%</strong></li>';
        }

        $contents_html .= '</ul>';

        wp_send_json_success(array(
            'contents' => $contents_html,
            'total' => wc_price($total_price)
        ));
    }

    /**
     * AJAX handler for adding bundle to order
     */
    public function ajax_add_bundle_to_order() {
        check_ajax_referer('wc_bundle_nonce', 'nonce');

        $bundle_id = intval($_REQUEST['bundle_id']);
        $order_id = intval($_REQUEST['order_id']);

        $order = wc_get_order($order_id);
        $bundle_product = wc_get_product($bundle_id);

        if (!$order || !$bundle_product) {
            wp_send_json_error('Invalid order or bundle product');
        }

        // Add bundle product to order
        $item_id = $order->add_product($bundle_product, 1);

        if (!$item_id) {
            wp_send_json_error('Failed to add bundle to order');
        }

        // Add bundle items as order item meta
        global $wpdb;
        $table_name = $wpdb->prefix . 'wc_bundle_products';
        $bundle_items = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $table_name WHERE bundle_id = %d ORDER BY sort_order",
            $bundle_id
        ));

        $bundle_data = array();
        foreach ($bundle_items as $item) {
            $product = wc_get_product($item->product_id);
            if ($product) {
                $bundle_data[] = array(
                    'product_id' => $item->product_id,
                    'product_name' => $product->get_name(),
                    'quantity' => $item->quantity,
                    'discount' => $item->discount_value
                );
            }
        }

        // Save bundle data to order item
        wc_add_order_item_meta($item_id, '_bundle_data', $bundle_data);
        wc_add_order_item_meta($item_id, '_is_bundle', 'yes');

        // Recalculate order totals
        $order->calculate_totals();
        $order->save();

        wp_send_json_success('Bundle added to order successfully');
    }

    /**
     * AJAX handler for getting product price
     */
    public function ajax_get_product_price() {
        check_ajax_referer('wc_bundle_nonce', 'nonce');

        $product_id = intval($_REQUEST['product_id']);
        $product = wc_get_product($product_id);

        if (!$product) {
            wp_send_json_error('Product not found');
        }

        wp_send_json_success($product->get_price());
    }

    /**
     * Test AJAX handler
     */
    public function ajax_test() {
        wp_send_json_success('AJAX is working correctly!');
    }

    /**
     * Display bundle products on frontend
     */
    public function display_bundle_products() {
        global $product;

        if (!$product || $product->get_type() !== 'bundle') {
            return;
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'wc_bundle_products';
        $bundle_items = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $table_name WHERE bundle_id = %d ORDER BY sort_order",
            $product->get_id()
        ));

        if (empty($bundle_items)) {
            return;
        }

        echo '<div class="wc-bundle-products">';
        echo '<h3>' . __('This bundle includes:', 'wc-bundle-products') . '</h3>';
        echo '<div class="bundle-items">';

        $total_savings = 0;

        foreach ($bundle_items as $item) {
            $bundle_product = wc_get_product($item->product_id);
            if (!$bundle_product) {
                continue;
            }

            $original_price = $bundle_product->get_price() * $item->quantity;
            $discounted_price = $original_price - ($original_price * $item->discount_value / 100);
            $savings = $original_price - $discounted_price;
            $total_savings += $savings;

            echo '<div class="bundle-item">';
            echo '<div class="bundle-item-image">' . $bundle_product->get_image('thumbnail') . '</div>';
            echo '<div class="bundle-item-details">';
            echo '<h4>' . $bundle_product->get_name() . '</h4>';
            echo '<div class="bundle-item-quantity">Quantity: ' . $item->quantity . '</div>';

            if ($item->discount_value > 0) {
                echo '<div class="bundle-item-price">';
                echo '<span class="original-price">' . wc_price($original_price) . '</span>';
                echo '<span class="discounted-price">' . wc_price($discounted_price) . '</span>';
                echo '<span class="discount-badge">-' . $item->discount_value . '%</span>';
                echo '</div>';
            } else {
                echo '<div class="bundle-item-price">' . wc_price($discounted_price) . '</div>';
            }

            if ($item->optional) {
                echo '<div class="bundle-item-optional">Optional</div>';
            }

            echo '</div>';
            echo '</div>';
        }

        echo '</div>';

        // Show total savings
        $bundle_discount = floatval(get_post_meta($product->get_id(), '_bundle_discount', true));
        if ($bundle_discount > 0 || $total_savings > 0) {
            echo '<div class="bundle-savings">';
            echo '<strong>' . __('Total Savings: ', 'wc-bundle-products') . wc_price($total_savings) . '</strong>';
            if ($bundle_discount > 0) {
                echo '<div class="bundle-discount">Additional Bundle Discount: ' . $bundle_discount . '%</div>';
            }
            echo '</div>';
        }

        echo '</div>';

        // Add CSS for bundle display
        echo '<style>
        .wc-bundle-products {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .bundle-items {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        .bundle-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            background: white;
            border-radius: 5px;
            border: 1px solid #eee;
        }
        .bundle-item-image {
            flex-shrink: 0;
        }
        .bundle-item-image img {
            width: 80px;
            height: 80px;
            object-fit: cover;
        }
        .bundle-item-details {
            flex-grow: 1;
        }
        .bundle-item-details h4 {
            margin: 0 0 5px 0;
            font-size: 16px;
        }
        .bundle-item-quantity {
            color: #666;
            font-size: 14px;
            margin-bottom: 5px;
        }
        .bundle-item-price {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .original-price {
            text-decoration: line-through;
            color: #999;
        }
        .discounted-price {
            font-weight: bold;
            color: #e74c3c;
        }
        .discount-badge {
            background: #e74c3c;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
        }
        .bundle-item-optional {
            color: #3498db;
            font-style: italic;
            font-size: 12px;
        }
        .bundle-savings {
            margin-top: 15px;
            padding: 10px;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            text-align: center;
        }
        .bundle-discount {
            color: #155724;
            font-size: 14px;
        }
        </style>';
    }

    /**
     * Handle bundle add to cart
     */
    public function add_to_cart_handler($handler, $product) {
        if ($product->get_type() === 'bundle') {
            return 'bundle';
        }
        return $handler;
    }

    /**
     * Add bundle to cart
     */
    public function add_bundle_to_cart($cart_item_key, $product_id, $quantity, $variation_id, $variation, $cart_item_data) {
        $product = wc_get_product($product_id);

        if (!$product || $product->get_type() !== 'bundle') {
            return;
        }

        // Get bundle items
        global $wpdb;
        $table_name = $wpdb->prefix . 'wc_bundle_products';
        $bundle_items = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $table_name WHERE bundle_id = %d ORDER BY sort_order",
            $product_id
        ));

        // Add bundle data to cart item
        WC()->cart->cart_contents[$cart_item_key]['bundle_data'] = $bundle_items;
        WC()->cart->cart_contents[$cart_item_key]['is_bundle'] = true;
    }

    /**
     * Modify order item name for bundles
     */
    public function order_item_name($name, $item) {
        if ($item->get_meta('_is_bundle') === 'yes') {
            $bundle_data = $item->get_meta('_bundle_data');
            if ($bundle_data) {
                $name .= '<br><small>' . __('Bundle includes:', 'wc-bundle-products') . '</small>';
                foreach ($bundle_data as $bundle_item) {
                    $name .= '<br><small>• ' . $bundle_item['product_name'] . ' x ' . $bundle_item['quantity'];
                    if ($bundle_item['discount'] > 0) {
                        $name .= ' (' . $bundle_item['discount'] . '% off)';
                    }
                    $name .= '</small>';
                }
            }
        }
        return $name;
    }

    /**
     * Add bundle data to order item
     */
    public function add_bundle_data_to_order_item($item, $cart_item_key, $values, $order) {
        if (isset($values['is_bundle']) && $values['is_bundle']) {
            $item->add_meta_data('_is_bundle', 'yes');
            if (isset($values['bundle_data'])) {
                $bundle_data = array();
                foreach ($values['bundle_data'] as $bundle_item) {
                    $product = wc_get_product($bundle_item->product_id);
                    if ($product) {
                        $bundle_data[] = array(
                            'product_id' => $bundle_item->product_id,
                            'product_name' => $product->get_name(),
                            'quantity' => $bundle_item->quantity,
                            'discount' => $bundle_item->discount_value
                        );
                    }
                }
                $item->add_meta_data('_bundle_data', $bundle_data);
            }
        }
    }
}

// Initialize the plugin
new WC_Bundle_Products();
