{"id": "PAY-KT5OSI01X8QU", "status": "SETTLED", "delayCapture": true, "amount": {"total": 102470, "gross": 102470, "fees": 7622, "refunds": 0, "liquid": 94848, "currency": "BRL"}, "installmentCount": 1, "fundingInstrument": {"creditCard": {"id": "CRC-Y193W7M06F08", "brand": "VISA", "first6": "401200", "last4": "1112", "store": true, "holder": {"birthdate": "1988-12-30", "birthDate": "1988-12-30", "taxDocument": {"type": "CPF", "number": "22222222222"}, "fullname": "<PERSON>"}}, "method": "CREDIT_CARD"}, "fees": [{"type": "TRANSACTION", "amount": 7622}], "events": [{"type": "PAYMENT.SETTLED", "createdAt": "2017-09-06T00:22:25.000-03"}, {"type": "PAYMENT.AUTHORIZED", "createdAt": "2017-09-04T14:25:11.000-03"}, {"type": "PAYMENT.PRE_AUTHORIZED", "createdAt": "2017-09-04T14:17:35.000-03"}, {"type": "PAYMENT.CREATED", "createdAt": "2017-09-04T14:17:34.000-03"}, {"type": "PAYMENT.IN_ANALYSIS", "createdAt": "2017-09-04T14:17:34.000-03"}], "receivers": [{"moipAccount": {"id": "MPA-CULBBYHD11", "login": "<EMAIL>", "fullname": "<PERSON><PERSON>"}, "type": "PRIMARY", "amount": {"total": 102470, "fees": 7622, "refunds": 0}, "feePayor": true}], "_links": {"self": {"href": "https://sandbox.moip.com.br/v2/payments/PAY-KT5OSI01X8QU"}, "order": {"href": "https://sandbox.moip.com.br/v2/orders/ORD-5N85DL9MNDSS", "title": "ORD-5N85DL9MNDSS"}}, "createdAt": "2017-09-04T14:17:34.000-03", "updatedAt": "2017-09-06T00:22:25.000-03"}