=== Zagzoog WooCommerce Integration ===
Contributors: Hisense Team
Tags: woocommerce, api, integration, orders, zagzoog
Requires at least: 5.0
Tested up to: 6.4
Requires PHP: 7.4
Stable tag: 1.0.0
License: GPLv2 or later

Integrates WooCommerce orders with Zagzoog API for seamless order processing and status updates.

== Description ==

The Zagzoog WooCommerce Integration plugin automatically sends new WooCommerce orders to the Zagzoog API and handles order status updates via webhooks.

= Features =

* Automatically sends new orders to Zagzoog API
* Handles order status updates from Zagzoog via webhooks
* Comprehensive logging for debugging
* Admin interface for configuration
* Maps Zagzoog statuses to WooCommerce statuses
* Secure webhook endpoint

= API Integration =

The plugin sends order data in the following format to Zagzoog:

```json
{
  "orderid": "123",
  "firstname": "John",
  "lastname": "Doe",
  "mobile": "**********",
  "email": "<EMAIL>",
  "shippingcity": "Jeddah",
  "shippingaddress": "123 Main St, District",
  "products": [
    {
      "productid": "ZRF93H",
      "quantity": "1"
    }
  ]
}
```

= Webhook Support =

The plugin provides a webhook endpoint for Zagzoog to send order status updates:
`https://yoursite.com/zagzoog-webhook/`

Expected webhook format:
```json
{
  "orderid": "123",
  "status": "Shipped"
}
```

= Status Mapping =

* Pending → Processing
* Shipment Ready → Processing  
* Shipped → Shipped
* Delivered → Completed

== Installation ==

1. Upload the plugin files to `/wp-content/plugins/zagzoog-woocommerce-integration/`
2. Activate the plugin through the 'Plugins' screen in WordPress
3. Go to Settings → Zagzoog Integration to configure
4. Provide the webhook URL to Zagzoog: `https://yoursite.com/zagzoog-webhook/`

== Configuration ==

1. Navigate to **Settings → Zagzoog Integration**
2. Configure the API URL (default: https://zagzoog.com/hisenseapi/saveorder.php)
3. Enable logging if needed for debugging
4. Copy the webhook URL and provide it to Zagzoog

== Frequently Asked Questions ==

= How do I test the integration? =

1. Create a test order in WooCommerce
2. Check the order notes for API response
3. Review the logs in Settings → Zagzoog Integration

= What happens if the API is down? =

The plugin will log the error and add a note to the order. You can retry sending the order manually or the system will attempt again on order status changes.

= How do I debug API issues? =

1. Enable logging in the plugin settings
2. Check the Recent API Logs section
3. Review WordPress error logs
4. Check order notes for specific error messages

== Changelog ==

= 1.0.0 =
* Initial release
* Order sending to Zagzoog API
* Webhook support for status updates
* Admin interface and logging
* Status mapping functionality
