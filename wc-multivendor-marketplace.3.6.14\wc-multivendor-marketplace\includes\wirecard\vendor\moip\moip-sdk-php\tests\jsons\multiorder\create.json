{"id": "MOR-J1G1C9W4P1SD", "ownId": "meu_multiorder_id", "status": "CREATED", "createdAt": "2017-09-29T10:26:21.266-03", "updatedAt": "", "amount": {"total": 8000, "gross": 8000, "currency": "BRL"}, "orders": [{"id": "ORD-740WQDF7K50X", "ownId": "pedido_1_id", "status": "CREATED", "platform": "V2", "createdAt": "2017-09-29T10:26:21.266-03", "updatedAt": "2017-09-29T10:26:21.266-03", "amount": {"paid": 0, "total": 4000, "fees": 0, "refunds": 0, "liquid": 0, "otherReceivers": 3500, "currency": "BRL", "subtotals": {"shipping": 2000, "addition": 0, "discount": 0, "items": 2000}}, "items": [{"product": "Camisa Verde e Amarelo - Brasil", "price": 2000, "detail": "<PERSON><PERSON>ção <PERSON>eira", "quantity": 1}], "customer": {"id": "CUS-02ZE76RXIG24", "ownId": "customer[1234]", "fullname": "<PERSON><PERSON>", "createdAt": "2017-06-21T11:02:24.000-03", "birthDate": "1988-12-30", "email": "<EMAIL>", "fundingInstrument": {"creditCard": {"id": "CRC-UARTCOXFHAQI", "brand": "VISA", "first6": "401200", "last4": "1112", "store": true}, "method": "CREDIT_CARD"}, "phone": {"countryCode": "55", "areaCode": "11", "number": "********"}, "taxDocument": {"type": "CPF", "number": "***********"}, "shippingAddress": {"zipCode": "********", "street": "Avenida Faria Lima", "streetNumber": "2927", "complement": "8", "city": "Sao Paulo", "district": "Itaim", "state": "SP", "country": "BRA"}, "moipAccount": {"id": "MPA-PQ0H8UZYNNWY"}, "_links": {"self": {"href": "https://sandbox.moip.com.br/v2/customers/CUS-02ZE76RXIG24"}, "hostedAccount": {"redirectHref": "https://hostedaccount-sandbox.moip.com.br?token=22e9ac2f-3cbd-4fa1-b143-8c0277565b39&id=CUS-02ZE76RXIG24&mpa=MPA-8D5DBB4EF8B8"}}, "fundingInstruments": [{"creditCard": {"id": "CRC-UARTCOXFHAQI", "brand": "VISA", "first6": "401200", "last4": "1112", "store": true}, "method": "CREDIT_CARD"}]}, "payments": [], "escrows": [], "refunds": [], "entries": [], "events": [{"type": "ORDER.CREATED", "createdAt": "2017-09-29T10:26:21.266-03", "description": ""}], "receivers": [{"moipAccount": {"id": "MPA-E3C8493A06AE", "login": "<EMAIL>", "fullname": "<PERSON>"}, "type": "PRIMARY", "amount": {"total": 3500, "refunds": 0}}, {"moipAccount": {"id": "MPA-8D5DBB4EF8B8", "login": "<EMAIL>", "fullname": "Caio <PERSON>par"}, "type": "SECONDARY", "amount": {"total": 500, "refunds": 0}}], "shippingAddress": {"zipCode": "********", "street": "Avenida Faria Lima", "streetNumber": "2927", "complement": "8", "city": "Sao Paulo", "district": "Itaim", "state": "SP", "country": "BRA"}, "_links": {"self": {"href": "https://sandbox.moip.com.br/v2/orders/ORD-740WQDF7K50X"}}}, {"id": "ORD-JR07AW1L8BZS", "ownId": "pedido_2_id", "status": "CREATED", "platform": "V2", "createdAt": "2017-09-29T10:26:21.279-03", "updatedAt": "2017-09-29T10:26:21.279-03", "amount": {"paid": 0, "total": 4000, "fees": 0, "refunds": 0, "liquid": 0, "otherReceivers": 0, "currency": "BRL", "subtotals": {"shipping": 3000, "addition": 0, "discount": 0, "items": 1000}}, "items": [{"product": "<PERSON><PERSON>", "price": 1000, "detail": "Camiseta da Copa 2014", "quantity": 1}], "customer": {"id": "CUS-02ZE76RXIG24", "ownId": "customer[1234]", "fullname": "<PERSON><PERSON>", "createdAt": "2017-06-21T11:02:24.000-03", "birthDate": "1988-12-30", "email": "<EMAIL>", "fundingInstrument": {"creditCard": {"id": "CRC-UARTCOXFHAQI", "brand": "VISA", "first6": "401200", "last4": "1112", "store": true}, "method": "CREDIT_CARD"}, "phone": {"countryCode": "55", "areaCode": "11", "number": "********"}, "taxDocument": {"type": "CPF", "number": "***********"}, "shippingAddress": {"zipCode": "********", "street": "Avenida Faria Lima", "streetNumber": "2927", "complement": "8", "city": "Sao Paulo", "district": "Itaim", "state": "SP", "country": "BRA"}, "moipAccount": {"id": "MPA-PQ0H8UZYNNWY"}, "_links": {"self": {"href": "https://sandbox.moip.com.br/v2/customers/CUS-02ZE76RXIG24"}, "hostedAccount": {"redirectHref": "https://hostedaccount-sandbox.moip.com.br?token=22e9ac2f-3cbd-4fa1-b143-8c0277565b39&id=CUS-02ZE76RXIG24&mpa=MPA-8D5DBB4EF8B8"}}, "fundingInstruments": [{"creditCard": {"id": "CRC-UARTCOXFHAQI", "brand": "VISA", "first6": "401200", "last4": "1112", "store": true}, "method": "CREDIT_CARD"}]}, "payments": [], "escrows": [], "refunds": [], "entries": [], "events": [{"type": "ORDER.CREATED", "createdAt": "2017-09-29T10:26:21.279-03", "description": ""}], "receivers": [{"moipAccount": {"id": "MPA-8D5DBB4EF8B8", "login": "<EMAIL>", "fullname": "Caio <PERSON>par"}, "type": "PRIMARY", "amount": {"total": 4000, "fees": 0, "refunds": 0}, "feePayor": true}], "shippingAddress": {"zipCode": "********", "street": "Avenida Faria Lima", "streetNumber": "2927", "complement": "8", "city": "Sao Paulo", "district": "Itaim", "state": "SP", "country": "BRA"}, "_links": {"self": {"href": "https://sandbox.moip.com.br/v2/orders/ORD-JR07AW1L8BZS"}}}], "_links": {"self": {"href": "https://sandbox.moip.com.br/v2/orders/MOR-J1G1C9W4P1SD"}, "checkout": {"payCreditCard": {"redirectHref": "https://checkout-sandbox.moip.com.br/creditcard/MOR-J1G1C9W4P1SD"}, "payBoleto": {"redirectHref": "https://checkout-sandbox.moip.com.br/boleto/MOR-J1G1C9W4P1SD"}, "payOnlineBankDebitItau": {"redirectHref": "https://checkout-sandbox.moip.com.br/debit/itau/MOR-J1G1C9W4P1SD"}}}}