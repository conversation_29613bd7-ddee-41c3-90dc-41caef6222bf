{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "bc226eb61286930c41f0687624155641", "packages": [{"name": "moip/moip-sdk-php", "version": "v3.1.0", "source": {"type": "git", "url": "https://github.com/moip/moip-sdk-php.git", "reference": "c32c90218aa6f2fa85bbefa260c1e457fc80a448"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/moip/moip-sdk-php/zipball/c32c90218aa6f2fa85bbefa260c1e457fc80a448", "reference": "c32c90218aa6f2fa85bbefa260c1e457fc80a448", "shasum": ""}, "require": {"php": ">=5.5", "rmccue/requests": ">=1.0"}, "require-dev": {"codacy/coverage": "dev-master", "phpunit/phpunit": "~4.8.35 || ^5.7 || ^6.4"}, "type": "libraries", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Moip\\": "src/"}, "files": ["src/Helper/helpers.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "jeances<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Caio <PERSON>par", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Cliente em PHP para integração server-side com APIs Moip v2", "homepage": "http://dev.moip.com.br", "keywords": ["checkout", "intermediary", "libraries", "marketplace", "moip"], "time": "2018-01-29T13:31:39+00:00"}, {"name": "rmccue/requests", "version": "v1.7.0", "source": {"type": "git", "url": "https://github.com/rmccue/Requests.git", "reference": "87932f52ffad70504d93f04f15690cf16a089546"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/rmccue/Requests/zipball/87932f52ffad70504d93f04f15690cf16a089546", "reference": "87932f52ffad70504d93f04f15690cf16a089546", "shasum": ""}, "require": {"php": ">=5.2"}, "require-dev": {"requests/test-server": "dev-master"}, "type": "library", "autoload": {"psr-0": {"Requests": "library/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["ISC"], "authors": [{"name": "<PERSON>", "homepage": "http://ryanmccue.info"}], "description": "A HTTP library written in PHP, for human beings.", "homepage": "http://github.com/rmccue/Requests", "keywords": ["curl", "fsockopen", "http", "idna", "ipv6", "iri", "sockets"], "time": "2016-10-13T00:11:37+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": [], "platform-dev": []}