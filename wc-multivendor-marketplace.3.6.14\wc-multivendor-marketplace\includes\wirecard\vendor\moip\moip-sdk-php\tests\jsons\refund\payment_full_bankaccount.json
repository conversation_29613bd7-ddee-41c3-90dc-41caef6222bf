{"id": "REF-4D5LXJ5EBUOB", "status": "REQUESTED", "events": [{"type": "REFUND.REQUESTED", "createdAt": "2017-08-29T09:31:31.000-03"}], "amount": {"total": 102470, "gross": 102470, "fees": 0, "currency": "BRL"}, "type": "FULL", "refundingInstrument": {"bankAccount": {"bankNumber": "001", "bankName": "BANCO DO BRASIL S.A.", "agencyNumber": "4444444", "agencyCheckNumber": "2", "accountNumber": "1234", "accountCheckNumber": "1", "type": "CHECKING", "holder": {"taxDocument": {"type": "CPF", "number": "***********"}, "fullname": "<PERSON>"}}, "method": "BANK_ACCOUNT"}, "createdAt": "2017-08-29T09:31:31.000-03", "_links": {"self": {"href": "https://sandbox.moip.com.br/v2/refunds/REF-4D5LXJ5EBUOB"}, "order": {"href": "https://sandbox.moip.com.br/v2/orders/ORD-9PWJ1P1TZN4B", "title": "ORD-9PWJ1P1TZN4B"}, "payment": {"href": "https://sandbox.moip.com.br/v2/payments/PAY-K6GFREQDHVV8", "title": "PAY-K6GFREQDHVV8"}}}