<?php
/**
 * Registration form with server-side processing.
 *
 * <AUTHOR> + Assistant
 * @package     WooCommerce-Simple-Registration
 * @version     1.0.1
 */

if (!defined('ABSPATH'))
  exit;

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['register'])) {

  // Verify nonce
  if (!isset($_POST['woocommerce-register-nonce']) || !wp_verify_nonce($_POST['woocommerce-register-nonce'], 'woocommerce-register')) {
    wc_add_notice('Nonce verification failed.', 'error');
  } else {

    $first_name = sanitize_text_field($_POST['billing_first_name']);
    $last_name = sanitize_text_field($_POST['billing_last_name']);
    $email = sanitize_email($_POST['email']);
    $phone = sanitize_text_field($_POST['billing_phone']);
    $city = sanitize_text_field($_POST['billing_city']);
    $password = $_POST['password'];
    $password2 = $_POST['password2'];
    $terms = isset($_POST['terms']) ? true : false;

    // Server-side validation
    if (empty($first_name)) {
      wc_add_notice('Please enter your first name.', 'error');
    }

    if (empty($last_name)) {
      wc_add_notice('Please enter your last name.', 'error');
    }

    if (empty($email) || !is_email($email)) {
      wc_add_notice('Please enter a valid email address.', 'error');
    }

    if (empty($phone)) {
      wc_add_notice('Please enter your phone number.', 'error');
    }

    if (empty($city)) {
      wc_add_notice('Please select your city.', 'error');
    }

    if (empty($password)) {
      wc_add_notice('Please enter a password.', 'error');
    }

    if (empty($password2)) {
      wc_add_notice('Please confirm your password.', 'error');
    }

    if ($password !== $password2) {
      wc_add_notice('Passwords do not match.', 'error');
    }

    if (!$terms) {
      wc_add_notice('You must accept the terms and privacy policy.', 'error');
    }

    if (email_exists($email)) {
      wc_add_notice('An account with this email already exists.', 'error');
    }

    // If no errors, create the user
    if (!wc_notice_count('error')) {

      $userdata = [
        'user_login' => $email,
        'user_email' => $email,
        'user_pass' => $password,
        'first_name' => $first_name,
        'last_name' => $last_name,
      ];

      $user_id = wp_insert_user($userdata);

      if (is_wp_error($user_id)) {
        wc_add_notice($user_id->get_error_message(), 'error');
      } else {
        // Save extra user meta
        update_user_meta($user_id, 'billing_phone', $phone);
        update_user_meta($user_id, 'billing_city', $city);

        // Log user in
        wp_set_auth_cookie($user_id);
        wp_set_current_user($user_id);

        // Redirect after registration
        wp_safe_redirect(site_url('/my-account/'));
        exit;
      }
    }
  }
}
?>

<style>
  .login-form-page {
    display: none;
  }

  .valid-error {
    border-color: #e74c3c !important;
  }

  .wpcf7-not-valid-tip {
    color: #e74c3c;
    font-size: 0.875rem;
    margin-top: 4px;
    display: block;
  }

  .toggle-password {
    cursor: pointer;
    position: absolute;
    right: 10px;
    top: 38px;
    color: #888;
  }

  .relative {
    position: relative;
  }
</style>

<div class="register-form-page">

  <?php wc_print_notices(); // Show WooCommerce error/success messages ?>

  <form method="post" action="">

    <?php do_action('woocommerce_register_form_start'); ?>

    <div class="register-form-right">
      <h4>Contact Information</h4>
      <ul class="register-fields">
        <li>
          <label for="first_name"><?php esc_html_e('First Name', 'woocommerce'); ?><span>*</span></label>
          <input type="text" name="billing_first_name" id="first_name"
            value="<?php echo !empty($_POST['billing_first_name']) ? esc_attr($_POST['billing_first_name']) : ''; ?>"
            class="woocommerce-Input woocommerce-Input--text input-text" placeholder="" />
        </li>
        <li>
          <label for="last_name"><?php esc_html_e('Last Name', 'woocommerce'); ?><span>*</span></label>
          <input type="text" name="billing_last_name" id="last_name"
            value="<?php echo !empty($_POST['billing_last_name']) ? esc_attr($_POST['billing_last_name']) : ''; ?>"
            class="woocommerce-Input woocommerce-Input--text input-text" placeholder="" />
        </li>

        <li>
          <label for="reg_email"><?php esc_html_e('Email', 'woocommerce'); ?><span>*</span></label>
          <input type="email" class="form-field" name="email" id="reg_email"
            value="<?php echo !empty($_POST['email']) ? esc_attr($_POST['email']) : ''; ?>" />
        </li>

        <li>
          <label for="billing_phone"><?php esc_html_e('Phone', 'woocommerce'); ?><span>*</span></label>
          <input type="tel" class="form-field" name="billing_phone" id="billing_phone"
            value="<?php echo !empty($_POST['billing_phone']) ? esc_attr($_POST['billing_phone']) : ''; ?>" />
        </li>

        <li>
          <label for="billing_city">City<span>*</span></label>
          <select name="billing_city" id="billing_city" class="form-field custom_select_icon" required>
            <option value="">Select City</option>
            <option value="riyadh">Riyadh</option>
            <option value="jeddah">Jeddah</option>
            <option value="mecca">Mecca</option>
            <option value="medina">Medina</option>
            <option value="dammam">Dammam</option>
            <option value="taif">Ta'if</option>
            <option value="tabuk">Tabuk</option>
            <option value="al-kharj">Al-Kharj</option>
            <option value="buraydah">Buraydah</option>
            <option value="khamis-mushait">Khamis Mushait</option>
            <option value="al-hofuf">Al-Hofuf</option>
            <option value="al-mubarraz">Al-Mubarraz</option>
            <option value="hafr-al-batin">Hafr Al-Batin</option>
            <option value="hail">Ha'il</option>
            <option value="najran">Najran</option>
            <option value="abha">Abha</option>
            <option value="yanbu">Yanbu</option>
            <option value="khobar">Khobar</option>
            <option value="arar">Arar</option>
            <option value="sakaka">Sakaka</option>
            <option value="al-qatif">Al-Qatif</option>
            <option value="al-bahah">Al-Bahah</option>
            <option value="al-jubail">Al-Jubail</option>
            <option value="jizan">Jizan</option>
            <option value="al-ula">Al-`Ula</option>
            <option value="duba">Duba</option>
            <option value="al-wajh">Al-Wajh</option>
            <option value="qurayyat">Al Qurayyat</option>
            <option value="dhahran">Dhahran</option>
            <option value="ras-tanura">Ras Tanura</option>
            <option value="safwa">Safwa</option>
            <option value="as-sulayyil">As Sulayyil</option>
            <option value="al-duwadimi">Al Duwadimi</option>
            <option value="az-zulfi">Az Zulfi</option>
            <option value="ar-rass">Ar Rass</option>
            <option value="rabigh">Rabigh</option>
            <option value="badr">Badr</option>
            <option value="unaizah">Unaizah</option>
            <option value="al-namas">Al Namas</option>
            <option value="al-lith">Al Lith</option>
            <option value="bisah">Bisha</option>
            <option value="haql">Haql</option>
            <option value="tabarjal">Tubarjal</option>
            <option value="al-qaisumah">Al Qaisumah</option>
            <option value="turaif">Turaif</option>
            <option value="um-as-sahik">Umm As Sahik</option>
            <option value="farasan">Farasan</option>
            <option value="al-kharj">Al-Kharj</option>
            <option value="bariq">Bariq</option>
            <option value="al-mubarraz">Al Mubarraz</option>
            <option value="ras-tanura">Ras Tanura</option>
            <option value="haql">Haql</option>
            <option value="jalajil">Jalajil</option>

          </select>
          </select>
        </li>

        <li class="relative relativeField password-container">
          <input placeholder="Password" class="form-field" type="password" name="password" id="password"
            autocomplete="current-password" />
          <span class="toggle-password" style="cursor:pointer;">
            <img src="<?php echo esc_url(get_stylesheet_directory_uri() . '/assets/images/eye.svg'); ?>"
              alt="Toggle password visibility" />
          </span>
        </li>
        <li class="relative relativeField password-container">
          <!-- <input placeholder="Password" class="form-field" type="password" name="password" id="password" -->
          <input placeholder="Password" class="form-field" type="password" name="password" id="password2"
            autocomplete="current-password" />
          <span class="toggle-password" style="cursor:pointer;">
            <img src="<?php echo esc_url(get_stylesheet_directory_uri() . '/assets/images/eye.svg'); ?>"
              alt="Toggle password visibility" />
          </span>
        </li>
      </ul>

      <ul>
        <li class="checking">
          <input type="checkbox" name="terms" id="terms" value="on" <?php checked(!empty($_POST['terms'])); ?> />
          <p>
            I agree to the
            <a href="<?php echo esc_url(site_url('/terms-of-use')); ?>" target="_blank">
              <?php esc_html_e('Terms and Condition', 'woocommerce'); ?>
            </a>
            &amp;
            <a href="<?php echo esc_url(site_url('/privacy-policy')); ?>" target="_blank">
              <?php esc_html_e('Privacy Policy', 'woocommerce'); ?>
            </a>
            <span>*</span>
          </p>
        </li>
        <li>
          <div class="submit_submit">

            <!-- Spam Trap -->
            <div style="<?php echo ((is_rtl()) ? 'right' : 'left'); ?>: -999em; position: absolute;">
              <label for="trap"><?php esc_html_e('Anti-spam', 'woocommerce-simple-registration'); ?></label>
              <input type="text" name="email_2" id="trap" tabindex="-1" autocomplete="off" />
            </div>

            <div class="action-btns">
              <?php do_action('woocommerce_register_form'); ?>
              <?php do_action('woocommerce_simple_registration_form'); ?>
            </div>

            <?php wp_nonce_field('woocommerce-register', 'woocommerce-register-nonce'); ?>

            <input type="submit" class="submit" name="register" id="register-button"
              value="<?php esc_attr_e('Create Account', 'woocommerce-simple-registration'); ?>" />

            <p>Already have an account? <a href="<?php echo esc_url(site_url('my-account/')); ?>">Login</a></p>
          </div>
        </li>
      </ul>

      <?php do_action('woocommerce_register_form_end'); ?>
    </div>
  </form>
</div>

<script>
  jQuery(document).ready(function ($) {

    // Validation helpers
    function isValidPhone(phone) {
      var phonePattern = /^[0-9\s\-\(\)]+$/;
      return phonePattern.test(phone);
    }

    // Remove error messages & styles on input focus
    $("input, select").on('focus', function () {
      $(this).removeClass('valid-error');
      $(this).closest('li').find('.wpcf7-not-valid-tip').remove();
    });

    // Toggle password visibility
    // $('.toggle-password').on('click', function() {
    //   var input = $($(this).data('target'));
    //   if (input.attr('type') === 'password') {
    //     input.attr('type', 'text');
    //     $(this).removeClass('fa-eye-slash').addClass('fa-eye');
    //   } else {
    //     input.attr('type', 'password');
    //     $(this).removeClass('fa-eye').addClass('fa-eye-slash');
    //   }
    // });

    // Form validation on submit
    $("#register-button").on('click', function (e) {
      $('.wpcf7-not-valid-tip').remove();

      if ($("#first_name").val().trim() === '') {
        $("#first_name").addClass('valid-error');
        $("#first_name").closest('li').append('<span class="wpcf7-not-valid-tip" aria-hidden="true">Please fill out this field.</span>');
        e.preventDefault();
        return false;
      }

      if ($("#last_name").val().trim() === '') {
        $("#last_name").addClass('valid-error');
        $("#last_name").closest('li').append('<span class="wpcf7-not-valid-tip" aria-hidden="true">Please fill out this field.</span>');
        e.preventDefault();
        return false;
      }

      var phoneVal = $("#billing_phone").val().trim();
      if (phoneVal === '') {
        $("#billing_phone").addClass('valid-error');
        $("#billing_phone").closest('li').append('<span class="wpcf7-not-valid-tip" aria-hidden="true">Please fill out this field.</span>');
        e.preventDefault();
        return false;
      } else if (!isValidPhone(phoneVal) || phoneVal.replace(/\D/g, '').length > 15) {
        $("#billing_phone").addClass('valid-error');
        $("#billing_phone").closest('li').append('<span class="wpcf7-not-valid-tip" aria-hidden="true">Please enter a valid phone number.</span>');
        e.preventDefault();
        return false;
      }

      if ($("#billing_city").val() === '') {
        $("#billing_city").addClass('valid-error');
        $("#billing_city").closest('li').append('<span class="wpcf7-not-valid-tip" aria-hidden="true">Please select your city.</span>');
        e.preventDefault();
        return false;
      }

      if ($("#reg_password").val().trim() === '') {
        $("#reg_password").addClass('valid-error');
        $("#reg_password").closest('li').append('<span class="wpcf7-not-valid-tip" aria-hidden="true">Please fill out this field.</span>');
        e.preventDefault();
        return false;
      }

      if ($("#reg_password2").val().trim() === '') {
        $("#reg_password2").addClass('valid-error');
        $("#reg_password2").closest('li').append('<span class="wpcf7-not-valid-tip" aria-hidden="true">Please fill out this field.</span>');
        e.preventDefault();
        return false;
      }

      if ($("#reg_password").val() !== $("#reg_password2").val()) {
        $("#reg_password2").addClass('valid-error');
        $("#reg_password2").closest('li').append('<span class="wpcf7-not-valid-tip" aria-hidden="true">Passwords do not match.</span>');
        e.preventDefault();
        return false;
      }

      if (!$("#terms").is(':checked')) {
        $("#terms").addClass('valid-error');
        $("#terms").closest('li').append('<span class="wpcf7-not-valid-tip" aria-hidden="true">Please accept the terms and privacy policy.</span>');
        e.preventDefault();
        return false;
      }
    });

  });
</script>
<script>
  // document.addEventListener("DOMContentLoaded", function () {
  const togglePassword = document.querySelector(".toggle-password");
  const passwordInput = document.querySelector("#password");

  if (togglePassword && passwordInput) {
    togglePassword.addEventListener("click", function () {
      const type = passwordInput.getAttribute("type") === "password" ? "text" : "password";
      passwordInput.setAttribute("type", type);
      // Optionally: toggle icon or class for eye open/closed state
      this.classList.toggle("visible");
    });
  }
  });

  document.addEventListener("DOMContentLoaded", function () {
    document.querySelectorAll(".toggle-password").forEach(function (toggle) {
      toggle.addEventListener("click", function () {
        const targetSelector = this.getAttribute("data-target");
        const passwordInput = document.querySelector(targetSelector);
        if (passwordInput) {
          const type = passwordInput.getAttribute("type") === "password" ? "text" : "password";
          passwordInput.setAttribute("type", type);
          this.classList.toggle("visible");
        }
      });
    });
  });

</script>