<?php
/**
 * Plugin Name: Zagzoog WooCommerce Integration
 * Plugin URI: https://hisense.com
 * Description: Integrates WooCommerce orders with Zagzoog API for order processing and status updates
 * Version: 1.0.0
 * Author: Hisense Team
 * License: GPL v2 or later
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class ZagzoogWooCommerceIntegration {
    
    private $api_url = 'https://zagzoog.com/hisenseapi/saveorder.php';
    private $webhook_endpoint = 'zagzoog-webhook';
    
    public function __construct() {
        add_action('init', array($this, 'init'));
        add_action('woocommerce_new_order', array($this, 'send_order_to_zagzoog'), 10, 1);
        add_action('woocommerce_order_status_changed', array($this, 'handle_order_status_change'), 10, 4);
        add_action('init', array($this, 'handle_webhook'));
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'register_settings'));
        add_action('woocommerce_order_actions', array($this, 'add_order_action'));
        add_action('woocommerce_order_action_send_to_zagzoog', array($this, 'manual_send_to_zagzoog'));
        add_action('add_meta_boxes', array($this, 'add_order_meta_box'));
        add_action('wp_ajax_zagzoog_resend_order', array($this, 'ajax_resend_order'));
        add_action('wp_ajax_nopriv_zagzoog_webhook', array($this, 'ajax_webhook_handler'));
        add_action('wp_ajax_zagzoog_webhook', array($this, 'ajax_webhook_handler'));
    }
    
    public function init() {
        // Add rewrite rule for webhook
        add_rewrite_rule(
            '^zagzoog-webhook/?$',
            'index.php?zagzoog_webhook=1',
            'top'
        );
        add_filter('query_vars', array($this, 'add_query_vars'));
    }
    
    public function add_query_vars($vars) {
        $vars[] = 'zagzoog_webhook';
        return $vars;
    }
    
    /**
     * Send new order to Zagzoog API
     */
    public function send_order_to_zagzoog($order_id) {
        // Check if WooCommerce is available
        if (!function_exists('wc_get_order')) {
            error_log("ERROR: WooCommerce not available when processing order {$order_id}");
            return;
        }

        $order = wc_get_order($order_id);
        if (!$order) {
            error_log("ERROR: Could not retrieve order {$order_id}");
            return;
        }

        error_log("Processing order {$order_id} for Zagzoog API");
        
        // Prepare order data for Zagzoog API
        $order_data = $this->prepare_order_data($order);
        
        // Send to Zagzoog API
        $response = $this->send_api_request($order_data);
        
        // Log the response
        $this->log_api_response($order_id, $order_data, $response);
        
        // Store Zagzoog response in order meta
        if ($response && !is_wp_error($response)) {
            $order->update_meta_data('_zagzoog_api_sent', current_time('mysql'));
            $order->update_meta_data('_zagzoog_api_response', $response);

            // Parse response to get Zagzoog order ID
            $response_data = json_decode($response, true);
            if ($response_data && isset($response_data['desc'])) {
                $order->update_meta_data('_zagzoog_order_id', $response_data['desc']);
            }

            $order->save();

            // Create detailed order note
            $products_sent = count($order_data['products']);
            $note = "Order sent to Zagzoog API successfully. Products sent: {$products_sent}";
            if ($response_data && isset($response_data['desc'])) {
                $note .= ". Zagzoog Order ID: {$response_data['desc']}";
            }
            $order->add_order_note($note);
        } else {
            $error_message = is_wp_error($response) ? $response->get_error_message() : 'Unknown error';
            $order->add_order_note('Failed to send order to Zagzoog API: ' . $error_message);
        }
    }
    
    /**
     * Prepare order data in Zagzoog API format
     */
    private function prepare_order_data($order) {
        $products = array();

        error_log("=== Zagzoog Product Debug - Order {$order->get_id()} ===");
        error_log("Total order items: " . count($order->get_items()));

        foreach ($order->get_items() as $item_id => $item) {
            error_log("Processing item ID: {$item_id}");

            $product = $item->get_product();
            if ($product) {
                $product_sku = $product->get_sku();
                $product_id = $product->get_id();
                $product_name = $product->get_name();
                $quantity = $item->get_quantity();

                error_log("Product details - ID: {$product_id}, SKU: '{$product_sku}', Name: '{$product_name}', Quantity: {$quantity}");

                // Determine product identifier
                $product_identifier = '';
                if (!empty($product_sku)) {
                    $product_identifier = $product_sku;
                    error_log("Using SKU: {$product_sku}");
                } elseif (!empty($product_id)) {
                    $product_identifier = (string)$product_id;
                    error_log("Using Product ID: {$product_id}");
                } else {
                    $product_identifier = $product_name;
                    error_log("Using Product Name: {$product_name}");
                }

                if (!empty($product_identifier)) {
                    $product_data = array(
                        'productid' => (string)$product_identifier,
                        'quantity' => (string)$quantity
                    );

                    $products[] = $product_data;
                    error_log("Added product to array: " . json_encode($product_data));
                } else {
                    error_log("ERROR: Could not determine product identifier for item {$item_id}");
                }
            } else {
                error_log("ERROR: Could not get product object for item {$item_id}");
            }
        }

        error_log("Final products array: " . json_encode($products));
        error_log("Total products to send: " . count($products));
        
        $order_data = array(
            'orderid' => (string)$order->get_id(),
            'firstname' => $order->get_billing_first_name(),
            'lastname' => $order->get_billing_last_name(),
            'mobile' => $order->get_billing_phone(),
            'email' => $order->get_billing_email(),
            'shippingcity' => $order->get_shipping_city() ?: $order->get_billing_city(),
            'shippingaddress' => $this->get_full_shipping_address($order),
            'products' => $products
        );
        
        return $order_data;
    }
    
    /**
     * Get full shipping address
     */
    private function get_full_shipping_address($order) {
        $address_parts = array();
        
        if ($order->get_shipping_address_1()) {
            $address_parts[] = $order->get_shipping_address_1();
        } elseif ($order->get_billing_address_1()) {
            $address_parts[] = $order->get_billing_address_1();
        }
        
        if ($order->get_shipping_address_2()) {
            $address_parts[] = $order->get_shipping_address_2();
        } elseif ($order->get_billing_address_2()) {
            $address_parts[] = $order->get_billing_address_2();
        }
        
        if ($order->get_shipping_state()) {
            $address_parts[] = $order->get_shipping_state();
        } elseif ($order->get_billing_state()) {
            $address_parts[] = $order->get_billing_state();
        }
        
        return implode(', ', array_filter($address_parts));
    }
    
    /**
     * Send API request to Zagzoog
     */
    private function send_api_request($data) {
        $response = wp_remote_post($this->api_url, array(
            'method' => 'POST',
            'timeout' => 30,
            'headers' => array(
                'Content-Type' => 'application/json',
            ),
            'body' => json_encode($data)
        ));
        
        if (is_wp_error($response)) {
            return $response;
        }
        
        return wp_remote_retrieve_body($response);
    }
    
    /**
     * Log API response for debugging
     */
    private function log_api_response($order_id, $request_data, $response) {
        $log_entry = array(
            'timestamp' => current_time('mysql'),
            'order_id' => $order_id,
            'request_data' => $request_data,
            'response' => $response,
            'response_code' => is_wp_error($response) ? 'ERROR' : wp_remote_retrieve_response_code($response)
        );
        
        error_log('Zagzoog API Log: ' . json_encode($log_entry));
        
        // Store in database for admin review
        $logs = get_option('zagzoog_api_logs', array());
        $logs[] = $log_entry;
        
        // Keep only last 100 logs
        if (count($logs) > 100) {
            $logs = array_slice($logs, -100);
        }
        
        update_option('zagzoog_api_logs', $logs);
    }
    
    /**
     * Handle webhook from Zagzoog for order status updates
     */
    public function handle_webhook() {
        // Check if this is our webhook endpoint
        if (get_query_var('zagzoog_webhook') == '1') {
            // Set proper headers
            header('Content-Type: application/json');

            // Get input data
            $input = file_get_contents('php://input');
            $data = json_decode($input, true);

            // Log webhook received
            error_log('Zagzoog Webhook Received: ' . $input);

            if ($data && isset($data['orderid']) && isset($data['status'])) {
                $result = $this->process_webhook_data($data);
                if ($result) {
                    wp_send_json_success('Webhook processed successfully');
                } else {
                    wp_send_json_error('Failed to process webhook');
                }
            } else {
                wp_send_json_error('Invalid webhook data');
            }

            exit;
        }

        // Alternative check using REQUEST_URI
        if (isset($_SERVER['REQUEST_URI']) && strpos($_SERVER['REQUEST_URI'], '/zagzoog-webhook') !== false) {
            header('Content-Type: application/json');

            $input = file_get_contents('php://input');
            $data = json_decode($input, true);

            error_log('Zagzoog Webhook (URI check): ' . $input);

            if ($data && isset($data['orderid']) && isset($data['status'])) {
                $result = $this->process_webhook_data($data);
                echo json_encode(array('success' => true, 'message' => 'Webhook received'));
            } else {
                echo json_encode(array('success' => false, 'message' => 'Invalid data'));
            }

            exit;
        }
    }
    
    /**
     * Process webhook data from Zagzoog
     */
    private function process_webhook_data($data) {
        $order_id = sanitize_text_field($data['orderid']);
        $zagzoog_status = sanitize_text_field($data['status']);

        error_log("Processing webhook for Order ID: {$order_id}, Status: {$zagzoog_status}");

        $order = wc_get_order($order_id);
        if (!$order) {
            error_log("Order {$order_id} not found");
            return false;
        }

        // Map Zagzoog status to WooCommerce status
        $wc_status = $this->map_zagzoog_status_to_wc($zagzoog_status);

        if ($wc_status) {
            $order->update_status($wc_status, 'Status updated by Zagzoog: ' . $zagzoog_status);
            error_log("Order {$order_id} status updated to: {$wc_status}");
        }

        // Store Zagzoog status in order meta
        $order->update_meta_data('_zagzoog_status', $zagzoog_status);
        $order->update_meta_data('_zagzoog_last_update', current_time('mysql'));
        $order->save();

        $order->add_order_note('Zagzoog status update: ' . $zagzoog_status);

        return true;
    }
    
    /**
     * Map Zagzoog status to WooCommerce status
     */
    private function map_zagzoog_status_to_wc($zagzoog_status) {
        $status_map = array(
            'Pending' => 'pending',           // Maps to "Pending payment"
            'Shipment Ready' => 'processing', // Maps to "Processing"
            'Shipped' => 'processing',        // Maps to "Processing" (since you don't have shipped status)
            'Delivered' => 'completed',       // Maps to "Completed"
            'Refunded' => 'refunded'          // Maps to "Refunded"
        );

        return isset($status_map[$zagzoog_status]) ? $status_map[$zagzoog_status] : null;
    }
    
    /**
     * Handle WooCommerce order status changes
     */
    public function handle_order_status_change($order_id, $old_status, $new_status, $order) {
        // Log status changes for debugging
        error_log("Order {$order_id} status changed from {$old_status} to {$new_status}");
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_options_page(
            'Zagzoog Integration',
            'Zagzoog Integration',
            'manage_options',
            'zagzoog-integration',
            array($this, 'admin_page')
        );
    }
    
    /**
     * Register plugin settings
     */
    public function register_settings() {
        register_setting('zagzoog_settings', 'zagzoog_api_url');
        register_setting('zagzoog_settings', 'zagzoog_enable_logging');
    }
    
    /**
     * Admin page content
     */
    public function admin_page() {
        ?>
        <div class="wrap">
            <h1>Zagzoog Integration Settings</h1>
            
            <form method="post" action="options.php">
                <?php settings_fields('zagzoog_settings'); ?>
                <?php do_settings_sections('zagzoog_settings'); ?>
                
                <table class="form-table">
                    <tr>
                        <th scope="row">API URL</th>
                        <td>
                            <input type="url" name="zagzoog_api_url" value="<?php echo esc_attr(get_option('zagzoog_api_url', $this->api_url)); ?>" class="regular-text" />
                            <p class="description">Zagzoog API endpoint URL</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Enable Logging</th>
                        <td>
                            <input type="checkbox" name="zagzoog_enable_logging" value="1" <?php checked(get_option('zagzoog_enable_logging'), 1); ?> />
                            <p class="description">Enable detailed API logging</p>
                        </td>
                    </tr>
                </table>
                
                <?php submit_button(); ?>
            </form>
            
            <h2>Webhook URLs</h2>
            <p>Use one of these URLs in Zagzoog system for order status updates:</p>

            <h3>Primary Webhook URL:</h3>
            <code><?php echo home_url('/zagzoog-webhook/'); ?></code>

            <h3>Alternative Webhook URL (if primary doesn't work):</h3>
            <code><?php echo admin_url('admin-ajax.php?action=zagzoog_webhook'); ?></code>

            <h3>Test Webhook</h3>
            <p>Use this Postman configuration to test the webhook:</p>
            <div style="background: #f1f1f1; padding: 15px; margin: 10px 0;">
                <strong>Method:</strong> POST<br>
                <strong>URL:</strong> <code><?php echo home_url('/zagzoog-webhook/'); ?></code><br>
                <strong>Headers:</strong> Content-Type: application/json<br>
                <strong>Body (JSON):</strong>
                <pre>{
  "orderid": "17725",
  "status": "Shipped"
}</pre>
            </div>

            <p><strong>Available Status Values:</strong></p>
            <ul>
                <li><code>Pending</code> → Changes order to <strong>Pending payment</strong></li>
                <li><code>Shipment Ready</code> → Changes order to <strong>Processing</strong></li>
                <li><code>Shipped</code> → Changes order to <strong>Processing</strong></li>
                <li><code>Delivered</code> → Changes order to <strong>Completed</strong></li>
                <li><code>Refunded</code> → Changes order to <strong>Refunded</strong></li>
            </ul>
            
            <h2>Debug Tools</h2>
            <form method="post" action="">
                <table class="form-table">
                    <tr>
                        <th scope="row">Test Order Products</th>
                        <td>
                            <input type="number" name="test_order_id" placeholder="Enter Order ID" />
                            <input type="submit" name="test_products" value="Test Products" class="button" />
                            <p class="description">Test product extraction for a specific order</p>
                        </td>
                    </tr>
                </table>
            </form>

            <?php
            if (isset($_POST['test_products']) && !empty($_POST['test_order_id'])) {
                $test_order_id = absint($_POST['test_order_id']);
                $test_order = wc_get_order($test_order_id);

                if ($test_order) {
                    echo '<h3>Test Results for Order #' . $test_order_id . '</h3>';
                    echo '<div style="background: #f1f1f1; padding: 15px; margin: 10px 0;">';

                    $test_data = $this->prepare_order_data($test_order);
                    echo '<strong>Prepared Order Data:</strong><br>';
                    echo '<pre>' . json_encode($test_data, JSON_PRETTY_PRINT) . '</pre>';

                    echo '<strong>Order Items Debug:</strong><br>';
                    foreach ($test_order->get_items() as $item_id => $item) {
                        $product = $item->get_product();
                        if ($product) {
                            echo "Item {$item_id}: Product ID {$product->get_id()}, SKU '{$product->get_sku()}', Name '{$product->get_name()}', Qty {$item->get_quantity()}<br>";
                        } else {
                            echo "Item {$item_id}: No product found<br>";
                        }
                    }

                    echo '</div>';
                } else {
                    echo '<div style="color: red;">Order #' . $test_order_id . ' not found.</div>';
                }
            }
            ?>

            <h2>Recent API Logs</h2>
            <?php $this->display_recent_logs(); ?>
        </div>
        <?php
    }
    
    /**
     * Display recent API logs
     */
    private function display_recent_logs() {
        $logs = get_option('zagzoog_api_logs', array());
        $logs = array_reverse(array_slice($logs, -10)); // Show last 10 logs
        
        if (empty($logs)) {
            echo '<p>No logs available.</p>';
            return;
        }
        
        echo '<table class="wp-list-table widefat fixed striped">';
        echo '<thead><tr><th>Timestamp</th><th>Order ID</th><th>Response Code</th><th>Details</th></tr></thead>';
        echo '<tbody>';
        
        foreach ($logs as $log) {
            echo '<tr>';
            echo '<td>' . esc_html($log['timestamp']) . '</td>';
            echo '<td>' . esc_html($log['order_id']) . '</td>';
            echo '<td>' . esc_html($log['response_code']) . '</td>';
            echo '<td><details><summary>View</summary><pre>' . esc_html(json_encode($log, JSON_PRETTY_PRINT)) . '</pre></details></td>';
            echo '</tr>';
        }
        
        echo '</tbody></table>';
    }

    /**
     * Add manual send action to order actions
     */
    public function add_order_action($actions) {
        $actions['send_to_zagzoog'] = __('Send to Zagzoog API', 'zagzoog-integration');
        return $actions;
    }

    /**
     * Handle manual send to Zagzoog
     */
    public function manual_send_to_zagzoog($order) {
        $this->send_order_to_zagzoog($order->get_id());
    }

    /**
     * Add meta box to order edit page
     */
    public function add_order_meta_box() {
        add_meta_box(
            'zagzoog-order-info',
            'Zagzoog Integration',
            array($this, 'order_meta_box_content'),
            'shop_order',
            'side',
            'default'
        );
    }

    /**
     * Meta box content
     */
    public function order_meta_box_content($post) {
        $order = wc_get_order($post->ID);
        if (!$order) return;

        $api_sent = $order->get_meta('_zagzoog_api_sent');
        $api_response = $order->get_meta('_zagzoog_api_response');
        $zagzoog_status = $order->get_meta('_zagzoog_status');
        $zagzoog_order_id = $order->get_meta('_zagzoog_order_id');
        $last_update = $order->get_meta('_zagzoog_last_update');

        echo '<div class="zagzoog-meta-box">';
        echo '<p><strong>API Status:</strong> ' . ($api_sent ? 'Sent on ' . $api_sent : 'Not sent') . '</p>';

        if ($zagzoog_order_id) {
            echo '<p><strong>Zagzoog Order ID:</strong> ' . esc_html($zagzoog_order_id) . '</p>';
        }

        if ($zagzoog_status) {
            echo '<p><strong>Zagzoog Status:</strong> ' . esc_html($zagzoog_status) . '</p>';
        }

        if ($last_update) {
            echo '<p><strong>Last Update:</strong> ' . esc_html($last_update) . '</p>';
        }

        // Show products that were sent
        $products_data = array();
        foreach ($order->get_items() as $item) {
            $product = $item->get_product();
            if ($product) {
                $product_id = $product->get_sku() ?: $product->get_id();
                $products_data[] = $product_id . ' (Qty: ' . $item->get_quantity() . ')';
            }
        }

        if (!empty($products_data)) {
            echo '<p><strong>Products Sent:</strong><br>' . implode('<br>', $products_data) . '</p>';
        }

        if ($api_response) {
            echo '<details><summary>API Response</summary>';
            echo '<pre style="font-size: 11px; max-height: 200px; overflow-y: auto;">' . esc_html($api_response) . '</pre>';
            echo '</details>';
        }

        echo '<p><button type="button" class="button" onclick="zagzoogResendOrder(' . $order->get_id() . ')">Resend to Zagzoog</button></p>';
        echo '</div>';

        // Add JavaScript for resend functionality
        ?>
        <script>
        function zagzoogResendOrder(orderId) {
            if (confirm('Are you sure you want to resend this order to Zagzoog?')) {
                jQuery.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'zagzoog_resend_order',
                        order_id: orderId,
                        nonce: '<?php echo wp_create_nonce('zagzoog_resend'); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            alert('Order sent to Zagzoog successfully!');
                            location.reload();
                        } else {
                            alert('Error: ' + response.data);
                        }
                    },
                    error: function() {
                        alert('Error sending order to Zagzoog');
                    }
                });
            }
        }
        </script>
        <?php
    }

    /**
     * AJAX handler for manual order resending
     */
    public function ajax_resend_order() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'zagzoog_resend')) {
            wp_send_json_error('Invalid nonce');
            return;
        }

        // Check permissions
        if (!current_user_can('edit_shop_orders')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        $order_id = absint($_POST['order_id']);
        if (!$order_id) {
            wp_send_json_error('Invalid order ID');
            return;
        }

        // Send order to Zagzoog
        $this->send_order_to_zagzoog($order_id);

        wp_send_json_success('Order sent to Zagzoog successfully');
    }

    /**
     * Alternative AJAX webhook handler (doesn't require rewrite rules)
     */
    public function ajax_webhook_handler() {
        // Get input data
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);

        error_log('Zagzoog AJAX Webhook Received: ' . $input);

        if ($data && isset($data['orderid']) && isset($data['status'])) {
            $result = $this->process_webhook_data($data);
            if ($result) {
                wp_send_json_success('Webhook processed successfully');
            } else {
                wp_send_json_error('Failed to process webhook');
            }
        } else {
            wp_send_json_error('Invalid webhook data');
        }
    }
}

// Initialize the plugin
new ZagzoogWooCommerceIntegration();

// Activation hook
register_activation_hook(__FILE__, function() {
    flush_rewrite_rules();
});

// Deactivation hook
register_deactivation_hook(__FILE__, function() {
    flush_rewrite_rules();
});
